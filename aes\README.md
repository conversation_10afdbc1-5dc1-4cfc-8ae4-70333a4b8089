# 图片处理和加密系统

这是一个自动化的图片处理和加密系统，能够监控指定目录中的图片文件，自动生成缩略图、转换格式并进行加密处理。

## 功能特性

- **自动监控**: 每2秒检测一次temp目录中的新图片文件
- **多格式支持**: 支持JPG、PNG、WebP、AVIF、HEIC格式的图片
- **缩略图生成**: 自动生成JPG格式的缩略图（最大200x200像素）
- **格式转换**: 将原图转换为80%质量的AVIF格式
- **安全加密**: 使用AES256-GCM算法加密图片文件
- **数据库存储**: 将加密信息安全存储在SQLite数据库中
- **跨平台兼容**: 同时支持Linux和Windows系统

## 系统要求

- Python 3.8+
- 支持的操作系统：Linux、Windows

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 确保项目目录下存在`temp`目录（程序会自动创建）
2. 运行主程序：
   ```bash
   python main.py
   ```
3. 将需要处理的图片文件放入`temp`目录
4. 程序会自动检测并处理这些图片文件

## 处理流程

1. **检测图片**: 每2秒扫描temp目录中的图片文件
2. **生成缩略图**: 创建JPG格式的缩略图（文件名_tagimg.jpg）
3. **格式转换**: 将原图转换为80%质量的AVIF格式
4. **生成密码**: 为每个文件生成唯一的16位安全密码
5. **文件加密**: 使用AES256-GCM算法加密AVIF图片和缩略图
6. **覆盖原文件**: 用加密后的文件覆盖原始文件
7. **数据库记录**: 将加密信息存储到SQLite数据库

## 数据库结构

程序会自动创建`image_encryption.db`数据库文件，包含以下字段：
- `file_path`: 文件相对路径
- `password`: 加密密码
- `iv`: 初始化向量
- `sha1_hash`: 原文件SHA1哈希值
- `created_at`: 创建时间

## 安全特性

- 每个文件使用唯一的16位随机密码
- 采用AES256-GCM加密算法，提供认证加密
- 密码和IV安全存储在本地数据库中
- 原文件的SHA1哈希值用于完整性验证

## 注意事项

- 程序会覆盖原始图片文件，请确保重要文件已备份
- 加密后的文件无法直接查看，需要解密程序才能恢复
- 数据库文件包含解密所需的关键信息，请妥善保管
- 按Ctrl+C可以安全停止程序

## 性能优化

- 使用高性能的cryptography库进行AES加密
- 采用PIL库进行图片处理，性能优异
- 数据库操作经过优化，支持大量文件处理
- 内存使用经过优化，适合长时间运行
