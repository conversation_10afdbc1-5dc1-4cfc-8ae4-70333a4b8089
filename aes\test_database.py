#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库逻辑优化
"""

from pathlib import Path
import sqlite3
import shutil

def test_database_scenarios():
    """测试各种数据库场景"""
    print("=" * 60)
    print("数据库逻辑测试")
    print("=" * 60)
    
    db_file = Path('image_encryption.db')
    
    # 场景1: 数据库不存在
    print("\n场景1: 数据库文件不存在")
    if db_file.exists():
        db_file.unlink()
        print("✓ 删除现有数据库文件")
    
    from main import ImageProcessor
    processor = ImageProcessor()
    print("✓ ImageProcessor初始化完成")
    
    # 检查数据库是否被创建
    if db_file.exists():
        print("✓ 数据库文件已自动创建")
        
        # 检查表结构
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"✓ 数据库表: {tables}")
        
        cursor.execute("PRAGMA table_info(encrypted_images)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"✓ 表结构: {columns}")
        
        # 检查索引
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
        indexes = [row[0] for row in cursor.fetchall()]
        print(f"✓ 索引: {indexes}")
        
        conn.close()
    else:
        print("✗ 数据库文件未创建")
    
    # 场景2: 数据库存在但表结构不完整
    print("\n场景2: 数据库存在但表结构损坏")
    if db_file.exists():
        db_file.unlink()
    
    # 创建一个损坏的数据库
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    cursor.execute("CREATE TABLE encrypted_images (id INTEGER PRIMARY KEY)")  # 缺少必要列
    conn.commit()
    conn.close()
    print("✓ 创建损坏的数据库")
    
    # 重新初始化
    processor2 = ImageProcessor()
    print("✓ ImageProcessor重新初始化")
    
    # 检查是否修复
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(encrypted_images)")
    columns = [row[1] for row in cursor.fetchall()]
    conn.close()
    
    if len(columns) >= 6:  # 应该有6个列
        print("✓ 数据库结构已修复")
    else:
        print("✗ 数据库结构未修复")
    
    # 场景3: 数据库存在且正常
    print("\n场景3: 数据库存在且结构正常")
    processor3 = ImageProcessor()
    print("✓ 正常数据库初始化完成")

def test_database_operations():
    """测试数据库操作"""
    print("\n" + "=" * 60)
    print("数据库操作测试")
    print("=" * 60)
    
    from main import ImageProcessor
    processor = ImageProcessor()
    
    # 测试保存记录
    print("\n测试保存记录...")
    test_file = Path('file/test_image.jpg')
    test_password = "test_password_123"
    test_iv = b'\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c'
    test_sha1 = "abcdef1234567890abcdef1234567890abcdef12"
    
    processor.save_to_database(test_file, test_password, test_iv, test_sha1)
    
    # 验证记录是否保存
    conn = sqlite3.connect('image_encryption.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM encrypted_images WHERE file_path = ?', ('file/test_image.jpg',))
    result = cursor.fetchone()
    conn.close()
    
    if result:
        print("✓ 记录保存成功")
        print(f"  - 文件路径: {result[1]}")
        print(f"  - 密码: {result[2]}")
        print(f"  - IV: {result[3]}")
        print(f"  - SHA1: {result[4]}")
    else:
        print("✗ 记录保存失败")
    
    # 测试更新记录
    print("\n测试更新记录...")
    new_password = "updated_password_456"
    processor.save_to_database(test_file, new_password, test_iv, test_sha1)
    
    # 验证记录是否更新
    conn = sqlite3.connect('image_encryption.db')
    cursor = conn.cursor()
    cursor.execute('SELECT password FROM encrypted_images WHERE file_path = ?', ('file/test_image.jpg',))
    result = cursor.fetchone()
    conn.close()
    
    if result and result[0] == new_password:
        print("✓ 记录更新成功")
    else:
        print("✗ 记录更新失败")

def test_decrypt_tool():
    """测试解密工具的数据库访问"""
    print("\n" + "=" * 60)
    print("解密工具数据库测试")
    print("=" * 60)
    
    try:
        from decrypt_tool import ImageDecryptor
        decryptor = ImageDecryptor()
        print("✓ 解密工具初始化成功")
        
        # 测试获取加密信息
        info = decryptor.get_encryption_info('file/test_image.jpg')
        if info:
            password, iv, sha1 = info
            print("✓ 成功获取加密信息")
            print(f"  - 密码长度: {len(password)}")
            print(f"  - IV长度: {len(iv)}")
            print(f"  - SHA1: {sha1}")
        else:
            print("✗ 获取加密信息失败")
            
    except Exception as e:
        print(f"✗ 解密工具测试失败: {e}")

def main():
    """主函数"""
    print("数据库逻辑优化测试")
    
    # 测试数据库场景
    test_database_scenarios()
    
    # 测试数据库操作
    test_database_operations()
    
    # 测试解密工具
    test_decrypt_tool()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
