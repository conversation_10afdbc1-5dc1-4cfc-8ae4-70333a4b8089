#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库队列管理器
统一处理所有数据库写入操作，避免并发冲突
"""

import sqlite3
import threading
import time
import queue
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

DB_FILE = 'image_encryption.db'

@dataclass
class DatabaseOperation:
    """数据库操作数据类"""
    operation_type: str  # 'insert', 'update', 'delete'
    table_name: str
    data: Dict[str, Any]
    condition: Optional[Dict[str, Any]] = None

class DatabaseQueue:
    """数据库队列管理器"""

    def __init__(self):
        # 使用有界队列防止内存溢出
        self.queue = queue.Queue(maxsize=1000)  # 最大1000个待处理操作
        self.running = False
        self.worker_thread = None
        self.monitor_thread = None
        self.lock = threading.Lock()

        # 并发统计
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'queue_full_warnings': 0,
            'database_lock_retries': 0
        }
        self.stats_lock = threading.Lock()
        
    def start(self):
        """启动数据库队列处理器"""
        with self.lock:
            if not self.running:
                self.running = True
                self.ensure_database_exists()

                # 启动工作线程
                self.worker_thread = threading.Thread(target=self._worker, daemon=True)
                self.worker_thread.start()

                # 启动数据库监控线程
                self.monitor_thread = threading.Thread(target=self._database_monitor, daemon=True)
                self.monitor_thread.start()

                print("[OK] 数据库队列处理器已启动")
                print("[OK] 数据库监控线程已启动")
    
    def stop(self):
        """停止数据库队列处理器"""
        with self.lock:
            if self.running:
                self.running = False
                # 添加停止信号到队列
                self.queue.put(None)

                # 等待工作线程停止
                if self.worker_thread:
                    self.worker_thread.join(timeout=5)

                # 等待监控线程停止
                if self.monitor_thread:
                    self.monitor_thread.join(timeout=5)

                print("[OK] 数据库队列处理器已停止")
                print("[OK] 数据库监控线程已停止")
    
    def ensure_database_exists(self):
        """确保数据库文件存在并具有正确的表结构"""
        db_path = Path(DB_FILE)
        
        if not db_path.exists():
            print(f"[OK] 创建新的数据库文件: {DB_FILE}")
            self._create_database()
        else:
            print(f"[OK] 检测到现有数据库文件: {DB_FILE}")
            self._verify_database_structure()
    
    def _create_database(self):
        """创建新的数据库和表结构"""
        try:
            conn = sqlite3.connect(DB_FILE, timeout=30)
            cursor = conn.cursor()
            
            # 创建加密图片表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS encrypted_images (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    iv TEXT NOT NULL,
                    sha1_hash TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    txt TEXT DEFAULT '',
                    capture_date TEXT DEFAULT NULL,
                    file_size_mb REAL DEFAULT NULL,
                    image_width INTEGER DEFAULT NULL,
                    image_height INTEGER DEFAULT NULL,
                    gps_latitude REAL DEFAULT NULL,
                    gps_longitude REAL DEFAULT NULL
                )
            ''')

            # 创建视频表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS encrypted_videos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sha1_hash TEXT UNIQUE NOT NULL,
                    original_filename TEXT NOT NULL,
                    m3u8_path TEXT NOT NULL,
                    encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    password TEXT NOT NULL,
                    iv TEXT NOT NULL,
                    file_created_date TEXT DEFAULT NULL,
                    file_size_mb REAL NOT NULL,
                    tag TEXT DEFAULT '',
                    txt TEXT DEFAULT ''
                )
            ''')

            # 创建文件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS encrypted_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sha1_hash TEXT UNIQUE NOT NULL,
                    original_filename TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    password TEXT NOT NULL,
                    iv TEXT NOT NULL,
                    file_created_date TEXT DEFAULT NULL,
                    file_size_mb REAL NOT NULL,
                    tag TEXT DEFAULT '',
                    txt TEXT DEFAULT ''
                )
            ''')

            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_file_path ON encrypted_images(file_path)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_sha1_hash ON encrypted_images(sha1_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_created_at ON encrypted_images(created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_sha1_hash ON encrypted_videos(sha1_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_encryption_date ON encrypted_videos(encryption_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_sha1_hash ON encrypted_files(sha1_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_encryption_date ON encrypted_files(encryption_date)')
            
            conn.commit()
            conn.close()
            print(f"[OK] 数据库创建成功: {DB_FILE}")

        except Exception as e:
            print(f"[ERROR] 数据库创建失败: {e}")
            raise
    
    def _verify_database_structure(self):
        """验证现有数据库结构"""
        try:
            conn = sqlite3.connect(DB_FILE, timeout=30)
            cursor = conn.cursor()

            # 检查所有必需的表是否存在
            required_tables = ['encrypted_images', 'encrypted_videos', 'encrypted_files']
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]

            missing_tables = [table for table in required_tables if table not in existing_tables]
            if missing_tables:
                print(f"[WARN] 缺少数据库表: {missing_tables}，正在创建...")

                # 创建缺失的表
                if 'encrypted_videos' in missing_tables:
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS encrypted_videos (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            sha1_hash TEXT UNIQUE NOT NULL,
                            original_filename TEXT NOT NULL,
                            m3u8_path TEXT NOT NULL,
                            encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            password TEXT NOT NULL,
                            iv TEXT NOT NULL,
                            file_created_date TEXT DEFAULT NULL,
                            file_size_mb REAL NOT NULL,
                            tag TEXT DEFAULT '',
                            txt TEXT DEFAULT ''
                        )
                    ''')
                    cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_sha1_hash ON encrypted_videos(sha1_hash)')
                    cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_encryption_date ON encrypted_videos(encryption_date)')
                    print("[OK] 创建 encrypted_videos 表")

                if 'encrypted_files' in missing_tables:
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS encrypted_files (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            sha1_hash TEXT UNIQUE NOT NULL,
                            original_filename TEXT NOT NULL,
                            file_path TEXT NOT NULL,
                            encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            password TEXT NOT NULL,
                            iv TEXT NOT NULL,
                            file_created_date TEXT DEFAULT NULL,
                            file_size_mb REAL NOT NULL,
                            tag TEXT DEFAULT '',
                            txt TEXT DEFAULT ''
                        )
                    ''')
                    cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_sha1_hash ON encrypted_files(sha1_hash)')
                    cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_encryption_date ON encrypted_files(encryption_date)')
                    print("[OK] 创建 encrypted_files 表")

                conn.commit()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(encrypted_images)")
            columns = [row[1] for row in cursor.fetchall()]
            required_columns = ['id', 'file_path', 'password', 'iv', 'sha1_hash', 'created_at', 'txt',
                              'capture_date', 'file_size_mb', 'image_width', 'image_height',
                              'gps_latitude', 'gps_longitude']

            missing_columns = [col for col in required_columns if col not in columns]
            if missing_columns:
                print(f"[WARN] 数据库结构不完整，缺少列: {missing_columns}")
                # 尝试添加缺失的列
                for col in missing_columns:
                    if col == 'txt':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN txt TEXT DEFAULT ''")
                    elif col == 'capture_date':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN capture_date TEXT DEFAULT NULL")
                    elif col == 'file_size_mb':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN file_size_mb REAL DEFAULT NULL")
                    elif col == 'image_width':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN image_width INTEGER DEFAULT NULL")
                    elif col == 'image_height':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN image_height INTEGER DEFAULT NULL")
                    elif col == 'gps_latitude':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN gps_latitude REAL DEFAULT NULL")
                    elif col == 'gps_longitude':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN gps_longitude REAL DEFAULT NULL")
                    print(f"[OK] 添加缺失列: {col}")

                conn.commit()

            # 检查记录数量
            cursor.execute("SELECT COUNT(*) FROM encrypted_images")
            count = cursor.fetchone()[0]
            print(f"[OK] 数据库验证通过，现有记录: {count} 条")
            
            conn.close()
            
        except Exception as e:
            print(f"[WARN] 数据库验证失败: {e}")
            print("重新创建数据库...")
            try:
                Path(DB_FILE).unlink()
            except:
                pass
            self._create_database()
    
    def add_operation(self, operation: DatabaseOperation):
        """添加数据库操作到队列"""
        if not self.running:
            print("[WARN] 数据库队列未启动，无法添加操作")
            return False

        try:
            # 使用非阻塞方式添加，避免长时间等待
            self.queue.put(operation, timeout=5)  # 最多等待5秒

            with self.stats_lock:
                self.stats['total_operations'] += 1

            return True

        except queue.Full:
            with self.stats_lock:
                self.stats['queue_full_warnings'] += 1
            print(f"[WARN] 数据库队列已满 (当前大小: {self.queue.qsize()})，操作被丢弃")
            return False
    
    def save_encrypted_image(self, file_path: str, password: str, iv: str, sha1_hash: str, txt: str = "",
                           capture_date: str = None, file_size_mb: float = None,
                           image_width: int = None, image_height: int = None,
                           gps_latitude: float = None, gps_longitude: float = None):
        """保存加密图片信息到队列"""
        operation = DatabaseOperation(
            operation_type='insert_or_update',
            table_name='encrypted_images',
            data={
                'file_path': file_path,
                'password': password,
                'iv': iv,
                'sha1_hash': sha1_hash,
                'txt': txt,
                'capture_date': capture_date,
                'file_size_mb': file_size_mb,
                'image_width': image_width,
                'image_height': image_height,
                'gps_latitude': gps_latitude,
                'gps_longitude': gps_longitude
            }
        )
        self.add_operation(operation)

    def save_encrypted_video(self, sha1_hash: str, original_filename: str, m3u8_path: str,
                           password: str, iv: str, file_created_date: str = None,
                           file_size_mb: float = 0.0, tag: str = "", txt: str = ""):
        """保存加密视频信息到队列"""
        operation = DatabaseOperation(
            operation_type='insert_or_update',
            table_name='encrypted_videos',
            data={
                'sha1_hash': sha1_hash,
                'original_filename': original_filename,
                'm3u8_path': m3u8_path,
                'password': password,
                'iv': iv,
                'file_created_date': file_created_date,
                'file_size_mb': file_size_mb,
                'tag': tag,
                'txt': txt
            }
        )
        self.add_operation(operation)

    def save_encrypted_file(self, sha1_hash: str, original_filename: str, file_path: str,
                          password: str, iv: str, file_created_date: str = None,
                          file_size_mb: float = 0.0, tag: str = "", txt: str = ""):
        """保存加密文件信息到队列"""
        operation = DatabaseOperation(
            operation_type='insert_or_update',
            table_name='encrypted_files',
            data={
                'sha1_hash': sha1_hash,
                'original_filename': original_filename,
                'file_path': file_path,
                'password': password,
                'iv': iv,
                'file_created_date': file_created_date,
                'file_size_mb': file_size_mb,
                'tag': tag,
                'txt': txt
            }
        )
        self.add_operation(operation)

    def get_queue_stats(self):
        """获取队列统计信息"""
        with self.stats_lock:
            stats = self.stats.copy()

        stats['queue_size'] = self.queue.qsize()
        stats['is_running'] = self.running
        return stats

    def print_queue_stats(self):
        """打印队列统计信息"""
        stats = self.get_queue_stats()
        print(f"[INFO] 数据库队列统计:")
        print(f"  - 队列大小: {stats['queue_size']}")
        print(f"  - 总操作数: {stats['total_operations']}")
        print(f"  - 成功操作: {stats['successful_operations']}")
        print(f"  - 失败操作: {stats['failed_operations']}")
        print(f"  - 队列满警告: {stats['queue_full_warnings']}")
        print(f"  - 数据库锁重试: {stats['database_lock_retries']}")
        print(f"  - 运行状态: {'运行中' if stats['is_running'] else '已停止'}")

    def is_file_processed(self, file_name: str, sha1_hash: str = None) -> bool:
        """检查文件是否已经处理过（同步查询）"""
        try:
            # 确保数据库存在
            if not Path(DB_FILE).exists():
                return False

            conn = sqlite3.connect(DB_FILE, timeout=10)
            cursor = conn.cursor()

            # 方法1: 检查是否有相同文件名的记录
            cursor.execute('SELECT id FROM encrypted_images WHERE file_path LIKE ?', (f'%{file_name}',))
            result = cursor.fetchone()

            if result:
                conn.close()
                return True

            # 方法2: 如果提供了SHA1，检查SHA1哈希
            if sha1_hash:
                cursor.execute('SELECT id FROM encrypted_images WHERE sha1_hash = ?', (sha1_hash,))
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return True

            conn.close()
            return False

        except Exception as e:
            print(f"[WARN] 检查文件处理状态失败: {e}")
            return False
    
    def _worker(self):
        """数据库队列工作线程"""
        print("[OK] 数据库队列工作线程已启动")

        # 批量处理缓冲区
        batch_operations = []
        batch_size = 10  # 每批处理10个操作
        last_batch_time = time.time()
        batch_timeout = 2.0  # 2秒超时强制处理批次

        while self.running:
            try:
                # 从队列获取操作，超时1秒
                operation = self.queue.get(timeout=1)

                # 检查停止信号
                if operation is None:
                    # 处理剩余的批次操作
                    if batch_operations:
                        self._execute_batch_operations(batch_operations)
                        batch_operations.clear()
                    break

                # 添加到批次缓冲区
                batch_operations.append(operation)

                # 检查是否需要处理批次
                current_time = time.time()
                should_process_batch = (
                    len(batch_operations) >= batch_size or  # 达到批次大小
                    (current_time - last_batch_time) >= batch_timeout  # 超时
                )

                if should_process_batch:
                    self._execute_batch_operations(batch_operations)
                    batch_operations.clear()
                    last_batch_time = current_time

                # 标记任务完成
                self.queue.task_done()

            except queue.Empty:
                # 队列为空，检查是否有待处理的批次
                if batch_operations and (time.time() - last_batch_time) >= batch_timeout:
                    self._execute_batch_operations(batch_operations)
                    batch_operations.clear()
                    last_batch_time = time.time()
                continue
            except Exception as e:
                print(f"[ERROR] 数据库队列工作线程错误: {e}")
                with self.stats_lock:
                    self.stats['failed_operations'] += len(batch_operations) if batch_operations else 1
                batch_operations.clear()  # 清空批次避免重复处理
                time.sleep(0.1)  # 短暂延迟后继续

        print("[OK] 数据库队列工作线程已停止")

    def _execute_batch_operations(self, operations):
        """批量执行数据库操作"""
        if not operations:
            return

        # 按表名分组操作
        operations_by_table = {}
        for op in operations:
            table_name = op.table_name
            if table_name not in operations_by_table:
                operations_by_table[table_name] = []
            operations_by_table[table_name].append(op)

        # 为每个表批量执行操作
        for table_name, table_operations in operations_by_table.items():
            try:
                self._execute_table_batch(table_name, table_operations)
                with self.stats_lock:
                    self.stats['successful_operations'] += len(table_operations)
            except Exception as e:
                print(f"[ERROR] 批量执行 {table_name} 表操作失败: {e}")
                with self.stats_lock:
                    self.stats['failed_operations'] += len(table_operations)
                # 回退到单个操作执行
                for op in table_operations:
                    try:
                        self._execute_operation(op)
                        with self.stats_lock:
                            self.stats['successful_operations'] += 1
                    except Exception as single_error:
                        print(f"[ERROR] 单个操作执行失败: {single_error}")
                        with self.stats_lock:
                            self.stats['failed_operations'] += 1

    def _execute_table_batch(self, table_name, operations):
        """为特定表批量执行操作"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(DB_FILE, timeout=30)
                cursor = conn.cursor()

                # 检查数据库和表是否存在，如果不存在则创建
                self._ensure_database_and_table_exist(cursor, table_name)

                # 开始事务
                cursor.execute("BEGIN TRANSACTION")

                # 批量执行操作
                for operation in operations:
                    if operation.operation_type == 'insert_or_update':
                        self._handle_insert_or_update(cursor, operation)
                    elif operation.operation_type == 'insert':
                        self._handle_insert(cursor, operation)
                    elif operation.operation_type == 'update':
                        self._handle_update(cursor, operation)
                    elif operation.operation_type == 'delete':
                        self._handle_delete(cursor, operation)

                # 提交事务
                conn.commit()
                conn.close()

                # 成功执行，退出重试循环
                break

            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    with self.stats_lock:
                        self.stats['database_lock_retries'] += 1
                    print(f"[WARN] 数据库被锁定，批量操作重试 {attempt + 1}/{max_retries}")
                    time.sleep(0.2 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    print(f"[ERROR] 批量数据库操作失败: {e}")
                    raise
            except Exception as e:
                print(f"[ERROR] 批量数据库操作异常: {e}")
                raise

    def _ensure_database_and_table_exist(self, cursor, table_name):
        """确保数据库和指定表存在"""
        try:
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if cursor.fetchone():
                return  # 表已存在

            print(f"[WARN] 表 {table_name} 不存在，正在创建...")

            # 根据表名创建相应的表
            if table_name == 'encrypted_images':
                self._create_images_table(cursor)
            elif table_name == 'encrypted_videos':
                self._create_videos_table(cursor)
            elif table_name == 'encrypted_files':
                self._create_files_table(cursor)
            else:
                raise ValueError(f"未知的表名: {table_name}")

            print(f"[OK] 表 {table_name} 创建成功")

        except Exception as e:
            print(f"[ERROR] 确保表存在失败: {e}")
            raise

    def _create_images_table(self, cursor):
        """创建图片表"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS encrypted_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                iv TEXT NOT NULL,
                sha1_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                txt TEXT DEFAULT '',
                capture_date TEXT DEFAULT NULL,
                file_size_mb REAL DEFAULT NULL,
                image_width INTEGER DEFAULT NULL,
                image_height INTEGER DEFAULT NULL,
                gps_latitude REAL DEFAULT NULL,
                gps_longitude REAL DEFAULT NULL
            )
        ''')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_file_path ON encrypted_images(file_path)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_sha1_hash ON encrypted_images(sha1_hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_created_at ON encrypted_images(created_at)')

    def _create_videos_table(self, cursor):
        """创建视频表"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS encrypted_videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sha1_hash TEXT UNIQUE NOT NULL,
                original_filename TEXT NOT NULL,
                m3u8_path TEXT NOT NULL,
                encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                password TEXT NOT NULL,
                iv TEXT NOT NULL,
                file_created_date TEXT DEFAULT NULL,
                file_size_mb REAL NOT NULL,
                tag TEXT DEFAULT '',
                txt TEXT DEFAULT ''
            )
        ''')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_sha1_hash ON encrypted_videos(sha1_hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_encryption_date ON encrypted_videos(encryption_date)')

    def _create_files_table(self, cursor):
        """创建文件表"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS encrypted_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sha1_hash TEXT UNIQUE NOT NULL,
                original_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                password TEXT NOT NULL,
                iv TEXT NOT NULL,
                file_created_date TEXT DEFAULT NULL,
                file_size_mb REAL NOT NULL,
                tag TEXT DEFAULT '',
                txt TEXT DEFAULT ''
            )
        ''')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_sha1_hash ON encrypted_files(sha1_hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_encryption_date ON encrypted_files(encryption_date)')

    def _database_monitor(self):
        """数据库监控线程，每30秒检查数据库是否存在"""
        print("[OK] 数据库监控线程已启动")

        while self.running:
            try:
                # 检查数据库是否存在
                db_path = Path(DB_FILE)
                if not db_path.exists():
                    print("[WARN] 检测到数据库文件不存在，自动创建...")
                    self._create_database_safe()
                else:
                    # 验证数据库结构
                    self._verify_database_structure_safe()

                # 等待30秒或直到停止信号
                for _ in range(300):  # 30秒 = 300 * 0.1秒
                    if not self.running:
                        break
                    time.sleep(0.1)

            except Exception as e:
                print(f"[WARN] 数据库监控线程错误: {e}")
                # 出错时等待5秒后继续
                for _ in range(50):  # 5秒 = 50 * 0.1秒
                    if not self.running:
                        break
                    time.sleep(0.1)

        print("[OK] 数据库监控线程已停止")

    def _create_database_safe(self):
        """安全地创建数据库（带重试机制）"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(DB_FILE, timeout=30)
                cursor = conn.cursor()

                # 创建加密图片表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS encrypted_images (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        file_path TEXT UNIQUE NOT NULL,
                        password TEXT NOT NULL,
                        iv TEXT NOT NULL,
                        sha1_hash TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        txt TEXT DEFAULT '',
                        capture_date TEXT DEFAULT NULL,
                        file_size_mb REAL DEFAULT NULL,
                        image_width INTEGER DEFAULT NULL,
                        image_height INTEGER DEFAULT NULL,
                        gps_latitude REAL DEFAULT NULL,
                        gps_longitude REAL DEFAULT NULL
                    )
                ''')

                # 创建视频表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS encrypted_videos (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sha1_hash TEXT UNIQUE NOT NULL,
                        original_filename TEXT NOT NULL,
                        m3u8_path TEXT NOT NULL,
                        encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        password TEXT NOT NULL,
                        iv TEXT NOT NULL,
                        file_created_date TEXT DEFAULT NULL,
                        file_size_mb REAL NOT NULL,
                        tag TEXT DEFAULT '',
                        txt TEXT DEFAULT ''
                    )
                ''')

                # 创建文件表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS encrypted_files (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sha1_hash TEXT UNIQUE NOT NULL,
                        original_filename TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        password TEXT NOT NULL,
                        iv TEXT NOT NULL,
                        file_created_date TEXT DEFAULT NULL,
                        file_size_mb REAL NOT NULL,
                        tag TEXT DEFAULT '',
                        txt TEXT DEFAULT ''
                    )
                ''')

                # 创建索引以提高查询性能
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_file_path ON encrypted_images(file_path)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_sha1_hash ON encrypted_images(sha1_hash)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_created_at ON encrypted_images(created_at)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_sha1_hash ON encrypted_videos(sha1_hash)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_encryption_date ON encrypted_videos(encryption_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_sha1_hash ON encrypted_files(sha1_hash)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_encryption_date ON encrypted_files(encryption_date)')

                conn.commit()
                conn.close()
                print(f"[OK] 数据库自动创建成功: {DB_FILE}")
                return True

            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    print(f"[WARN] 数据库被锁定，重试 {attempt + 1}/{max_retries}")
                    time.sleep(1 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    print(f"[ERROR] 数据库创建失败: {e}")
                    return False
            except Exception as e:
                print(f"[ERROR] 数据库创建异常: {e}")
                return False

        return False

    def _verify_database_structure_safe(self):
        """安全地验证数据库结构"""
        try:
            conn = sqlite3.connect(DB_FILE, timeout=10)
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='encrypted_images'")
            if not cursor.fetchone():
                print("[WARN] 数据库表不存在，重新创建...")
                conn.close()
                Path(DB_FILE).unlink()
                self._create_database_safe()
                return

            # 检查表结构
            cursor.execute("PRAGMA table_info(encrypted_images)")
            columns = [row[1] for row in cursor.fetchall()]
            required_columns = ['id', 'file_path', 'password', 'iv', 'sha1_hash', 'created_at', 'txt',
                              'capture_date', 'file_size_mb', 'image_width', 'image_height',
                              'gps_latitude', 'gps_longitude']

            missing_columns = [col for col in required_columns if col not in columns]
            if missing_columns:
                print(f"[WARN] 数据库结构不完整，缺少列: {missing_columns}")
                # 尝试添加缺失的列
                for col in missing_columns:
                    if col == 'txt':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN txt TEXT DEFAULT ''")
                    elif col == 'capture_date':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN capture_date TEXT DEFAULT NULL")
                    elif col == 'file_size_mb':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN file_size_mb REAL DEFAULT NULL")
                    elif col == 'image_width':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN image_width INTEGER DEFAULT NULL")
                    elif col == 'image_height':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN image_height INTEGER DEFAULT NULL")
                    elif col == 'gps_latitude':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN gps_latitude REAL DEFAULT NULL")
                    elif col == 'gps_longitude':
                        cursor.execute("ALTER TABLE encrypted_images ADD COLUMN gps_longitude REAL DEFAULT NULL")
                    print(f"[OK] 添加缺失列: {col}")

                conn.commit()

            conn.close()

        except Exception as e:
            print(f"[WARN] 数据库结构验证失败: {e}")
            # 如果验证失败，尝试重新创建
            try:
                Path(DB_FILE).unlink()
                self._create_database_safe()
            except:
                pass
    
    def _execute_operation(self, operation: DatabaseOperation):
        """执行单个数据库操作"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(DB_FILE, timeout=30)
                cursor = conn.cursor()

                # 确保数据库和表存在
                self._ensure_database_and_table_exist(cursor, operation.table_name)

                if operation.operation_type == 'insert_or_update':
                    self._handle_insert_or_update(cursor, operation)
                elif operation.operation_type == 'insert':
                    self._handle_insert(cursor, operation)
                elif operation.operation_type == 'update':
                    self._handle_update(cursor, operation)
                elif operation.operation_type == 'delete':
                    self._handle_delete(cursor, operation)
                
                conn.commit()
                conn.close()
                
                # 成功执行，退出重试循环
                break
                
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                    with self.stats_lock:
                        self.stats['database_lock_retries'] += 1
                    print(f"[WARN] 数据库被锁定，重试 {attempt + 1}/{max_retries}")
                    time.sleep(0.1 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    print(f"[ERROR] 数据库操作失败: {e}")
                    with self.stats_lock:
                        self.stats['failed_operations'] += 1
                    break
            except Exception as e:
                print(f"[ERROR] 数据库操作异常: {e}")
                with self.stats_lock:
                    self.stats['failed_operations'] += 1
                break
    
    def _handle_insert_or_update(self, cursor, operation):
        """处理插入或更新操作"""
        data = operation.data
        table_name = operation.table_name

        if table_name == 'encrypted_images':
            self._handle_images_insert_or_update(cursor, data)
        elif table_name == 'encrypted_videos':
            self._handle_videos_insert_or_update(cursor, data)
        elif table_name == 'encrypted_files':
            self._handle_files_insert_or_update(cursor, data)
        else:
            print(f"[ERROR] 未知的表名: {table_name}")

    def _handle_images_insert_or_update(self, cursor, data):
        """处理图片表的插入或更新操作"""
        # 检查记录是否已存在
        cursor.execute('SELECT id FROM encrypted_images WHERE file_path = ?', (data['file_path'],))
        existing_record = cursor.fetchone()

        if existing_record:
            # 更新现有记录
            cursor.execute('''
                UPDATE encrypted_images
                SET password = ?, iv = ?, sha1_hash = ?, txt = ?,
                    capture_date = ?, file_size_mb = ?, image_width = ?, image_height = ?,
                    gps_latitude = ?, gps_longitude = ?, created_at = CURRENT_TIMESTAMP
                WHERE file_path = ?
            ''', (data['password'], data['iv'], data['sha1_hash'], data.get('txt', ''),
                  data.get('capture_date'), data.get('file_size_mb'), data.get('image_width'),
                  data.get('image_height'), data.get('gps_latitude'), data.get('gps_longitude'),
                  data['file_path']))
            print(f"[OK] 更新图片记录: {data['file_path']}")
        else:
            # 插入新记录
            cursor.execute('''
                INSERT INTO encrypted_images
                (file_path, password, iv, sha1_hash, txt, capture_date, file_size_mb,
                 image_width, image_height, gps_latitude, gps_longitude)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (data['file_path'], data['password'], data['iv'], data['sha1_hash'], data.get('txt', ''),
                  data.get('capture_date'), data.get('file_size_mb'), data.get('image_width'),
                  data.get('image_height'), data.get('gps_latitude'), data.get('gps_longitude')))
            print(f"[OK] 新增图片记录: {data['file_path']}")

    def _handle_videos_insert_or_update(self, cursor, data):
        """处理视频表的插入或更新操作"""
        # 检查记录是否已存在
        cursor.execute('SELECT id FROM encrypted_videos WHERE sha1_hash = ?', (data['sha1_hash'],))
        existing_record = cursor.fetchone()

        if existing_record:
            # 更新现有记录
            cursor.execute('''
                UPDATE encrypted_videos
                SET original_filename = ?, m3u8_path = ?, password = ?, iv = ?,
                    file_created_date = ?, file_size_mb = ?, tag = ?, txt = ?,
                    encryption_date = CURRENT_TIMESTAMP
                WHERE sha1_hash = ?
            ''', (data['original_filename'], data['m3u8_path'], data['password'], data['iv'],
                  data.get('file_created_date'), data.get('file_size_mb', 0.0),
                  data.get('tag', ''), data.get('txt', ''), data['sha1_hash']))
            print(f"[OK] 更新视频记录: {data['original_filename']}")
        else:
            # 插入新记录
            cursor.execute('''
                INSERT INTO encrypted_videos
                (sha1_hash, original_filename, m3u8_path, password, iv,
                 file_created_date, file_size_mb, tag, txt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (data['sha1_hash'], data['original_filename'], data['m3u8_path'],
                  data['password'], data['iv'], data.get('file_created_date'),
                  data.get('file_size_mb', 0.0), data.get('tag', ''), data.get('txt', '')))
            print(f"[OK] 新增视频记录: {data['original_filename']}")

    def _handle_files_insert_or_update(self, cursor, data):
        """处理文件表的插入或更新操作"""
        # 检查记录是否已存在
        cursor.execute('SELECT id FROM encrypted_files WHERE sha1_hash = ?', (data['sha1_hash'],))
        existing_record = cursor.fetchone()

        if existing_record:
            # 更新现有记录
            cursor.execute('''
                UPDATE encrypted_files
                SET original_filename = ?, file_path = ?, password = ?, iv = ?,
                    file_created_date = ?, file_size_mb = ?, tag = ?, txt = ?,
                    encryption_date = CURRENT_TIMESTAMP
                WHERE sha1_hash = ?
            ''', (data['original_filename'], data['file_path'], data['password'], data['iv'],
                  data.get('file_created_date'), data.get('file_size_mb', 0.0),
                  data.get('tag', ''), data.get('txt', ''), data['sha1_hash']))
            print(f"[OK] 更新文件记录: {data['original_filename']}")
        else:
            # 插入新记录
            cursor.execute('''
                INSERT INTO encrypted_files
                (sha1_hash, original_filename, file_path, password, iv,
                 file_created_date, file_size_mb, tag, txt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (data['sha1_hash'], data['original_filename'], data['file_path'],
                  data['password'], data['iv'], data.get('file_created_date'),
                  data.get('file_size_mb', 0.0), data.get('tag', ''), data.get('txt', '')))
            print(f"[OK] 新增文件记录: {data['original_filename']}")
    
    def _handle_insert(self, cursor, operation):
        """处理插入操作"""
        # 实现插入逻辑
        pass
    
    def _handle_update(self, cursor, operation):
        """处理更新操作"""
        # 实现更新逻辑
        pass
    
    def _handle_delete(self, cursor, operation):
        """处理删除操作"""
        # 实现删除逻辑
        pass

# 全局数据库队列实例
_db_queue = None

def get_database_queue():
    """获取全局数据库队列实例"""
    global _db_queue
    if _db_queue is None:
        _db_queue = DatabaseQueue()
    return _db_queue

def start_database_queue():
    """启动数据库队列"""
    queue_instance = get_database_queue()
    queue_instance.start()
    return queue_instance

def stop_database_queue():
    """停止数据库队列"""
    global _db_queue
    if _db_queue:
        _db_queue.stop()
        _db_queue = None
