{"name": "backup-assistant", "version": "1.0.0", "description": "照片视频文件备份应用", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "uni build -p app-plus", "build:custom": "uni build -p custom", "build:h5": "uni build -p h5", "build:mp-360": "uni build -p mp-360", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-native": "uni build -p quickapp-native", "build:quickapp-webview": "uni build -p quickapp-webview", "dev:app-plus": "uni -p app-plus", "dev:custom": "uni -p custom", "dev:h5": "uni -p h5", "dev:mp-360": "uni -p mp-360", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-native": "uni -p quickapp-native", "dev:quickapp-webview": "uni -p quickapp-webview", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "uni -p app-plus -t app", "test:h5": "uni -p h5 -t h5", "test:ios": "uni -p app-plus -t app"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-alipay": "^3.0.0", "@dcloudio/uni-mp-baidu": "^3.0.0", "@dcloudio/uni-mp-qq": "^3.0.0", "@dcloudio/uni-mp-toutiao": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "@dcloudio/uni-quickapp-native": "^3.0.0", "@dcloudio/uni-quickapp-webview": "^3.0.0", "core-js": "^3.6.5", "vue": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "^3.0.0", "@dcloudio/uni-automator": "^3.0.0", "@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/webpack-uni-mp-loader": "^3.0.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.13.0", "cross-env": "^7.0.2", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}, "keywords": ["uniapp", "backup", "photo", "video", "webview", "gallery"], "author": "Developer", "license": "MIT"}