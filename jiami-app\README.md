# AES加密解密系统 - 图片与视频

这是一个基于HTML5和WebCrypto API的高性能文件加密解密系统，支持图片和视频文件的加密解密，使用不同强度的AES-GCM算法优化性能。

## 功能特点

### 🔐 加密器 (encrypt.html)
- **双模式支持**: 图片模式(AES-256-GCM) + 视频模式(AES-128-GCM)
- **超大文件支持**: 最大支持10GB文件，自动分块处理
- **高性能加密**: 使用WebCrypto API原生实现，16MB分块优化
- **智能处理策略**:
  - 小文件(<50MB): 直接加密，速度最快
  - 大文件(≥50MB): 分块加密，内存友好
- **批量处理**: 支持同时加密多个文件
- **拖拽上传**: 支持文件拖拽和点击选择
- **随机密钥生成**: 根据模式自动生成对应长度的安全随机密钥
- **实时进度**: 显示加密进度和详细性能统计，包含分块处理信息
- **文件格式**:
  - 图片：JPG、PNG、GIF、WebP等
  - 视频：MP4、AVI、MOV、WebM、MKV等

### 🔓 解密器 (decrypt.html)
- **智能识别**: 自动识别文件类型、加密算法和分块状态
- **超大文件支持**: 最大支持10GB文件，自动分块解密
- **高性能解密**: 使用WebCrypto API实现最佳性能
- **智能处理策略**:
  - 传统文件: 直接解密
  - 分块文件: 并行分块解密，内存优化
- **实时预览**: 解密后立即显示文件预览
- **批量解密**: 支持同时解密多个加密文件
- **多媒体展示**: 网格布局展示图片和视频
- **全屏预览**: 点击文件可全屏查看或播放
- **下载功能**: 支持下载解密后的原始文件
- **向后兼容**: 支持旧版本加密的文件

## 技术规格

### 图片模式
- **加密算法**: AES-256-GCM
- **密钥长度**: 256位 (32字节)
- **安全级别**: 最高级别，适合敏感图片

### 视频模式
- **加密算法**: AES-128-GCM
- **密钥长度**: 128位 (16字节)
- **性能优化**: 针对大文件优化，处理速度更快

### 通用规格
- **IV长度**: 12字节 (96位)
- **认证标签**: 16字节 (128位)
- **密钥派生**: PBKDF2-SHA256，100,000次迭代
- **随机盐**: 16字节，每次加密生成新盐

## 安全特性

1. **认证加密**: GCM模式提供数据完整性验证
2. **随机IV**: 每次加密使用新的随机初始化向量
3. **密钥派生**: 使用PBKDF2强化用户密码
4. **元数据保护**: 文件名、大小等信息也被加密保护
5. **前向安全**: 每个文件使用独立的盐和IV

## 使用方法

### 加密文件
1. 打开 `encrypt.html`
2. 选择处理模式：
   - **📷 图片模式**: 处理图片文件，使用AES-256-GCM
   - **🎬 视频模式**: 处理视频文件，使用AES-128-GCM
3. 输入加密密钥（至少8位）或点击"生成随机密钥"
4. 拖拽文件到上传区域或点击选择文件
5. 等待加密完成，查看性能统计
6. 下载生成的 `.encrypted` 文件

### 解密文件
1. 打开 `decrypt.html`
2. 选择对应的处理模式（图片或视频）
3. 输入与加密时相同的密钥
4. 拖拽 `.encrypted` 文件到上传区域
5. 等待解密完成，查看性能统计
6. 预览解密后的文件（图片/视频）
7. 可下载原始文件

## 文件格式

加密文件采用自定义格式，包含以下部分：
```
[Salt 16字节] + [IV 12字节] + [元数据长度 4字节] + [元数据] + [加密数据]
```

元数据包含：
- 原始文件名
- 原始文件大小
- MIME类型
- 加密时间戳
- 文件类型（图片/视频）
- 加密算法标识

## 性能优化

### 🚀 高性能分块处理
1. **智能分块**: 大于50MB的文件自动使用16MB分块处理
2. **并行处理**: 分块加密/解密支持并行处理，充分利用CPU
3. **内存优化**: 分块处理避免大文件导致的内存溢出
4. **进度可视化**: 实时显示分块处理进度

### ⚡ 核心优化技术
1. **WebCrypto API**: 使用浏览器原生加密API，性能最优
2. **异步操作**: 所有加密解密操作都是异步的，不阻塞UI
3. **内存管理**: 及时释放不需要的对象URL和缓冲区
4. **智能策略**: 根据文件大小自动选择最优处理方式

### 📊 性能监控
1. **详细统计**: 显示分块处理文件数量和处理策略
2. **时间分析**: 分别统计密钥派生、加密/解密、文件解析时间
3. **吞吐量计算**: 实时显示数据处理速度(MB/s)
4. **处理策略显示**: 区分直接处理和分块处理模式

## 浏览器兼容性

- Chrome 37+
- Firefox 34+
- Safari 7+
- Edge 12+

## 安全注意事项

1. **密钥安全**: 请妥善保管加密密钥，丢失密钥将无法恢复数据
2. **HTTPS**: 建议在HTTPS环境下使用，确保传输安全
3. **本地处理**: 所有加密解密操作都在本地进行，不会上传到服务器
4. **密钥强度**: 建议使用复杂的密钥或生成的随机密钥
5. **大文件处理**: 10GB文件处理需要足够的可用内存和存储空间
6. **分块安全**: 每个分块使用独立的IV，确保分块间的安全隔离

## 开发说明

代码采用现代JavaScript编写，使用ES6+语法：
- 类结构清晰，易于维护
- 错误处理完善
- 用户体验友好
- 代码注释详细

## 许可证

MIT License - 可自由使用和修改
