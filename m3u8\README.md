# 视频处理程序

这是一个自动化视频处理程序，用于监控指定目录中的MP4文件并进行加密处理。

## 功能特性

- **自动监控**: 每2秒检测一次temp目录中的MP4文件
- **缩略图生成**: 自动提取视频第一帧作为缩略图
- **智能加密**: 
  - 小文件(≤120MB): 直接AES128-GCM加密
  - 大文件(>120MB): 生成加密的M3U8分片
- **安全性**: 使用安全的16字节随机密钥和12字节IV
- **跨平台**: 兼容Windows和Linux系统
- **文件管理**: 基于SHA1哈希自动组织文件结构

## 系统要求

- Python 3.8+
- FFmpeg (必须安装并在PATH中)
- cryptography库

## 安装步骤

1. 确保Python 3.8+已安装
2. 安装FFmpeg:
   - Windows: 下载FFmpeg并添加到PATH
   - Linux: `sudo apt install ffmpeg` 或 `sudo yum install ffmpeg`
3. 安装Python依赖:
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行程序:
   ```bash
   python video_processor.py
   ```

2. 将MP4文件放入`temp`目录

3. 程序会自动:
   - 检测MP4文件
   - 生成缩略图
   - 根据文件大小选择加密方式
   - 将处理后的文件移动到`video`目录下的SHA1命名文件夹中

## 目录结构

```
项目根目录/
├── video_processor.py    # 主程序
├── requirements.txt      # Python依赖
├── README.md            # 说明文档
├── temp/                # 输入目录(放置MP4文件)
├── video/               # 输出目录
│   └── [SHA1]/          # 以文件SHA1命名的文件夹
│       ├── video.mp4.enc        # 加密的视频文件(小文件)
│       ├── playlist.m3u8        # M3U8播放列表(大文件)
│       ├── segment_*.ts         # 视频分片(大文件)
│       ├── [name]_tagvideo.jpg.enc  # 加密的缩略图
│       ├── key.key              # 加密密钥
│       ├── iv.iv                # 初始化向量
│       └── encryption_info.txt  # 加密信息
└── video_processor.log   # 日志文件
```

## 配置说明

- **监控间隔**: 2秒 (硬编码)
- **文件大小阈值**: 120MB
- **M3U8分片大小**: 10MB
- **缩略图尺寸**: 320x240
- **加密算法**: AES128-GCM

## 注意事项

1. 确保FFmpeg已正确安装并在PATH中
2. 程序会删除原始文件，请确保有备份
3. 加密密钥保存在输出目录中，请妥善保管
4. 程序运行时会持续监控，使用Ctrl+C停止

## 故障排除

- **FFmpeg未找到**: 确保FFmpeg已安装并在PATH中
- **权限错误**: 确保程序对temp和video目录有读写权限
- **内存不足**: 大文件处理时可能需要较多内存
- **加密失败**: 检查磁盘空间是否充足

## 日志

程序运行日志保存在`video_processor.log`文件中，包含详细的处理信息和错误记录。
