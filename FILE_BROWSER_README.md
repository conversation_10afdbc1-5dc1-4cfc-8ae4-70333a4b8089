# 文件浏览功能说明

## 功能概述

新增的文件浏览页面是一个端对端加密相册应用的核心功能，支持浏览存储在服务器上的加密图片、视频和文件。

## 主要特性

### 1. 三类文件支持
- **图片浏览**: 支持加密图片的解密和缩略图显示
- **视频浏览**: 支持加密视频的播放（开发中）
- **文件浏览**: 支持其他加密文件的管理（开发中）

### 2. 安全特性
- **端对端加密**: 所有文件使用AES-256-GCM算法加密
- **WebCrypto API**: 使用浏览器原生加密API确保安全性
- **动态URL**: 基于日期和密钥生成动态访问URL
- **本地解密**: 所有解密操作在本地进行

### 3. 性能优化
- **懒加载**: 图片按需加载和解密
- **批量处理**: 支持批量解密多张图片
- **内存管理**: 自动清理解密后的Object URLs
- **进度显示**: 实时显示加载和解密进度

## 使用方法

### 1. 配置设置
首次使用需要配置以下参数：
- **服务器域名**: 图片服务器的完整域名（如：https://example.com）
- **访问密钥**: 用于生成动态URL的密钥字符串

### 2. 数据库要求
应用需要读取私有目录下的SQLite数据库文件：
- 路径：`应用私有目录/doc/image_encryption.db`
- 表结构：包含`encrypted_images`、`encrypted_videos`、`encrypted_files`三个表

### 3. URL构建逻辑
图片访问URL的构建规则：
```
完整URL = 域名 + /file + 日期路径 + /哈希密钥 + /文件名
哈希密钥 = SHA256(访问密钥 + 今日日期)
```

例如：
- 数据库路径：`../data/file/2025-07-31/IMG20190621141002.jpg`
- 访问密钥：`mykey123`
- 今日日期：`2025-07-30`
- 哈希密钥：`SHA256("mykey1232025-07-30")`
- 最终URL：`https://example.com/file/2025-07-31/[哈希密钥]/IMG20190621141002.jpg`

### 4. 缩略图规则
缩略图URL在原图URL基础上添加`_tagimg`后缀：
- 原图：`IMG20190621141002.jpg`
- 缩略图：`IMG20190621141002_tagimg.jpg`

## 技术实现

### 1. 核心模块

#### 数据库模块 (`utils/database.js`)
- 读取SQLite数据库
- 查询加密文件信息
- 支持分页加载

#### URL构建模块 (`utils/urlBuilder.js`)
- 动态URL生成
- SHA256哈希计算
- 批量URL构建

#### 加密解密模块 (`utils/crypto.js`)
- AES-256-GCM解密
- WebCrypto API封装
- 批量图片解密

### 2. 数据库表结构

#### encrypted_images表
```sql
CREATE TABLE encrypted_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    iv TEXT NOT NULL,
    sha1_hash TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    capture_date TEXT DEFAULT NULL,
    file_size_mb REAL DEFAULT NULL,
    image_width INTEGER DEFAULT NULL,
    image_height INTEGER DEFAULT NULL,
    gps_latitude REAL DEFAULT NULL,
    gps_longitude REAL DEFAULT NULL
);
```

### 3. 解密流程
1. 从数据库读取图片信息（file_path, password, iv）
2. 根据file_path和设置构建访问URL
3. 下载加密的图片数据
4. 使用password和iv进行AES-256-GCM解密
5. 创建Blob对象和Object URL用于显示

## 开发说明

### 1. 环境要求
- UniApp框架
- 支持WebCrypto API的浏览器
- HTML5+ API（用于SQLite访问）

### 2. 测试功能
项目包含测试页面`test_crypto.html`，可以测试：
- URL构建逻辑
- SHA256哈希生成
- AES解密准备

### 3. 错误处理
- 数据库连接失败
- 图片下载失败
- 解密失败
- 网络连接问题

### 4. 性能考虑
- 图片按需解密，避免一次性解密大量图片
- 及时清理Object URLs，防止内存泄漏
- 支持进度显示，提升用户体验

## 安全注意事项

1. **密钥安全**: 访问密钥应妥善保管，不要在代码中硬编码
2. **HTTPS**: 建议使用HTTPS协议确保传输安全
3. **本地存储**: 设置信息存储在本地，注意保护
4. **解密密钥**: 每个文件的解密密钥和IV都是独立的

## 后续开发计划

1. **视频支持**: 实现加密视频的解密和播放
2. **文件管理**: 支持其他类型文件的下载和管理
3. **缓存机制**: 实现解密结果的本地缓存
4. **搜索功能**: 支持按文件名、日期等条件搜索
5. **批量操作**: 支持批量下载、删除等操作
