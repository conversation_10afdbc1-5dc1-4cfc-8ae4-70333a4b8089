/**
 * WebDAV客户端实现
 * 严格基于 kd2org/webdav-manager.js 专业实现
 * 支持基本的WebDAV操作：连接、认证、文件管理等
 */

// 简化的WebDAV客户端，专注于核心功能
class WebDAVClient {
  constructor(baseUrl, options = {}) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // 移除末尾斜杠
    this.user = options.user || ''
    this.password = options.password || ''
    this.timeout = options.timeout || 30000
    this.debug = options.debug || false

    // 设置认证头
    this.authHeader = (this.user && this.password) ?
      'Basic ' + btoa(this.user + ':' + this.password) : null
  }

  // 发送HTTP请求 - 严格按照专业实现
  async _request(method, url, body = null, headers = {}) {
    const requestHeaders = {
      ...headers
    }

    // 添加认证头
    if (this.authHeader) {
      requestHeaders['Authorization'] = this.authHeader
    }

    if (this.debug) {
      console.log(`WebDAV Request: ${method} ${url}`, { headers: requestHeaders, body })
    }

    return new Promise((resolve, reject) => {
      const requestConfig = {
        url,
        method,
        header: requestHeaders,
        timeout: this.timeout,
        success: (res) => {
          if (this.debug) {
            console.log(`WebDAV Response: ${res.statusCode}`, res)
          }

          // 创建兼容的Response对象
          const response = {
            status: res.statusCode,
            statusText: res.statusCode === 200 ? 'OK' : 'Error',
            ok: res.statusCode >= 200 && res.statusCode < 300,
            headers: {
              get: (name) => {
                const lowerName = name.toLowerCase()
                for (const key in res.header) {
                  if (key.toLowerCase() === lowerName) {
                    return res.header[key]
                  }
                }
                return null
              }
            },
            text: () => Promise.resolve(typeof res.data === 'string' ? res.data : JSON.stringify(res.data)),
            arrayBuffer: () => {
              if (res.data instanceof ArrayBuffer) {
                return Promise.resolve(res.data)
              } else if (typeof res.data === 'string') {
                const encoder = new TextEncoder()
                return Promise.resolve(encoder.encode(res.data).buffer)
              } else {
                return Promise.resolve(new ArrayBuffer(0))
              }
            }
          }

          resolve(response)
        },
        fail: (error) => {
          if (this.debug) {
            console.error('WebDAV Request failed:', error)
          }
          reject(new Error(`Request failed: ${error.errMsg || error.message}`))
        }
      }

      // 根据请求类型设置数据
      if (body) {
        if (method === 'PUT' && body instanceof ArrayBuffer) {
          requestConfig.data = body
        } else if (typeof body === 'string') {
          requestConfig.data = body
        } else {
          requestConfig.data = body
        }
      }

      uni.request(requestConfig)
    })
  }

  // 测试连接 - 使用PROPFIND而不是OPTIONS
  async ping() {
    try {
      // 直接尝试PROPFIND根目录，这是更可靠的WebDAV测试方法
      const response = await this.readDir('/')
      return true
    } catch (error) {
      if (this.debug) console.error('Ping failed:', error)
      throw error
    }
  }

  // 读取目录内容 - 严格按照专业实现
  async readDir(path = '/') {
    const normalizedPath = this._normalizePath(path)

    // PROPFIND XML模板，严格按照专业实现
    const propfindXml = `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:displayname/>
    <D:getcontentlength/>
    <D:getcontenttype/>
    <D:getlastmodified/>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`

    try {
      const response = await this._request('PROPFIND', this.baseUrl + normalizedPath, propfindXml, {
        'Content-Type': 'application/xml; charset=utf-8',
        'Depth': '1'
      })

      if (response.status === 207 || response.status === 200) {
        const xmlText = await response.text()
        return this._parseMultiStatus(xmlText, normalizedPath)
      } else {
        if (this.debug) {
          console.log(`ReadDir failed with status ${response.status}`)
          const errorText = await response.text()
          console.log('Response:', errorText)
        }
        throw new Error(`Failed to read directory: ${response.status}`)
      }
    } catch (error) {
      if (this.debug) console.error('ReadDir failed:', error)
      throw error
    }
  }

  // 规范化路径 - 添加URL编码支持
  _normalizePath(path) {
    if (!path.startsWith('/')) {
      path = '/' + path
    }
    // 移除重复的斜杠
    path = path.replace(/\/+/g, '/')

    // 对路径进行URL编码，但保留斜杠
    const pathParts = path.split('/')
    const encodedParts = pathParts.map(part => {
      if (part === '') return part
      return encodeURIComponent(part)
    })

    return encodedParts.join('/')
  }

  // 解析多状态XML响应 - 严格按照专业实现
  _parseMultiStatus(xmlText, basePath) {
    const files = []

    try {
      // 使用正则表达式解析XML，避免DOMParser兼容性问题
      const responseRegex = /<D:response[^>]*>([\s\S]*?)<\/D:response>/gi
      let match

      while ((match = responseRegex.exec(xmlText)) !== null) {
        const responseXml = match[1]
        const file = this._parseFileFromResponse(responseXml, basePath)
        if (file && file.path !== basePath) { // 排除当前目录本身
          files.push(file)
        }
      }
    } catch (error) {
      if (this.debug) console.error('XML parsing failed:', error)
    }

    return files
  }

  // 从响应中解析文件信息
  _parseFileFromResponse(responseXml, basePath) {
    try {
      const hrefMatch = responseXml.match(/<D:href[^>]*>([^<]+)<\/D:href>/i)
      if (!hrefMatch) return null

      let path = decodeURIComponent(hrefMatch[1])
      if (path.startsWith(this.baseUrl)) {
        path = path.substring(this.baseUrl.length)
      }

      const name = path.split('/').filter(p => p).pop() || ''
      const isDirectory = responseXml.includes('<D:collection') || responseXml.includes('<D:resourcetype><D:collection/></D:resourcetype>')

      const sizeMatch = responseXml.match(/<D:getcontentlength[^>]*>([^<]+)<\/D:getcontentlength>/i)
      const size = sizeMatch ? parseInt(sizeMatch[1]) : 0

      const typeMatch = responseXml.match(/<D:getcontenttype[^>]*>([^<]+)<\/D:getcontenttype>/i)
      const contentType = typeMatch ? typeMatch[1] : ''

      const modifiedMatch = responseXml.match(/<D:getlastmodified[^>]*>([^<]+)<\/D:getlastmodified>/i)
      const lastModified = modifiedMatch ? new Date(modifiedMatch[1]) : new Date()

      return {
        name,
        path,
        size,
        isDirectory,
        lastModified,
        contentType
      }
    } catch (error) {
      if (this.debug) console.error('File parsing failed:', error)
      return null
    }
  }

  // 创建目录
  async mkdir(path) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('MKCOL', this.baseUrl + normalizedPath)
      if (response.status !== 201 && response.status !== 405) {
        throw new Error(`Failed to create directory: ${response.status}`)
      }
      return true
    } catch (error) {
      if (this.debug) console.error('Mkdir failed:', error)
      throw error
    }
  }

  // 删除文件或目录
  async remove(path) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('DELETE', this.baseUrl + normalizedPath)
      if (response.status !== 200 && response.status !== 204 && response.status !== 404) {
        throw new Error(`Failed to remove: ${response.status}`)
      }
      return true
    } catch (error) {
      if (this.debug) console.error('Remove failed:', error)
      throw error
    }
  }

  // 重命名/移动文件或目录
  async rename(oldPath, newPath, overwrite = true) {
    const normalizedOldPath = this._normalizePath(oldPath)
    const normalizedNewPath = this._normalizePath(newPath)
    const destination = this.baseUrl + normalizedNewPath

    try {
      const response = await this._request('MOVE', this.baseUrl + normalizedOldPath, null, {
        'Destination': destination,
        'Overwrite': overwrite ? 'T' : 'F'
      })

      if (response.status !== 201 && response.status !== 204) {
        throw new Error(`Failed to rename: ${response.status}`)
      }
      return true
    } catch (error) {
      if (this.debug) console.error('Rename failed:', error)
      throw error
    }
  }

  // 下载文件
  async downloadFile(path, onProgress = null) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('GET', this.baseUrl + normalizedPath)
      if (response.status !== 200) {
        throw new Error(`Failed to download file: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()

      // 如果有进度回调，模拟进度更新
      if (onProgress) {
        onProgress(arrayBuffer.byteLength, arrayBuffer.byteLength)
      }

      return new Uint8Array(arrayBuffer)
    } catch (error) {
      if (this.debug) console.error('Download failed:', error)
      throw error
    }
  }

  // 上传文件
  async uploadFile(path, fileData, onProgress = null) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('PUT', this.baseUrl + normalizedPath, fileData, {
        'Content-Type': 'application/octet-stream'
      })

      if (response.status !== 200 && response.status !== 201 && response.status !== 204) {
        throw new Error(`Failed to upload file: ${response.status}`)
      }

      // 模拟进度完成
      if (onProgress) {
        const size = fileData.byteLength || fileData.length || 0
        onProgress(size, size)
      }

      return true
    } catch (error) {
      if (this.debug) console.error('Upload failed:', error)
      throw error
    }
  }
}

}

// WebDAV文件信息类
class WebDAVFile {
  constructor(data = {}) {
    this.name = data.name || ''
    this.path = data.path || ''
    this.size = data.size || 0
    this.isDirectory = data.isDirectory || false
    this.lastModified = data.lastModified || new Date()
    this.contentType = data.contentType || ''
  }
}

// 导出 - UniApp兼容方式，使用全局变量
if (typeof global !== 'undefined') {
  global.WebDAVClient = WebDAVClient
  global.WebDAVFile = WebDAVFile
} else if (typeof window !== 'undefined') {
  window.WebDAVClient = WebDAVClient
  window.WebDAVFile = WebDAVFile
}
