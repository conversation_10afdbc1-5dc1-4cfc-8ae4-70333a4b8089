#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库删除后的修复
"""

from pathlib import Path
import time
from PIL import Image

def cleanup_environment():
    """清理测试环境"""
    print("清理测试环境...")
    
    # 删除数据库文件
    db_file = Path('image_encryption.db')
    if db_file.exists():
        db_file.unlink()
        print("✓ 删除数据库文件")
    
    # 清理temp目录
    temp_dir = Path('temp')
    if temp_dir.exists():
        for file in temp_dir.iterdir():
            if file.is_file():
                file.unlink()
        print("✓ 清理temp目录")
    else:
        temp_dir.mkdir()
        print("✓ 创建temp目录")
    
    # 清理file目录
    file_dir = Path('file')
    if file_dir.exists():
        for file in file_dir.iterdir():
            if file.is_file():
                file.unlink()
        print("✓ 清理file目录")

def create_test_image():
    """创建测试图片"""
    temp_dir = Path('temp')
    
    # 创建一个测试图片
    img = Image.new('RGB', (300, 200), color=(200, 100, 50))
    test_path = temp_dir / "test_after_db_delete.jpg"
    img.save(test_path, 'JPEG', quality=90)
    
    print(f"✓ 创建测试图片: {test_path}")
    return test_path

def test_after_db_deletion():
    """测试删除数据库后的处理"""
    print("=" * 60)
    print("测试删除数据库后的图片处理")
    print("=" * 60)
    
    # 1. 清理环境
    cleanup_environment()
    
    # 2. 创建测试图片
    test_image = create_test_image()
    
    # 3. 确认数据库不存在
    db_file = Path('image_encryption.db')
    if db_file.exists():
        print("✗ 数据库文件仍然存在")
        return False
    else:
        print("✓ 确认数据库文件不存在")
    
    # 4. 启动主程序
    print("\n启动主程序...")
    from main import ImageProcessor
    
    try:
        processor = ImageProcessor()
        print("✓ ImageProcessor初始化成功")
        
        # 5. 检查数据库是否被创建
        if db_file.exists():
            print("✓ 数据库文件已自动创建")
        else:
            print("✗ 数据库文件未创建")
            return False
        
        # 6. 处理图片
        print("\n处理测试图片...")
        image_files = processor.get_image_files()
        print(f"找到 {len(image_files)} 个图片文件")
        
        if image_files:
            test_file = image_files[0]
            print(f"处理: {test_file}")
            
            # 处理图片
            processor.process_image(test_file)
            
            # 等待队列处理
            print("等待数据库队列处理...")
            time.sleep(3)
            
            # 7. 验证结果
            print("\n验证处理结果...")
            
            # 检查file目录
            file_dir = Path('file')
            if file_dir.exists():
                files = list(file_dir.iterdir())
                print(f"✓ file目录中有 {len(files)} 个文件")
                for f in files:
                    print(f"  - {f.name}")
            
            # 检查数据库记录
            check_database_records()
            
        # 8. 停止数据库队列
        print("\n停止数据库队列...")
        from database_queue import stop_database_queue
        stop_database_queue()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_records():
    """检查数据库记录"""
    try:
        import sqlite3
        conn = sqlite3.connect('image_encryption.db')
        cursor = conn.cursor()
        
        # 检查记录数量
        cursor.execute("SELECT COUNT(*) FROM encrypted_images")
        count = cursor.fetchone()[0]
        print(f"✓ 数据库记录数量: {count}")
        
        if count > 0:
            # 显示记录
            cursor.execute("SELECT file_path, created_at FROM encrypted_images ORDER BY created_at DESC")
            records = cursor.fetchall()
            print("✓ 数据库记录:")
            for file_path, created_at in records:
                print(f"  - {file_path} [{created_at}]")
        
        conn.close()
        
    except Exception as e:
        print(f"✗ 检查数据库记录失败: {e}")

def main():
    """主函数"""
    print("数据库删除后修复测试")
    
    success = test_after_db_deletion()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 测试成功！数据库删除后能正常工作")
    else:
        print("✗ 测试失败！仍有问题需要修复")
    print("=" * 60)

if __name__ == "__main__":
    main()
