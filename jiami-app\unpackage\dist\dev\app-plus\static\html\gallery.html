<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>相册展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 12px;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15),
                        0 8px 32px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        .header {
            text-align: center;
            margin-bottom: 28px;
            position: relative;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .subtitle {
            color: #666;
            font-size: 15px;
            font-weight: 400;
            opacity: 0.8;
        }

        .tabs {
            display: flex;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 6px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .tab {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            color: #64748b;
            position: relative;
            overflow: hidden;
        }

        .tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
            z-index: -1;
        }

        .tab.active {
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4),
                        0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .tab.active::before {
            opacity: 1;
        }

        .tab:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .content {
            min-height: 400px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            padding: 12px 0;
        }

        .item {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 18px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08),
                        0 4px 16px rgba(0, 0, 0, 0.04);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .item:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15),
                        0 8px 32px rgba(102, 126, 234, 0.2);
        }

        .item:hover::before {
            opacity: 1;
        }

        .item:active {
            transform: translateY(-4px) scale(1.01);
            transition: all 0.2s ease;
        }

        .item-image {
            width: 100%;
            height: 110px;
            object-fit: cover;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #cbd5e1;
            position: relative;
            overflow: hidden;
        }

        .item-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(102, 126, 234, 0.1) 0%,
                rgba(118, 75, 162, 0.1) 50%,
                rgba(240, 147, 251, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .item:hover .item-image::before {
            opacity: 1;
        }

        .item-info {
            padding: 14px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
        }

        .item-name {
            font-size: 13px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.3;
        }

        .item-meta {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #64748b;
            font-weight: 500;
        }

        .item-meta span {
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .item:hover .item-meta span {
            opacity: 1;
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #64748b;
            background: rgba(248, 250, 252, 0.5);
            border-radius: 20px;
            border: 2px dashed rgba(102, 126, 234, 0.2);
            margin: 20px 0;
        }

        .empty-icon {
            font-size: 72px;
            margin-bottom: 20px;
            opacity: 0.6;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .empty-state div:last-child {
            font-size: 16px;
            font-weight: 500;
            margin-top: 8px;
        }

        .loading {
            text-align: center;
            padding: 50px 20px;
            color: #64748b;
        }

        .spinner {
            width: 36px;
            height: 36px;
            border: 3px solid rgba(102, 126, 234, 0.1);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
            margin: 0 auto 20px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
            }
            50% {
                box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
            }
            100% {
                transform: rotate(360deg);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
            }
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 24px;
            padding: 20px;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 18px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
        }

        .stat-item {
            text-align: center;
            position: relative;
        }

        .stat-item::after {
            content: '';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 30px;
            background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.3), transparent);
        }

        .stat-item:last-child::after {
            display: none;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 13px;
            color: #64748b;
            font-weight: 500;
            opacity: 0.8;
        }

        @media (max-width: 480px) {
            body {
                padding: 8px;
            }

            .container {
                padding: 18px;
                margin: 0;
                border-radius: 20px;
            }

            .title {
                font-size: 24px;
            }

            .subtitle {
                font-size: 14px;
            }

            .stats {
                gap: 25px;
                padding: 16px;
                margin-bottom: 20px;
            }

            .stat-number {
                font-size: 20px;
            }

            .stat-label {
                font-size: 12px;
            }

            .tabs {
                padding: 4px;
                margin-bottom: 20px;
            }

            .tab {
                padding: 10px 12px;
                font-size: 13px;
            }

            .grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 12px;
                padding: 8px 0;
            }

            .item {
                border-radius: 16px;
            }

            .item-image {
                height: 90px;
                font-size: 30px;
            }

            .item-info {
                padding: 12px;
            }

            .item-name {
                font-size: 12px;
                margin-bottom: 5px;
            }

            .item-meta {
                font-size: 10px;
            }

            .item:hover {
                transform: translateY(-4px) scale(1.01);
            }
        }

        /* 页面加载动画 */
        .container {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 网格项目交错动画 */
        .item {
            animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
        }

        .item:nth-child(1) { animation-delay: 0.1s; }
        .item:nth-child(2) { animation-delay: 0.2s; }
        .item:nth-child(3) { animation-delay: 0.3s; }
        .item:nth-child(4) { animation-delay: 0.4s; }
        .item:nth-child(5) { animation-delay: 0.5s; }
        .item:nth-child(6) { animation-delay: 0.6s; }
        .item:nth-child(n+7) { animation-delay: 0.7s; }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 统计数字动画 */
        .stat-number {
            animation: countUp 1s ease-out 0.5s both;
        }

        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">📱 我的相册</div>
            <div class="subtitle">照片和视频备份管理</div>
        </div>

        <div class="stats" id="stats">
            <div class="stat-item">
                <div class="stat-number" id="imageCount">0</div>
                <div class="stat-label">照片</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="videoCount">0</div>
                <div class="stat-label">视频</div>
            </div>
        </div>

        <div class="tabs">
            <div class="tab active" data-tab="images">📷 照片</div>
            <div class="tab" data-tab="videos">🎬 视频</div>
        </div>

        <div class="content">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div>正在加载...</div>
            </div>
            
            <div class="grid" id="imagesGrid" style="display: none;"></div>
            <div class="grid" id="videosGrid" style="display: none;"></div>
            
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">📂</div>
                <div>暂无内容</div>
            </div>
        </div>
    </div>

    <script>
        class GalleryApp {
            constructor() {
                this.currentTab = 'images';
                this.data = { images: [], videos: [] };
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.sendReadyMessage();
            }

            setupEventListeners() {
                // 标签切换
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        const tabType = e.target.dataset.tab;
                        this.switchTab(tabType);
                    });
                });
            }

            sendReadyMessage() {
                // 向uni-app发送准备就绪消息
                if (typeof uni !== 'undefined' && uni.postMessage) {
                    uni.postMessage({
                        data: {
                            type: 'ready',
                            payload: { message: 'WebView已准备就绪' }
                        }
                    });
                }
            }

            switchTab(tabType) {
                // 更新标签状态
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-tab="${tabType}"]`).classList.add('active');

                // 切换内容
                document.getElementById('imagesGrid').style.display = tabType === 'images' ? 'grid' : 'none';
                document.getElementById('videosGrid').style.display = tabType === 'videos' ? 'grid' : 'none';
                
                this.currentTab = tabType;
                this.updateEmptyState();
            }

            updateGalleryData(data) {
                this.data = data;
                this.renderImages();
                this.renderVideos();
                this.updateStats();
                this.hideLoading();
                this.updateEmptyState();
            }

            renderImages() {
                const grid = document.getElementById('imagesGrid');
                grid.innerHTML = '';

                this.data.images.forEach(image => {
                    const item = this.createImageItem(image);
                    grid.appendChild(item);
                });
            }

            renderVideos() {
                const grid = document.getElementById('videosGrid');
                grid.innerHTML = '';

                this.data.videos.forEach(video => {
                    const item = this.createVideoItem(video);
                    grid.appendChild(item);
                });
            }

            createImageItem(image) {
                const item = document.createElement('div');
                item.className = 'item';
                item.innerHTML = `
                    <div class="item-image">📷</div>
                    <div class="item-info">
                        <div class="item-name">${image.name}</div>
                        <div class="item-meta">
                            <span>${image.size}</span>
                            <span>${image.date}</span>
                        </div>
                    </div>
                `;

                item.addEventListener('click', () => {
                    this.handleImageClick(image);
                });

                return item;
            }

            createVideoItem(video) {
                const item = document.createElement('div');
                item.className = 'item';
                item.style.position = 'relative';
                item.innerHTML = `
                    <div class="item-image">🎬</div>
                    <div class="video-duration">${video.duration}</div>
                    <div class="item-info">
                        <div class="item-name">${video.name}</div>
                        <div class="item-meta">
                            <span>${video.size}</span>
                            <span>${video.date}</span>
                        </div>
                    </div>
                `;

                item.addEventListener('click', () => {
                    this.handleVideoClick(video);
                });

                return item;
            }

            handleImageClick(image) {
                if (typeof uni !== 'undefined' && uni.postMessage) {
                    uni.postMessage({
                        data: {
                            type: 'imageClick',
                            payload: image
                        }
                    });
                }
            }

            handleVideoClick(video) {
                if (typeof uni !== 'undefined' && uni.postMessage) {
                    uni.postMessage({
                        data: {
                            type: 'videoClick',
                            payload: video
                        }
                    });
                }
            }

            updateStats() {
                document.getElementById('imageCount').textContent = this.data.images.length;
                document.getElementById('videoCount').textContent = this.data.videos.length;
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }

            updateEmptyState() {
                const currentData = this.data[this.currentTab];
                const isEmpty = !currentData || currentData.length === 0;
                document.getElementById('emptyState').style.display = isEmpty ? 'block' : 'none';
            }
        }

        // 初始化应用
        const gallery = new GalleryApp();

        // 全局方法，供uni-app调用
        window.updateGalleryData = function(data) {
            gallery.updateGalleryData(data);
        };
    </script>
</body>
</html>
