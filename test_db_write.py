#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试数据库写入功能
"""

import sys
import time
from pathlib import Path

# 添加aes目录到Python路径
sys.path.append(str(Path('aes')))

def test_database_write():
    """测试数据库写入功能"""
    try:
        print("1. 导入数据库队列...")
        from database_queue import get_database_queue
        
        print("2. 获取数据库队列实例...")
        db_queue = get_database_queue()
        
        print("3. 启动数据库队列...")
        if not db_queue.running:
            db_queue.start()
        
        print("4. 等待数据库队列启动完成...")
        time.sleep(2)
        
        print("5. 测试写入视频记录...")
        db_queue.save_encrypted_video(
            sha1_hash='video_test_123',
            original_filename='test_video.mp4',
            m3u8_path='video/test_video/index.m3u8',
            password='1234567890abcdef',
            iv='fedcba0987654321',
            file_size_mb=15.5,
            tag='test_tag',
            txt='test_description'
        )
        print("   ✓ 视频记录已添加到队列")
        
        print("6. 测试写入文件记录...")
        db_queue.save_encrypted_file(
            sha1_hash='file_test_456',
            original_filename='test_file.txt',
            file_path='other/2025-07-30/test_file.txt',
            password='abcdef1234567890',
            iv='0987654321fedcba',
            file_size_mb=2.3,
            tag='test_tag',
            txt='test_description'
        )
        print("   ✓ 文件记录已添加到队列")
        
        print("7. 等待队列处理完成...")
        time.sleep(3)
        
        print("8. 检查数据库记录...")
        import sqlite3
        db_file = Path('aes/image_encryption.db')
        
        if db_file.exists():
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()
            
            # 检查视频表
            cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
            video_count = cursor.fetchone()[0]
            print(f"   视频记录数: {video_count}")
            
            if video_count > 0:
                cursor.execute("SELECT original_filename, m3u8_path, password FROM encrypted_videos ORDER BY id DESC LIMIT 3")
                videos = cursor.fetchall()
                print("   最新视频记录:")
                for video in videos:
                    print(f"     - {video[0]} -> {video[1]} (密码: {video[2][:8]}...)")
            
            # 检查文件表
            cursor.execute("SELECT COUNT(*) FROM encrypted_files")
            file_count = cursor.fetchone()[0]
            print(f"   文件记录数: {file_count}")
            
            if file_count > 0:
                cursor.execute("SELECT original_filename, file_path, password FROM encrypted_files ORDER BY id DESC LIMIT 3")
                files = cursor.fetchall()
                print("   最新文件记录:")
                for file in files:
                    print(f"     - {file[0]} -> {file[1]} (密码: {file[2][:8]}...)")
            
            conn.close()
        else:
            print("   ❌ 数据库文件不存在")
            return False
        
        print("\n✅ 数据库写入测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_database_write()
