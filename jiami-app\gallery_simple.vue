<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="initialLoading" class="loading">
			<view class="spinner"></view>
			<text>正在扫描本机相册...</text>
			<text class="loading-tip">首次加载可能需要较长时间</text>
			<!-- 调试按钮 -->
			<view class="debug-actions">
				<button @click="skipToMockData" class="debug-btn">跳过使用模拟数据</button>
				<button @click="loadQuickMode" class="debug-btn quick-btn">快速加载模式</button>
			</view>
		</view>

		<!-- 简化的相册列表 -->
		<scroll-view v-else class="gallery-scroll" scroll-y="true" :style="{ height: containerHeight + 'px' }">
			<!-- 按日期分组显示 -->
			<view v-for="(group, date) in groupedMedia" :key="date" class="date-group">
				<!-- 图片网格 -->
				<view class="media-grid">
					<view
						v-for="item in group"
						:key="item.id"
						class="media-item"
						@click="handleItemClick(item)"
					>
						<image
							:src="item.url"
							class="media-image"
							mode="aspectFill"
							:lazy-load="true"
							@load="handleImageLoad(item)"
							@error="handleImageError(item)"
						/>
						
						<!-- 视频标识 -->
						<view v-if="item.type === 'video'" class="video-overlay">
							<view class="video-duration">{{ item.duration }}</view>
							<view class="video-play-icon">▶️</view>
						</view>
						
						<!-- 加载状态 -->
						<view v-if="item.loading" class="loading-overlay">
							<view class="mini-spinner"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="allMediaItems.length === 0" class="empty-state">
				<text class="empty-icon">🖼️</text>
				<text class="empty-text">暂无图片文件</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: 'Gallery',
	data() {
		return {
			// 基础数据
			allMediaItems: [],
			totalImages: 0,
			initialLoading: true,
			containerHeight: 0,
			imageCache: new Map()
		}
	},
	computed: {
		groupedMedia() {
			if (!this.allMediaItems || this.allMediaItems.length === 0) {
				return {}
			}

			const groups = {}
			this.allMediaItems.forEach(item => {
				const dateKey = item.date || '未知日期'
				if (!groups[dateKey]) {
					groups[dateKey] = []
				}
				groups[dateKey].push(item)
			})

			const sortedGroups = {}
			Object.keys(groups)
				.sort((a, b) => {
					if (a === '未知日期') return 1
					if (b === '未知日期') return -1
					return new Date(b) - new Date(a)
				})
				.forEach(key => {
					sortedGroups[key] = groups[key]
				})

			return sortedGroups
		}
	},
	onLoad() {
		console.log('相册页面加载')
		this.initializeGallery()
	},
	onUnload() {
		this.cleanup()
	},
	methods: {
		async initializeGallery() {
			console.log('开始初始化相册...')

			try {
				this.initializeContainer()
				await this.loadAllMediaData()
				this.initialLoading = false



			} catch (error) {
				console.error('❌ 相册初始化失败:', error)
				this.initialLoading = false

				uni.showModal({
					title: '初始化失败',
					content: `相册初始化失败: ${error.message || '未知错误'}`,
					showCancel: false,
					confirmText: '重试',
					success: (res) => {
						if (res.confirm) {
							this.initialLoading = true
							setTimeout(() => {
								this.initializeGallery()
							}, 1000)
						}
					}
				})
			}
		},

		initializeContainer() {
			const systemInfo = uni.getSystemInfoSync()
			this.containerHeight = systemInfo.windowHeight
			console.log('容器高度设置为:', this.containerHeight + 'px')
		},

		async loadAllMediaData() {
			console.log('开始加载媒体数据...')

			try {
				const hasPermission = await this.requestPermission()
				if (!hasPermission) {
					throw new Error('相册权限被拒绝')
				}

				const allImages = await this.loadAllImages()
				this.allMediaItems = allImages || []
				this.totalImages = this.allMediaItems.length

				console.log(`✅ 媒体数据加载完成: ${this.totalImages}张图片`)

			} catch (error) {
				console.error('❌ 加载媒体数据失败:', error)
				this.allMediaItems = []
				this.totalImages = 0
				throw error
			}
		},

		async loadAllImages() {
			// #ifdef APP-PLUS
			console.log('App端: 开始查询MediaStore...')
			return new Promise((resolve) => {
				try {
					const main = plus.android.runtimeMainActivity()
					const ContentResolver = plus.android.importClass('android.content.ContentResolver')
					const MediaStore = plus.android.importClass('android.provider.MediaStore')
					const ContentUris = plus.android.importClass('android.content.ContentUris')

					const resolver = main.getContentResolver()
					const uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI

					this.queryAllMediaStore(resolver, uri, 'image').then((images) => {
						console.log(`✅ MediaStore查询成功: ${images.length}张图片`)
						resolve(images)
					}).catch((error) => {
						console.error('❌ MediaStore查询失败:', error)
						resolve([])
					})

				} catch (error) {
					console.error('❌ 加载图片异常:', error)
					resolve([])
				}
			})
			// #endif

			// #ifndef APP-PLUS
			console.log('非App端: 生成模拟数据...')
			try {
				const mockImages = this.generateMockImages(50)
				console.log(`✅ 模拟图片生成成功: ${mockImages.length}张`)
				return Promise.resolve(mockImages)
			} catch (error) {
				console.error('❌ 生成模拟数据失败:', error)
				return Promise.resolve([])
			}
			// #endif
		},

		queryAllMediaStore(resolver, uri, type) {
			return new Promise((resolve) => {
				let cursor = null

				try {
					const MediaStore = plus.android.importClass('android.provider.MediaStore')
					const ContentUris = plus.android.importClass('android.content.ContentUris')

					const projection = [
						MediaStore.MediaColumns._ID,
						MediaStore.MediaColumns.DATE_MODIFIED
					]

					const sortOrder = `${MediaStore.MediaColumns.DATE_MODIFIED} DESC`

					cursor = resolver.query(uri, projection, null, null, sortOrder)
					const mediaFiles = []
					let processedCount = 0
					const maxItems = 100

					if (cursor && plus.android.invoke(cursor, "moveToFirst")) {
						const idColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns._ID)
						const dateColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DATE_MODIFIED)

						do {
							try {
								if (processedCount >= maxItems) {
									break
								}

								const id = plus.android.invoke(cursor, "getLong", idColumn)
								const dateModified = plus.android.invoke(cursor, "getLong", dateColumn)

								const contentUri = ContentUris.withAppendedId(uri, id)
								const uriString = plus.android.invoke(contentUri, "toString")

								const mediaItem = {
									id: `${type}_${id}`,
									url: uriString,
									name: `图片${processedCount + 1}`,
									size: '未知',
									date: new Date(dateModified * 1000).toLocaleDateString(),
									type: type,
									loading: false,
									cached: false
								}

								mediaFiles.push(mediaItem)
								processedCount++

							} catch (itemError) {
								console.error('❌ 处理媒体项失败:', itemError)
							}
						} while (plus.android.invoke(cursor, "moveToNext"))
					}

					resolve(mediaFiles)

				} catch (error) {
					console.error(`❌ 查询${type}失败:`, error)
					resolve([])
				} finally {
					if (cursor) {
						try {
							plus.android.invoke(cursor, "close")
						} catch (closeError) {
							console.error('❌ 关闭cursor失败:', closeError)
						}
					}
				}
			})
		},

		generateMockImages(count = 30) {
			const mockImages = []
			for (let i = 1; i <= count; i++) {
				const daysAgo = Math.floor(i / 10)
				const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000)

				mockImages.push({
					id: `mock_image_${i}`,
					url: `https://picsum.photos/300/300?random=${i}`,
					name: `模拟图片${i}.jpg`,
					size: `${(Math.random() * 3 + 0.5).toFixed(1)}MB`,
					date: date.toLocaleDateString(),
					type: 'image',
					loading: false,
					cached: false
				})
			}
			return mockImages
		},

		async requestPermission() {
			// #ifdef APP-PLUS
			return new Promise((resolve) => {
				plus.android.requestPermissions(
					['android.permission.READ_EXTERNAL_STORAGE'],
					(result) => {
						resolve(result.granted.length > 0)
					},
					(error) => {
						console.error('权限请求失败:', error)
						resolve(false)
					}
				)
			})
			// #endif

			// #ifndef APP-PLUS
			return Promise.resolve(true)
			// #endif
		},

		async skipToMockData() {
			try {
				this.initializeContainer()
				this.allMediaItems = this.generateMockImages(30)
				this.totalImages = this.allMediaItems.length
				this.initialLoading = false



			} catch (error) {
				console.error('❌ 模拟数据模式失败:', error)
				this.initialLoading = false
			}
		},

		async loadQuickMode() {
			try {
				this.initializeContainer()

				const hasPermission = await this.requestPermission()
				if (!hasPermission) {
					this.skipToMockData()
					return
				}

				const quickImages = await this.loadQuickImages(20)
				this.allMediaItems = quickImages
				this.totalImages = this.allMediaItems.length
				this.initialLoading = false



			} catch (error) {
				console.error('❌ 快速加载模式失败:', error)
				this.skipToMockData()
			}
		},

		async loadQuickImages(maxCount = 20) {
			// #ifdef APP-PLUS
			return new Promise((resolve) => {
				try {
					const main = plus.android.runtimeMainActivity()
					const ContentResolver = plus.android.importClass('android.content.ContentResolver')
					const MediaStore = plus.android.importClass('android.provider.MediaStore')
					const ContentUris = plus.android.importClass('android.content.ContentUris')

					const resolver = main.getContentResolver()
					const uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI

					const projection = [MediaStore.MediaColumns._ID, MediaStore.MediaColumns.DATE_MODIFIED]
					const sortOrder = `${MediaStore.MediaColumns.DATE_MODIFIED} DESC LIMIT ${maxCount}`

					const cursor = resolver.query(uri, projection, null, null, sortOrder)
					const mediaFiles = []

					if (cursor && plus.android.invoke(cursor, "moveToFirst")) {
						const idColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns._ID)
						const dateColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DATE_MODIFIED)

						let count = 0
						do {
							if (count >= maxCount) break

							const id = plus.android.invoke(cursor, "getLong", idColumn)
							const dateModified = plus.android.invoke(cursor, "getLong", dateColumn)
							const contentUri = ContentUris.withAppendedId(uri, id)
							const uriString = plus.android.invoke(contentUri, "toString")

							mediaFiles.push({
								id: `quick_${id}`,
								url: uriString,
								name: `图片${count + 1}`,
								size: '未知',
								date: new Date(dateModified * 1000).toLocaleDateString(),
								type: 'image',
								loading: false,
								cached: false
							})

							count++
						} while (plus.android.invoke(cursor, "moveToNext"))

						plus.android.invoke(cursor, "close")
					}

					resolve(mediaFiles)
				} catch (error) {
					console.error('快速加载失败:', error)
					resolve([])
				}
			})
			// #endif

			// #ifndef APP-PLUS
			return this.generateMockImages(maxCount)
			// #endif
		},

		handleImageLoad(item) {
			item.loading = false
		},

		handleImageError(item) {
			item.loading = false
		},

		handleItemClick(item) {
			if (item.type === 'image') {
				const imageUrls = this.allMediaItems
					.filter(img => img.type === 'image')
					.map(img => img.url)

				uni.previewImage({
					urls: imageUrls,
					current: item.url,
					fail: (error) => {
						uni.showToast({
							title: '图片预览失败',
							icon: 'error'
						})
					}
				})
			}
		},

		cleanup() {
			this.imageCache.clear()
		}
	}
}
</script>
