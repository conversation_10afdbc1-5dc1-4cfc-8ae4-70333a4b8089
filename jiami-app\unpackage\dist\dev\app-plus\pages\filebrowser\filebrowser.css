/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.filebrowser-container[data-v-fabfb9c0] {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}
.tab-bar[data-v-fabfb9c0] {
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}
.tab-bar .tab-item[data-v-fabfb9c0] {
  flex: 1;
  padding: 15px;
  text-align: center;
  border-bottom: 3px solid transparent;
}
.tab-bar .tab-item.active[data-v-fabfb9c0] {
  border-bottom-color: #007AFF;
}
.tab-bar .tab-item.active .tab-text[data-v-fabfb9c0] {
  color: #007AFF;
  font-weight: bold;
}
.tab-bar .tab-item .tab-text[data-v-fabfb9c0] {
  font-size: 16px;
  color: #666;
}
.settings-bar[data-v-fabfb9c0] {
  padding: 10px 15px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 10px;
}
.settings-bar .settings-btn[data-v-fabfb9c0], .settings-bar .refresh-btn[data-v-fabfb9c0], .settings-bar .debug-btn[data-v-fabfb9c0], .settings-bar .import-btn[data-v-fabfb9c0] {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
}
.settings-bar .settings-btn[data-v-fabfb9c0]:disabled, .settings-bar .refresh-btn[data-v-fabfb9c0]:disabled, .settings-bar .debug-btn[data-v-fabfb9c0]:disabled, .settings-bar .import-btn[data-v-fabfb9c0]:disabled {
  background: #ccc;
  opacity: 0.6;
}
.settings-bar .settings-btn .settings-text[data-v-fabfb9c0], .settings-bar .settings-btn .refresh-text[data-v-fabfb9c0], .settings-bar .settings-btn .debug-text[data-v-fabfb9c0], .settings-bar .settings-btn .import-text[data-v-fabfb9c0], .settings-bar .refresh-btn .settings-text[data-v-fabfb9c0], .settings-bar .refresh-btn .refresh-text[data-v-fabfb9c0], .settings-bar .refresh-btn .debug-text[data-v-fabfb9c0], .settings-bar .refresh-btn .import-text[data-v-fabfb9c0], .settings-bar .debug-btn .settings-text[data-v-fabfb9c0], .settings-bar .debug-btn .refresh-text[data-v-fabfb9c0], .settings-bar .debug-btn .debug-text[data-v-fabfb9c0], .settings-bar .debug-btn .import-text[data-v-fabfb9c0], .settings-bar .import-btn .settings-text[data-v-fabfb9c0], .settings-bar .import-btn .refresh-text[data-v-fabfb9c0], .settings-bar .import-btn .debug-text[data-v-fabfb9c0], .settings-bar .import-btn .import-text[data-v-fabfb9c0] {
  color: white;
}
.settings-bar .refresh-btn[data-v-fabfb9c0] {
  background: #28a745;
}
.settings-bar .debug-btn[data-v-fabfb9c0] {
  background: #ffc107;
}
.settings-bar .import-btn[data-v-fabfb9c0] {
  background: #6f42c1;
}
.content-area[data-v-fabfb9c0] {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}
.loading-container[data-v-fabfb9c0] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  padding: 20px;
}
.loading-container .loading-text[data-v-fabfb9c0] {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
}
.loading-container .progress-info[data-v-fabfb9c0] {
  width: 100%;
  max-width: 300px;
}
.loading-container .progress-info .progress-text[data-v-fabfb9c0] {
  font-size: 14px;
  color: #999;
  text-align: center;
  display: block;
  margin-bottom: 8px;
}
.loading-container .progress-info .progress-bar[data-v-fabfb9c0] {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}
.loading-container .progress-info .progress-bar .progress-fill[data-v-fabfb9c0] {
  height: 100%;
  background: #007AFF;
  transition: width 0.3s ease;
}
.images-grid[data-v-fabfb9c0] {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}
.images-grid .image-item[data-v-fabfb9c0] {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.images-grid .image-item.decrypt-error[data-v-fabfb9c0] {
  border: 2px solid #dc3545;
  opacity: 0.8;
}
.images-grid .image-item .image-container[data-v-fabfb9c0] {
  position: relative;
}
.images-grid .image-item .image-container .image-thumbnail[data-v-fabfb9c0] {
  width: 100%;
  height: 150px;
  background: #f5f5f5;
}
.images-grid .image-item .image-container .error-overlay[data-v-fabfb9c0] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 53, 69, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}
.images-grid .image-item .image-container .error-overlay .error-text[data-v-fabfb9c0] {
  color: white;
  font-size: 12px;
  font-weight: bold;
}
.images-grid .image-item .image-info[data-v-fabfb9c0] {
  padding: 10px;
}
.images-grid .image-item .image-info .image-name[data-v-fabfb9c0] {
  display: block;
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.images-grid .image-item .image-info .image-size[data-v-fabfb9c0] {
  font-size: 11px;
  color: #999;
  margin-bottom: 2px;
}
.images-grid .image-item .image-info .image-date[data-v-fabfb9c0] {
  font-size: 10px;
  color: #aaa;
  margin-bottom: 2px;
}
.images-grid .image-item .image-info .error-msg[data-v-fabfb9c0] {
  font-size: 10px;
  color: #dc3545;
  word-break: break-word;
}
.placeholder-text[data-v-fabfb9c0] {
  text-align: center;
  color: #999;
  font-size: 16px;
  margin-top: 50px;
}
.empty-state[data-v-fabfb9c0] {
  text-align: center;
  margin-top: 50px;
}
.empty-state .empty-text[data-v-fabfb9c0] {
  display: block;
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}
.empty-state .empty-hint[data-v-fabfb9c0] {
  font-size: 14px;
  color: #999;
}
.settings-modal[data-v-fabfb9c0] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.settings-modal .settings-content[data-v-fabfb9c0] {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}
.settings-modal .settings-content .settings-header[data-v-fabfb9c0] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}
.settings-modal .settings-content .settings-header .settings-title[data-v-fabfb9c0] {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.settings-modal .settings-content .settings-header .close-btn[data-v-fabfb9c0] {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.settings-modal .settings-content .settings-form[data-v-fabfb9c0] {
  padding: 20px;
}
.settings-modal .settings-content .settings-form .form-item[data-v-fabfb9c0] {
  margin-bottom: 20px;
}
.settings-modal .settings-content .settings-form .form-item .form-label[data-v-fabfb9c0] {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.settings-modal .settings-content .settings-form .form-item .form-textarea[data-v-fabfb9c0] {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box;
  resize: vertical;
  line-height: 1.4;
}
.settings-modal .settings-content .settings-form .form-actions[data-v-fabfb9c0] {
  text-align: center;
}
.settings-modal .settings-content .settings-form .form-actions .save-btn[data-v-fabfb9c0] {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 30px;
  font-size: 16px;
}