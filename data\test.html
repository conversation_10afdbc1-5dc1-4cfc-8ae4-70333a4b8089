<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .success {
            color: #27ae60;
            background-color: #d5f4e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            color: #2980b9;
            background-color: #ebf3fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 HTTP服务器安全测试</h1>
        
        <div class="success">
            <strong>✓ 成功访问！</strong><br>
            您正在访问 data 目录下的文件，这是被允许的。
        </div>
        
        <div class="info">
            <strong>ℹ️ 安全说明：</strong><br>
            • 此服务器已配置为只允许访问 data 文件夹及其子目录<br>
            • 尝试访问其他目录将返回 403 错误<br>
            • 所有路径遍历攻击（如 ../ ）都会被阻止<br>
            • 根路径访问会自动重定向到 data 目录
        </div>
        
        <h2>当前时间</h2>
        <p id="current-time"></p>
        
        <script>
            function updateTime() {
                const now = new Date();
                document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
            }
            updateTime();
            setInterval(updateTime, 1000);
        </script>
    </div>
</body>
</html>
