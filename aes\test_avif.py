#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AVIF转换功能
"""

import subprocess
from pathlib import Path

def test_avif_conversion():
    """测试AVIF转换"""
    # 检查AVIF工具是否存在
    avifenc = Path('avifenc.exe')
    if not avifenc.exists():
        print("错误: 未找到avifenc.exe")
        return False
    
    # 检查是否有测试图片
    test_images = list(Path('temp').glob('*.jpg'))
    if not test_images:
        print("错误: temp目录中没有JPG图片")
        return False
    
    test_image = test_images[0]
    output_path = test_image.parent / f"{test_image.stem}_test.avif"
    
    print(f"测试转换: {test_image} -> {output_path}")
    
    # 构建命令
    cmd = [
        str(avifenc),
        '-q', '80',  # 80%质量
        '-s', '6',   # 速度参数
        str(test_image),
        str(output_path)
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ AVIF转换成功!")
            if output_path.exists():
                size = output_path.stat().st_size
                print(f"输出文件: {output_path} ({size} bytes)")
                return True
            else:
                print("✗ 输出文件不存在")
                return False
        else:
            print(f"✗ AVIF转换失败:")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 执行失败: {e}")
        return False

if __name__ == "__main__":
    print("测试AVIF转换功能...")
    success = test_avif_conversion()
    if success:
        print("测试通过!")
    else:
        print("测试失败!")
