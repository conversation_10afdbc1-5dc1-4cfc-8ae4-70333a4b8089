#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本 - 测试所有功能的集成
"""

import os
import sys
import time
import sqlite3
from pathlib import Path

# 添加必要的路径
current_dir = Path(__file__).parent
aes_dir = current_dir / 'aes'
m3u8_dir = current_dir / 'm3u8'

sys.path.append(str(aes_dir))
sys.path.append(str(m3u8_dir))

def test_database_structure():
    """测试数据库结构"""
    print("=" * 50)
    print("测试数据库结构")
    print("=" * 50)
    
    try:
        from database_queue import get_database_queue
        
        # 获取数据库队列实例（这会自动创建数据库）
        db_queue = get_database_queue()
        if not db_queue.running:
            db_queue.start()

        # 等待数据库创建
        time.sleep(1)

        # 检查数据库文件
        db_file = aes_dir / 'image_encryption.db'
        if not db_file.exists():
            print(f"✗ 数据库文件不存在: {db_file}")
            # 尝试在当前目录查找
            alt_db_file = current_dir / 'aes' / 'image_encryption.db'
            if alt_db_file.exists():
                db_file = alt_db_file
                print(f"✓ 在备用路径找到数据库文件: {db_file}")
            else:
                return False
        
        # 连接数据库检查表结构
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['encrypted_images', 'encrypted_videos', 'encrypted_files']
        for table in expected_tables:
            if table in tables:
                print(f"✓ 表 {table} 存在")
                
                # 检查表结构
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [row[1] for row in cursor.fetchall()]
                print(f"  列: {', '.join(columns)}")
                
                # 检查记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  记录数: {count}")
            else:
                print(f"✗ 表 {table} 不存在")
        
        conn.close()
        print("✓ 数据库结构测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 数据库结构测试失败: {e}")
        return False

def test_video_processor():
    """测试视频处理器"""
    print("=" * 50)
    print("测试视频处理器")
    print("=" * 50)
    
    try:
        from video_processor import VideoProcessor
        
        # 创建视频处理器实例
        processor = VideoProcessor()
        print("✓ 视频处理器创建成功")
        
        # 检查必要目录
        temp_dir = m3u8_dir / 'temp'
        video_dir = m3u8_dir / 'video'
        
        if temp_dir.exists():
            print(f"✓ 临时目录存在: {temp_dir}")
        else:
            print(f"⚠ 临时目录不存在: {temp_dir}")
        
        if video_dir.exists():
            print(f"✓ 视频目录存在: {video_dir}")
        else:
            print(f"⚠ 视频目录不存在: {video_dir}")
        
        # 检查数据库队列连接
        if processor.db_queue:
            print("✓ 数据库队列连接成功")
        else:
            print("⚠ 数据库队列连接失败")
        
        return True
        
    except Exception as e:
        print(f"✗ 视频处理器测试失败: {e}")
        return False

def test_file_encryptor():
    """测试文件加密器"""
    print("=" * 50)
    print("测试文件加密器")
    print("=" * 50)
    
    try:
        from file_encryptor import FileEncryptor
        
        # 创建文件加密器实例
        encryptor = FileEncryptor()
        print("✓ 文件加密器创建成功")
        
        # 检查必要目录
        scan_dir = m3u8_dir / 'scanfile'
        other_dir = m3u8_dir / 'other'
        
        if scan_dir.exists():
            print(f"✓ 扫描目录存在: {scan_dir}")
        else:
            print(f"⚠ 扫描目录不存在: {scan_dir}")
        
        if other_dir.exists():
            print(f"✓ 输出目录存在: {other_dir}")
        else:
            print(f"⚠ 输出目录不存在: {other_dir}")
        
        # 检查数据库队列连接
        if encryptor.db_queue:
            print("✓ 数据库队列连接成功")
        else:
            print("⚠ 数据库队列连接失败")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件加密器测试失败: {e}")
        return False

def test_image_processor():
    """测试图片处理器"""
    print("=" * 50)
    print("测试图片处理器")
    print("=" * 50)
    
    try:
        # 检查aes目录的main.py
        main_file = aes_dir / 'main.py'
        if main_file.exists():
            print(f"✓ 图片处理器文件存在: {main_file}")
        else:
            print(f"✗ 图片处理器文件不存在: {main_file}")
            return False
        
        # 检查必要目录
        temp_dir = aes_dir / 'temp'
        if temp_dir.exists():
            print(f"✓ 临时目录存在: {temp_dir}")
        else:
            print(f"⚠ 临时目录不存在: {temp_dir}")
        
        return True
        
    except Exception as e:
        print(f"✗ 图片处理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("集成测试开始")
    print("=" * 60)
    
    tests = [
        ("数据库结构", test_database_structure),
        ("视频处理器", test_video_processor),
        ("文件加密器", test_file_encryptor),
        ("图片处理器", test_image_processor)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
        
        print()  # 空行分隔
    
    # 汇总结果
    print("=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统集成成功！")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
