<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="initialLoading" class="loading">
			<view class="spinner"></view>
			<text>正在扫描本机相册...</text>
			<text class="loading-tip">首次加载可能需要较长时间</text>
			<!-- 调试按钮 -->
			<view class="debug-actions">
				<button @click="skipToMockData" class="debug-btn">跳过使用模拟数据</button>
				<button @click="loadQuickMode" class="debug-btn quick-btn">快速加载模式</button>
			</view>
		</view>

		<!-- 简化的高性能相册列表 -->
		<scroll-view v-else class="gallery-scroll" scroll-y="true" :style="{ height: containerHeight + 'px' }">
			<!-- 按日期分组显示 -->
			<view v-for="(group, date) in groupedMedia" :key="date" class="date-group">
				<!-- 图片网格 -->
				<view class="media-grid">
					<view
						v-for="item in group"
						:key="item.id"
						class="media-item"
						@click="handleItemClick(item)"
					>
						<image
							:src="item.url"
							class="media-image"
							mode="aspectFill"
							:lazy-load="true"
							@load="handleImageLoad(item)"
							@error="handleImageError(item)"
						/>
						
						<!-- 视频标识 -->
						<view v-if="item.type === 'video'" class="video-overlay">
							<view class="video-duration">{{ item.duration }}</view>
							<view class="video-play-icon">▶️</view>
						</view>
						
						<!-- 加载状态 -->
						<view v-if="item.loading" class="loading-overlay">
							<view class="mini-spinner"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="allMediaItems.length === 0" class="empty-state">
				<text class="empty-icon">🖼️</text>
				<text class="empty-text">暂无图片文件</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: 'Gallery',
	data() {
		return {
			// 基础数据
			allMediaItems: [], // 所有媒体项的完整列表
			totalImages: 0,
			cachedImages: 0,
			initialLoading: true,

			// 容器相关
			containerHeight: 0,

			// 性能优化
			imageCache: new Map(), // 图片缓存
			cacheHitRate: 0
		}
	},
	computed: {
		// 按日期分组媒体数据
		groupedMedia() {
			if (!this.allMediaItems || this.allMediaItems.length === 0) {
				return {}
			}

			const groups = {}

			this.allMediaItems.forEach(item => {
				const dateKey = item.date || '未知日期'
				if (!groups[dateKey]) {
					groups[dateKey] = []
				}
				groups[dateKey].push(item)
			})

			// 按日期排序（最新的在前）
			const sortedGroups = {}
			Object.keys(groups)
				.sort((a, b) => {
					if (a === '未知日期') return 1
					if (b === '未知日期') return -1
					return new Date(b) - new Date(a)
				})
				.forEach(key => {
					sortedGroups[key] = groups[key]
				})

			return sortedGroups
		}
	},
	onLoad() {
		console.log('相册页面加载')
		this.initializeGallery()
	},
	onUnload() {
		this.cleanup()
	},
	methods: {
		// ==================== 初始化方法 ====================

		async initializeGallery() {
			console.log('开始初始化相册...')

			try {
				// 初始化容器高度
				this.initializeContainer()

				// 开始加载媒体数据
				console.log('1. 开始加载媒体数据...')
				await this.loadAllMediaData()

				this.initialLoading = false
				console.log('✅ 相册初始化完成')



			} catch (error) {
				console.error('❌ 相册初始化失败:', error)
				this.initialLoading = false

				// 显示错误提示
				uni.showModal({
					title: '初始化失败',
					content: `相册初始化失败: ${error.message || '未知错误'}`,
					showCancel: false,
					confirmText: '重试',
					success: (res) => {
						if (res.confirm) {
							// 重新初始化
							this.initialLoading = true
							setTimeout(() => {
								this.initializeGallery()
							}, 1000)
						}
					}
				})
			}
		},

		initializeContainer() {
			// 获取系统信息并计算容器高度
			const systemInfo = uni.getSystemInfoSync()
			// 使用全屏高度
			this.containerHeight = systemInfo.windowHeight

			console.log('容器高度设置为:', this.containerHeight + 'px')
		},

		// ==================== 数据加载方法 ====================

		async loadAllMediaData() {
			console.log('开始加载媒体数据...')
			const startTime = Date.now()

			try {
				// 请求权限
				const hasPermission = await this.requestPermission()
				if (!hasPermission) {
					throw new Error('相册权限被拒绝')
				}

				// 加载图片数据
				const allImages = await this.loadAllImages()
				this.allMediaItems = allImages || []
				this.totalImages = this.allMediaItems.length

				const loadTime = Date.now() - startTime
				console.log(`✅ 媒体数据加载完成: ${this.totalImages}张图片, 耗时: ${loadTime}ms`)

			} catch (error) {
				console.error('❌ 加载媒体数据失败:', error)
				this.allMediaItems = []
				this.totalImages = 0
				throw error
			}
		},

		async loadAllImages() {
			// #ifdef APP-PLUS
			console.log('App端: 开始查询MediaStore...')
			return new Promise((resolve, reject) => {
				try {
					const main = plus.android.runtimeMainActivity()
					const ContentResolver = plus.android.importClass('android.content.ContentResolver')
					const MediaStore = plus.android.importClass('android.provider.MediaStore')
					const ContentUris = plus.android.importClass('android.content.ContentUris')

					const resolver = main.getContentResolver()
					const uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI

					console.log('MediaStore URI:', uri.toString())

					// 查询所有图片，不分页
					this.queryAllMediaStore(resolver, uri, 'image').then((images) => {
						console.log(`✅ MediaStore查询成功: ${images.length}张图片`)
						resolve(images)
					}).catch((error) => {
						console.error('❌ MediaStore查询失败:', error)
						// 查询失败时返回空数组而不是reject
						resolve([])
					})

				} catch (error) {
					console.error('❌ 加载图片异常:', error)
					// 异常时也返回空数组
					resolve([])
				}
			})
			// #endif

			// #ifndef APP-PLUS
			// 非App端返回模拟数据
			console.log('非App端: 生成模拟数据...')
			try {
				const mockImages = this.generateMockImages(50) // 减少到50张避免卡顿
				console.log(`✅ 模拟图片生成成功: ${mockImages.length}张`)
				return Promise.resolve(mockImages)
			} catch (error) {
				console.error('❌ 生成模拟数据失败:', error)
				return Promise.resolve([])
			}
			// #endif
		},

		// 查询所有媒体文件（优化版本）
		queryAllMediaStore(resolver, uri, type) {
			return new Promise((resolve, reject) => {
				let cursor = null

				try {
					const MediaStore = plus.android.importClass('android.provider.MediaStore')
					const ContentUris = plus.android.importClass('android.content.ContentUris')

					// 查询列 - 只查询必要的列以提高性能
					const projection = [
						MediaStore.MediaColumns._ID,
						MediaStore.MediaColumns.DATE_MODIFIED
					]

					// 按修改时间降序排列
					const sortOrder = `${MediaStore.MediaColumns.DATE_MODIFIED} DESC`

					console.log(`📱 开始查询${type}文件...`)
					const startTime = Date.now()

					cursor = resolver.query(uri, projection, null, null, sortOrder)
					const mediaFiles = []
					let processedCount = 0
					const maxItems = 100 // 限制数量，提高加载速度

					if (cursor && plus.android.invoke(cursor, "moveToFirst")) {
						const idColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns._ID)
						const dateColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DATE_MODIFIED)

						console.log(`📊 列索引获取成功: ID=${idColumn}, Date=${dateColumn}`)

						do {
							try {
								// 限制处理数量
								if (processedCount >= maxItems) {
									console.log(`⚠️ 达到最大处理数量限制: ${maxItems}`)
									break
								}

								const id = plus.android.invoke(cursor, "getLong", idColumn)
								const dateModified = plus.android.invoke(cursor, "getLong", dateColumn)

								// 构建内容URI
								const contentUri = ContentUris.withAppendedId(uri, id)
								const uriString = plus.android.invoke(contentUri, "toString")

								const mediaItem = {
									id: `${type}_${id}`,
									url: uriString,
									name: `${type === 'image' ? '图片' : '视频'}${processedCount + 1}`,
									size: '未知',
									date: new Date(dateModified * 1000).toLocaleDateString(),
									type: type,
									loading: false,
									cached: false
								}

								mediaFiles.push(mediaItem)
								processedCount++

							} catch (itemError) {
								console.error('❌ 处理媒体项失败:', itemError)
							}
						} while (plus.android.invoke(cursor, "moveToNext"))

						console.log(`📋 ${type}文件遍历完成，共处理 ${processedCount} 个文件`)
					} else {
						console.log(`⚠️ 没有找到${type}文件或cursor为空`)
					}

					const loadTime = Date.now() - startTime
					console.log(`✅ ${type}查询完成: ${mediaFiles.length}个文件, 耗时: ${loadTime}ms`)
					resolve(mediaFiles)

				} catch (error) {
					console.error(`❌ 查询${type}失败:`, error)
					resolve([]) // 改为resolve空数组而不是reject
				} finally {
					// 确保cursor被正确关闭
					if (cursor) {
						try {
							plus.android.invoke(cursor, "close")
							console.log('📝 Cursor已关闭')
						} catch (closeError) {
							console.error('❌ 关闭cursor失败:', closeError)
						}
					}
				}
			})
		},

		// 生成模拟图片数据
		generateMockImages(count = 30) {
			const mockImages = []
			for (let i = 1; i <= count; i++) {
				// 生成不同的日期，确保有多样性
				const daysAgo = Math.floor(i / 10) // 每10张图片一个日期
				const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000)

				mockImages.push({
					id: `mock_image_${i}`,
					url: `https://picsum.photos/300/300?random=${i}`,
					name: `模拟图片${i}.jpg`,
					size: `${(Math.random() * 3 + 0.5).toFixed(1)}MB`,
					date: date.toLocaleDateString(),
					type: 'image',
					loading: false,
					cached: false
				})
			}
			return mockImages
		},
