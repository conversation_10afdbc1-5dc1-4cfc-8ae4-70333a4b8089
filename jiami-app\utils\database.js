/**
 * SQLite数据库操作工具类
 * 用于读取应用私有目录中的加密图片数据库
 */

class DatabaseManager {
  constructor() {
    this.dbPath = null
    this.isInitialized = false
    this.currentDbName = null // 记录当前使用的数据库名称
  }

  /**
   * 初始化数据库路径
   */
  async initialize() {
    try {
      // 检查运行环境
      if (typeof plus === 'undefined') {
        // 非App环境，使用模拟数据
        console.warn('非App环境，将使用模拟数据')
        this.isInitialized = true
        return true
      }

      // 获取应用私有目录路径
      const docPath = plus.io.convertLocalFileSystemURL('_doc/')
      console.log('应用私有目录路径:', docPath)

      // 尝试多个可能的数据库路径
      const possiblePaths = [
        docPath + 'doc/image_encryption.db',
        docPath + 'image_encryption.db',
        docPath + 'files/image_encryption.db'
      ]

      let foundPath = null
      for (const path of possiblePaths) {
        console.log('检查数据库路径:', path)
        const exists = await this.checkFileExists(path)
        if (exists) {
          foundPath = path
          console.log('找到数据库文件:', path)
          break
        }
      }

      if (!foundPath) {
        // 检查并创建doc文件夹
        const docFolderPath = docPath + 'doc/'
        const docFolderExists = await this.checkFileExists(docFolderPath)
        if (!docFolderExists) {
          console.log('doc文件夹不存在，尝试创建:', docFolderPath)
          await this.createDirectory(docFolderPath)
        }

        // 列出目录内容用于调试
        console.log('列出_doc目录内容:')
        await this.listDirectoryContents(docPath)

        if (docFolderExists) {
          console.log('列出doc目录内容:')
          await this.listDirectoryContents(docFolderPath)
        }

        throw new Error('数据库文件不存在于任何预期路径中。请将image_encryption.db文件放入应用私有目录的doc文件夹下。')
      }

      this.dbPath = foundPath

      this.isInitialized = true
      console.log('数据库初始化成功:', this.dbPath)
      return true
    } catch (error) {
      console.error('数据库初始化失败:', error)
      throw error
    }
  }

  /**
   * 检查文件是否存在
   */
  checkFileExists(filePath) {
    return new Promise((resolve) => {
      plus.io.resolveLocalFileSystemURL(filePath,
        () => resolve(true),
        () => resolve(false)
      )
    })
  }

  /**
   * 创建目录
   */
  createDirectory(dirPath) {
    return new Promise((resolve, reject) => {
      plus.io.resolveLocalFileSystemURL('_doc/', (entry) => {
        entry.getDirectory('doc', { create: true }, (docEntry) => {
          console.log('doc目录创建成功:', docEntry.fullPath)
          resolve(docEntry)
        }, (error) => {
          console.error('创建doc目录失败:', error)
          reject(error)
        })
      }, (error) => {
        console.error('获取_doc目录失败:', error)
        reject(error)
      })
    })
  }

  /**
   * 列出目录内容
   */
  listDirectoryContents(dirPath) {
    return new Promise((resolve) => {
      plus.io.resolveLocalFileSystemURL(dirPath, (entry) => {
        const reader = entry.createReader()
        reader.readEntries((entries) => {
          console.log(`目录 ${dirPath} 内容:`)
          entries.forEach((entry) => {
            console.log(`- ${entry.isDirectory ? '[DIR]' : '[FILE]'} ${entry.name}`)
          })
          resolve(entries)
        }, (error) => {
          console.error('读取目录内容失败:', error)
          resolve([])
        })
      }, (error) => {
        console.error('访问目录失败:', error)
        resolve([])
      })
    })
  }

  /**
   * 执行SQL查询 - 强制打开数据库
   */
  async executeQuery(sql, params = []) {
    if (!this.isInitialized) {
      await this.initialize()
    }

    // 如果是非App环境，返回模拟数据
    if (typeof plus === 'undefined') {
      return this.getMockData(sql)
    }

    return new Promise((resolve, reject) => {
      // 根据DCloud官方文档，使用正确的static路径
      const attempts = [
        { name: 'image_encryption', path: '_www/static/image_encryption.db' },
        { name: 'imagedb', path: '_www/static/image_encryption.db' },
        { name: 'image_encryption', path: '_www/static/image_encryption.db' }
      ]

      let currentAttempt = 0

      const tryNext = () => {
        if (currentAttempt >= attempts.length) {
          reject(new Error('所有数据库打开尝试都失败了'))
          return
        }

        const { name, path } = attempts[currentAttempt]
        console.log(`尝试 ${currentAttempt + 1}/${attempts.length}: name=${name}, path=${path}`)

        // 先尝试关闭可能存在的数据库
        plus.sqlite.closeDatabase({
          name: name,
          success: () => console.log(`已关闭数据库 ${name}`),
          fail: () => console.log(`数据库 ${name} 未打开`)
        })

        // 打开数据库
        plus.sqlite.openDatabase({
          name: name,
          path: path,
          success: () => {
            console.log(`数据库打开成功: ${name}`)

            // 执行查询
            plus.sqlite.selectSql({
              name: name,
              sql: sql,
              success: (data) => {
                console.log('查询成功，结果行数:', data.length)
                plus.sqlite.closeDatabase({ name: name })
                resolve(data)
              },
              fail: (error) => {
                console.error('查询失败:', error)
                plus.sqlite.closeDatabase({ name: name })
                currentAttempt++
                tryNext()
              }
            })
          },
          fail: (error) => {
            console.error(`数据库打开失败: ${name}, 错误:`, error)
            currentAttempt++
            tryNext()
          }
        })
      }

      tryNext()
    })
  }

  /**
   * 读取数据库文件内容
   */
  readDatabaseFile() {
    return new Promise((resolve, reject) => {
      plus.io.resolveLocalFileSystemURL(this.dbPath, (entry) => {
        entry.file((file) => {
          const reader = new FileReader()
          reader.onload = (e) => {
            resolve(e.target.result)
          }
          reader.onerror = reject
          reader.readAsArrayBuffer(file)
        }, reject)
      }, reject)
    })
  }

  /**
   * 基于真实数据的模拟数据（文件确实存在时使用）
   */
  getRealBasedMockData(sql) {
    console.log('使用基于真实数据库的模拟数据，SQL:', sql)

    if (sql.includes('encrypted_images')) {
      // 返回更真实的数据，基于您提供的数据库结构
      return [
        {
          id: 1,
          file_path: '../data/file/2025-07-31/IMG20200312162112.jpg',
          password: 'fIsIZ3-aACs1yNeV',
          iv: '2233bd7706b151a6273c3208',
          sha1_hash: '6e3456785176385d12700046ecc0216a3be12280',
          created_at: '2025-07-30 18:43:30',
          txt: '',
          capture_date: '2020:03:12 16:21:12',
          file_size_mb: 9.03,
          image_width: 6000,
          image_height: 8000,
          gps_latitude: 30.63127,
          gps_longitude: 104.008772
        },
        {
          id: 2,
          file_path: '../data/file/2025-07-31/IMG_0045.HEIC',
          password: 'yRsxtYTMQ-ZbzyW7',
          iv: '2192bdfb74a64b3b76cd7d67',
          sha1_hash: 'dd07527c9c2943290cc14ee51056a877b8626a60',
          created_at: '2025-07-30 18:43:33',
          txt: '',
          capture_date: null,
          file_size_mb: 9.02,
          image_width: 4032,
          image_height: 3024,
          gps_latitude: null,
          gps_longitude: null
        },
        {
          id: 3,
          file_path: '../data/file/2025-07-31/IMG20190621141002.jpg',
          password: 'lkW4dKvjULCKkW7y',
          iv: '8571c8692d1ded7e9060b167',
          sha1_hash: 'fd86adc37c119e9b246314b86198c2c14f1cdd64',
          created_at: '2025-07-30 18:43:35',
          txt: '',
          capture_date: '2019:06:21 14:10:02',
          file_size_mb: 9.05,
          image_width: 8000,
          image_height: 6000,
          gps_latitude: 30.659698,
          gps_longitude: 104.061352
        }
      ]
    }

    if (sql.includes('COUNT')) {
      return [{ count: 3 }]
    }

    return []
  }

  /**
   * 获取模拟数据（用于开发测试）
   */
  getMockData(sql) {
    console.log('使用模拟数据，SQL:', sql)

    if (sql.includes('encrypted_images')) {
      return [
        {
          id: 1,
          file_path: '../data/file/2025-07-31/IMG20200312161551.jpg',
          password: 'P1Dd7d036UoYVm0y',
          iv: '853c2c49bfa3846606ab595b',
          sha1_hash: '1c844665e1dedcff9e7dd2852c0d6a781edd1195',
          created_at: '2025-07-30 18:43:41',
          capture_date: '2020:03:12 16:15:51',
          file_size_mb: 9.03,
          image_width: 6000,
          image_height: 8000
        },
        {
          id: 2,
          file_path: '../data/file/2025-07-31/IMG20190621141002.jpg',
          password: 'lkW4dKvjULCKkW7y',
          iv: '8571c8692d1ded7e9060b167',
          sha1_hash: 'fd86adc37c119e9b246314b86198c2c14f1cdd64',
          created_at: '2025-07-30 18:43:35',
          capture_date: '2019:06:21 14:10:02',
          file_size_mb: 9.05,
          image_width: 8000,
          image_height: 6000
        },
        {
          id: 3,
          file_path: '../data/file/2025-07-31/test_image.jpg',
          password: 'testPassword123',
          iv: 'abcd1234567890ef',
          sha1_hash: 'test_hash_123456789',
          created_at: '2025-07-30 19:00:00',
          capture_date: '2025:07:30 19:00:00',
          file_size_mb: 5.2,
          image_width: 1920,
          image_height: 1080
        }
      ]
    }

    if (sql.includes('COUNT')) {
      return [{ count: 3 }]
    }

    return []
  }

  /**
   * 获取所有加密图片数据
   */
  async getEncryptedImages(limit = 50, offset = 0) {
    try {
      const sql = `
        SELECT 
          id,
          file_path,
          password,
          iv,
          sha1_hash,
          created_at,
          capture_date,
          file_size_mb,
          image_width,
          image_height,
          gps_latitude,
          gps_longitude
        FROM encrypted_images 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `
      
      const results = await this.executeQuery(sql, [limit, offset])
      console.log(`获取到 ${results.length} 条图片记录`)
      return results
    } catch (error) {
      console.error('获取图片数据失败:', error)
      throw error
    }
  }

  /**
   * 获取所有加密视频数据
   */
  async getEncryptedVideos(limit = 50, offset = 0) {
    try {
      const sql = `
        SELECT 
          id,
          sha1_hash,
          original_filename,
          m3u8_path,
          encryption_date,
          password,
          iv,
          file_created_date,
          file_size_mb,
          tag,
          txt
        FROM encrypted_videos 
        ORDER BY encryption_date DESC 
        LIMIT ? OFFSET ?
      `
      
      const results = await this.executeQuery(sql, [limit, offset])
      console.log(`获取到 ${results.length} 条视频记录`)
      return results
    } catch (error) {
      console.error('获取视频数据失败:', error)
      throw error
    }
  }

  /**
   * 获取所有加密文件数据
   */
  async getEncryptedFiles(limit = 50, offset = 0) {
    try {
      const sql = `
        SELECT 
          id,
          sha1_hash,
          original_filename,
          file_path,
          encryption_date,
          password,
          iv,
          file_created_date,
          file_size_mb,
          tag,
          txt
        FROM encrypted_files 
        ORDER BY encryption_date DESC 
        LIMIT ? OFFSET ?
      `
      
      const results = await this.executeQuery(sql, [limit, offset])
      console.log(`获取到 ${results.length} 条文件记录`)
      return results
    } catch (error) {
      console.error('获取文件数据失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取单个图片记录
   */
  async getImageById(id) {
    try {
      const sql = `
        SELECT * FROM encrypted_images WHERE id = ?
      `
      const results = await this.executeQuery(sql, [id])
      return results.length > 0 ? results[0] : null
    } catch (error) {
      console.error('获取图片记录失败:', error)
      throw error
    }
  }

  /**
   * 获取图片总数
   */
  async getImageCount() {
    try {
      const sql = 'SELECT COUNT(*) as count FROM encrypted_images'
      const results = await this.executeQuery(sql)
      return results[0].count
    } catch (error) {
      console.error('获取图片总数失败:', error)
      throw error
    }
  }

  /**
   * 获取视频总数
   */
  async getVideoCount() {
    try {
      const sql = 'SELECT COUNT(*) as count FROM encrypted_videos'
      const results = await this.executeQuery(sql)
      return results[0].count
    } catch (error) {
      console.error('获取视频总数失败:', error)
      throw error
    }
  }

  /**
   * 获取文件总数
   */
  async getFileCount() {
    try {
      const sql = 'SELECT COUNT(*) as count FROM encrypted_files'
      const results = await this.executeQuery(sql)
      return results[0].count
    } catch (error) {
      console.error('获取文件总数失败:', error)
      throw error
    }
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (typeof plus !== 'undefined') {
      try {
        plus.sqlite.closeDatabase({
          name: 'image_encryption',
          success: () => {
            console.log('数据库连接已关闭')
          },
          fail: (error) => {
            console.error('关闭数据库失败:', error)
          }
        })
      } catch (error) {
        console.error('关闭数据库异常:', error)
      }
    }
    this.isInitialized = false
  }

  /**
   * 获取数据库路径（用于调试）
   */
  getDbPath() {
    return this.dbPath
  }

  /**
   * 获取初始化状态（用于调试）
   */
  getInitStatus() {
    return this.isInitialized
  }

  /**
   * 修复文件权限
   */
  fixFilePermissions(filePath) {
    try {
      console.log('尝试修复文件权限:', filePath)

      // 使用plus.io读取文件来测试权限
      plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
        entry.file((file) => {
          console.log('文件可读，大小:', file.size, '字节')
        }, (error) => {
          console.error('文件读取失败:', error)
          // 尝试复制文件到新位置
          this.copyDatabaseFile(filePath)
        })
      }, (error) => {
        console.error('文件访问失败:', error)
      })
    } catch (error) {
      console.error('修复权限异常:', error)
    }
  }

  /**
   * 复制数据库文件到可访问位置
   */
  copyDatabaseFile(sourcePath) {
    try {
      console.log('尝试复制数据库文件到可访问位置')

      plus.io.resolveLocalFileSystemURL(sourcePath, (sourceEntry) => {
        plus.io.resolveLocalFileSystemURL('_doc/', (docEntry) => {
          sourceEntry.copyTo(docEntry, 'image_encryption_copy.db', (copiedEntry) => {
            console.log('数据库文件复制成功:', copiedEntry.fullPath)
            this.dbPath = copiedEntry.fullPath
          }, (error) => {
            console.error('复制数据库文件失败:', error)
          })
        })
      })
    } catch (error) {
      console.error('复制文件异常:', error)
    }
  }

  /**
   * 获取文件信息（用于调试）
   */
  getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      if (typeof plus === 'undefined') {
        reject(new Error('非App环境'))
        return
      }

      plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
        entry.getMetadata((metadata) => {
          resolve({
            name: entry.name,
            fullPath: entry.fullPath,
            size: metadata.size,
            modificationTime: metadata.modificationTime,
            isFile: entry.isFile,
            isDirectory: entry.isDirectory
          })
        }, (error) => {
          reject(error)
        })
      }, (error) => {
        reject(error)
      })
    })
  }
}

// 创建单例实例
const databaseManager = new DatabaseManager()

export default databaseManager
