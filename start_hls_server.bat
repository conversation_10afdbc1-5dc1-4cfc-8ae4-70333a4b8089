@echo off
chcp 65001 >nul
echo ========================================
echo HLS 视频播放服务器 (外网访问版)
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.6+
    pause
    exit /b 1
)

REM 检查m3u8目录
if not exist "m3u8" (
    echo 警告: m3u8目录不存在
    echo 请确保在正确的目录中运行此脚本
    echo.
)

REM 设置端口
set PORT=8081
if not "%1"=="" set PORT=%1

echo 正在启动服务器...
echo 端口: %PORT%
echo 绑定地址: 0.0.0.0 (支持外网访问)
echo.
echo 访问地址:
echo   本地: http://localhost:%PORT%/fixed_player.html
echo   局域网: http://************:%PORT%/fixed_player.html
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 启动服务器
python restart_server.py %PORT%
if %errorlevel% neq 0 (
    echo.
    echo 服务器启动失败，错误代码: %errorlevel%
    echo 可能的原因:
    echo   1. 端口 %PORT% 已被占用
    echo   2. Python环境问题
    echo   3. 权限不足
    echo.
    echo 建议解决方案:
    echo   1. 尝试其他端口: start_hls_server.bat 8082
    echo   2. 检查防火墙设置
    echo   3. 以管理员身份运行
)

pause
