#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动创建数据库
"""

import sqlite3
from pathlib import Path

def create_database():
    """创建数据库和表"""
    db_file = Path('aes/image_encryption.db')
    
    # 删除现有数据库
    if db_file.exists():
        db_file.unlink()
        print("删除现有数据库文件")
    
    # 创建新数据库
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    
    print("创建图片表...")
    cursor.execute('''
        CREATE TABLE encrypted_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_path TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            iv TEXT NOT NULL,
            sha1_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            txt TEXT DEFAULT '',
            capture_date TEXT DEFAULT NULL,
            file_size_mb REAL DEFAULT NULL,
            image_width INTEGER DEFAULT NULL,
            image_height INTEGER DEFAULT NULL,
            gps_latitude REAL DEFAULT NULL,
            gps_longitude REAL DEFAULT NULL
        )
    ''')
    
    print("创建视频表...")
    cursor.execute('''
        CREATE TABLE encrypted_videos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sha1_hash TEXT UNIQUE NOT NULL,
            original_filename TEXT NOT NULL,
            m3u8_path TEXT NOT NULL,
            encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            password TEXT NOT NULL,
            iv TEXT NOT NULL,
            file_created_date TEXT DEFAULT NULL,
            file_size_mb REAL NOT NULL,
            tag TEXT DEFAULT '',
            txt TEXT DEFAULT ''
        )
    ''')
    
    print("创建文件表...")
    cursor.execute('''
        CREATE TABLE encrypted_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sha1_hash TEXT UNIQUE NOT NULL,
            original_filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            password TEXT NOT NULL,
            iv TEXT NOT NULL,
            file_created_date TEXT DEFAULT NULL,
            file_size_mb REAL NOT NULL,
            tag TEXT DEFAULT '',
            txt TEXT DEFAULT ''
        )
    ''')
    
    print("创建索引...")
    # 图片表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_sha1 ON encrypted_images(sha1_hash)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_created_at ON encrypted_images(created_at)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_images_file_path ON encrypted_images(file_path)')
    
    # 视频表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_sha1 ON encrypted_videos(sha1_hash)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_encryption_date ON encrypted_videos(encryption_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_original_filename ON encrypted_videos(original_filename)')
    
    # 文件表索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_sha1 ON encrypted_files(sha1_hash)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_encryption_date ON encrypted_files(encryption_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_files_original_filename ON encrypted_files(original_filename)')
    
    conn.commit()
    conn.close()
    
    print(f"数据库创建成功: {db_file}")
    print(f"数据库文件大小: {db_file.stat().st_size} 字节")

if __name__ == "__main__":
    create_database()
