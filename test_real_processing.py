#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的文件处理流程
"""

import sys
import os
import time
import sqlite3
from pathlib import Path

def create_test_files():
    """创建测试文件"""
    print("1. 创建测试文件...")
    
    # 确保目录存在
    video_temp_dir = Path('m3u8/temp')
    file_scan_dir = Path('m3u8/scanfile')
    
    video_temp_dir.mkdir(parents=True, exist_ok=True)
    file_scan_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建测试视频文件
    test_video = video_temp_dir / 'test_real.mp4'
    with open(test_video, 'wb') as f:
        f.write(b'fake mp4 content for testing' * 1000)  # 创建一个较大的文件
    print(f"   ✓ 创建测试视频: {test_video}")
    
    # 创建测试文件
    test_file = file_scan_dir / 'test_real.txt'
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write('这是一个测试文件\n' * 100)
    print(f"   ✓ 创建测试文件: {test_file}")
    
    return test_video, test_file

def test_video_processor():
    """测试视频处理器"""
    print("2. 测试视频处理器...")
    
    try:
        # 切换到m3u8目录并导入
        original_cwd = os.getcwd()
        os.chdir('m3u8')
        sys.path.insert(0, os.getcwd())
        
        from video_processor import VideoProcessor
        
        # 创建处理器
        processor = VideoProcessor()
        
        # 检查temp目录中的文件
        temp_files = list(Path('temp').glob('*.mp4'))
        print(f"   发现视频文件: {len(temp_files)} 个")
        
        if temp_files:
            for video_file in temp_files:
                print(f"   处理视频文件: {video_file}")
                try:
                    # 手动调用处理方法
                    success = processor.process_video_file(video_file)
                    if success:
                        print(f"   ✓ 视频处理完成: {video_file}")
                    else:
                        print(f"   ❌ 视频处理失败: {video_file}")
                except Exception as e:
                    print(f"   ❌ 视频处理失败: {e}")
                    import traceback
                    traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 视频处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        os.chdir(original_cwd)
        if os.getcwd() in sys.path:
            sys.path.remove(os.getcwd())

def test_file_encryptor():
    """测试文件加密器"""
    print("3. 测试文件加密器...")
    
    try:
        # 切换到m3u8目录并导入
        original_cwd = os.getcwd()
        os.chdir('m3u8')
        sys.path.insert(0, os.getcwd())
        
        from file_encryptor import FileEncryptor
        
        # 创建加密器
        encryptor = FileEncryptor()
        
        # 检查scanfile目录中的文件
        scan_files = []
        for file_path in Path('scanfile').rglob('*'):
            if file_path.is_file() and encryptor.should_encrypt_file(file_path):
                scan_files.append(file_path)
        
        print(f"   发现需要加密的文件: {len(scan_files)} 个")
        
        if scan_files:
            for file_path in scan_files:
                print(f"   处理文件: {file_path}")
                try:
                    # 手动调用处理方法
                    encryptor.process_file(file_path)
                    print(f"   ✓ 文件处理完成: {file_path}")
                except Exception as e:
                    print(f"   ❌ 文件处理失败: {e}")
                    import traceback
                    traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 文件加密器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        os.chdir(original_cwd)
        if os.getcwd() in sys.path:
            sys.path.remove(os.getcwd())

def check_database():
    """检查数据库结果"""
    print("4. 检查数据库结果...")
    
    try:
        db_file = Path('aes/image_encryption.db')
        if not db_file.exists():
            print("   ❌ 数据库文件不存在")
            return False
        
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 检查视频记录
        cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
        video_count = cursor.fetchone()[0]
        
        # 检查文件记录
        cursor.execute("SELECT COUNT(*) FROM encrypted_files")
        file_count = cursor.fetchone()[0]
        
        print(f"   数据库记录数 - 视频: {video_count}, 文件: {file_count}")
        
        # 显示详细记录
        if video_count > 0:
            cursor.execute("SELECT original_filename, m3u8_path, file_size_mb FROM encrypted_videos ORDER BY id DESC LIMIT 3")
            videos = cursor.fetchall()
            print("   视频记录:")
            for video in videos:
                print(f"     - {video[0]} -> {video[1]} ({video[2]}MB)")
        
        if file_count > 0:
            cursor.execute("SELECT original_filename, file_path, file_size_mb FROM encrypted_files ORDER BY id DESC LIMIT 3")
            files = cursor.fetchall()
            print("   文件记录:")
            for file in files:
                print(f"     - {file[0]} -> {file[1]} ({file[2]}MB)")
        
        conn.close()
        
        return video_count > 0 or file_count > 0
        
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("5. 清理测试文件...")
    
    try:
        # 清理测试文件
        test_files = [
            Path('m3u8/temp/test_real.mp4'),
            Path('m3u8/scanfile/test_real.txt')
        ]
        
        for file_path in test_files:
            if file_path.exists():
                file_path.unlink()
                print(f"   ✓ 删除: {file_path}")
        
        # 清理可能生成的输出文件
        output_dirs = [
            Path('m3u8/video'),
            Path('m3u8/other')
        ]
        
        for output_dir in output_dirs:
            if output_dir.exists():
                for item in output_dir.rglob('*'):
                    if item.is_file() and 'test_real' in item.name:
                        item.unlink()
                        print(f"   ✓ 删除输出文件: {item}")
        
    except Exception as e:
        print(f"   ⚠ 清理失败: {e}")

def main():
    """主函数"""
    print("=== 测试真实文件处理流程 ===")
    
    try:
        # 1. 创建测试文件
        test_video, test_file = create_test_files()
        
        # 2. 测试视频处理器
        video_success = test_video_processor()
        
        # 3. 测试文件加密器
        file_success = test_file_encryptor()
        
        # 4. 检查数据库
        db_success = check_database()
        
        # 5. 清理测试文件
        cleanup_test_files()
        
        print("\n" + "="*50)
        print("测试结果汇总:")
        print(f"  视频处理器: {'✅ 通过' if video_success else '❌ 失败'}")
        print(f"  文件加密器: {'✅ 通过' if file_success else '❌ 失败'}")
        print(f"  数据库写入: {'✅ 通过' if db_success else '❌ 失败'}")
        
        if video_success and file_success and db_success:
            print("\n🎉 所有测试通过！文件处理和数据库写入正常工作")
        else:
            print("\n⚠ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
