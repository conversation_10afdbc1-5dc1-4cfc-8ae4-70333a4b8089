#!/bin/bash

echo "========================================"
echo "视频处理程序 - Linux启动脚本"
echo "========================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: Python3未安装"
    echo "请安装Python 3.8或更高版本"
    exit 1
fi

# 检查FFmpeg是否安装
if ! command -v ffmpeg &> /dev/null; then
    echo "错误: FFmpeg未安装"
    echo "请运行: sudo apt install ffmpeg (Ubuntu/Debian)"
    echo "或: sudo yum install ffmpeg (CentOS/RHEL)"
    exit 1
fi

# 安装依赖
echo "安装Python依赖..."
pip3 install -r requirements.txt

# 运行程序
echo "启动视频处理程序..."
python3 start.py
