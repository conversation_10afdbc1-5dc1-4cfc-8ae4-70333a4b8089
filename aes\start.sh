#!/bin/bash

# 图片处理和加密系统 - Linux启动脚本

echo "========================================"
echo "图片处理和加密系统 - Linux启动脚本"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "Arch Linux: sudo pacman -S python python-pip"
    exit 1
fi

echo "Python已安装，版本信息:"
python3 --version
echo

# 检查pip是否安装
if ! command -v pip3 &> /dev/null; then
    echo "错误: 未找到pip3，请先安装pip"
    exit 1
fi

# 检查是否存在requirements.txt
if [ ! -f "requirements.txt" ]; then
    echo "错误: 未找到requirements.txt文件"
    exit 1
fi

echo "正在检查并安装依赖包..."
python3 -m pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "警告: 依赖包安装可能存在问题"
    echo
fi

echo
echo "启动图片处理和加密系统..."
echo
python3 run.py
