# 项目结构说明

## 文件列表

```
aes/
├── main.py                    # 主程序 - 图片监控和处理系统
├── decrypt_tool.py           # 解密工具 - 用于恢复加密的图片
├── test_system.py            # 测试脚本 - 创建测试图片和验证功能
├── run.py                    # 启动脚本 - 提供交互式菜单界面
├── start.bat                 # Windows启动脚本
├── start.sh                  # Linux启动脚本
├── requirements.txt          # Python依赖包列表
├── README.md                 # 项目说明文档
├── PROJECT_STRUCTURE.md      # 项目结构说明（本文件）
├── temp/                     # 监控目录（程序自动创建）
│   └── (放置待处理的图片文件)
├── file/                     # 处理完成的文件存储目录（程序自动创建）
│   ├── 2025-07-29/          # 按日期分类的子文件夹（年-月-日格式）
│   │   └── (当日处理完成的加密图片文件)
│   └── 2025-07-30/          # 其他日期的文件夹
│       └── (其他日期处理的文件)
├── decrypted/                # 解密输出目录（解密时自动创建）
│   └── (解密后的图片文件)
└── image_encryption.db       # SQLite数据库（程序自动创建）
```

## 核心文件说明

### main.py
- **功能**: 主程序，负责监控temp目录并处理图片文件
- **主要类**: `ImageProcessor`
- **核心功能**:
  - 每2秒扫描temp目录
  - 生成JPG缩略图
  - 转换为AVIF格式
  - AES256-GCM加密
  - 数据库存储

### decrypt_tool.py
- **功能**: 解密工具，用于恢复被加密的图片
- **主要类**: `ImageDecryptor`
- **支持操作**:
  - 解密单个文件
  - 批量解密所有文件
  - 列出加密文件清单
  - 命令行参数支持

### test_system.py
- **功能**: 测试脚本，用于创建测试图片和验证系统功能
- **主要功能**:
  - 创建不同格式的测试图片
  - 检查依赖包安装状态
  - 清理测试环境

### run.py
- **功能**: 交互式启动脚本，提供用户友好的菜单界面
- **主要功能**:
  - 依赖包安装
  - 测试图片创建
  - 系统启动
  - 文件解密
  - 帮助信息

## 数据库结构

### encrypted_images表
```sql
CREATE TABLE encrypted_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT UNIQUE NOT NULL,      -- 文件相对路径
    password TEXT NOT NULL,              -- 加密密码
    iv TEXT NOT NULL,                    -- 初始化向量(十六进制)
    sha1_hash TEXT NOT NULL,             -- 原文件SHA1哈希
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    txt TEXT DEFAULT '',                 -- 备注信息
    capture_date TEXT DEFAULT NULL,      -- 拍摄日期(EXIF)
    file_size_mb REAL DEFAULT NULL,      -- 文件大小(MB)
    image_width INTEGER DEFAULT NULL,    -- 图片宽度(像素)
    image_height INTEGER DEFAULT NULL,   -- 图片高度(像素)
    gps_latitude REAL DEFAULT NULL,      -- GPS纬度
    gps_longitude REAL DEFAULT NULL      -- GPS经度
);
```

## 使用流程

### 快速开始
1. **Windows用户**: 双击 `start.bat`
2. **Linux用户**: 运行 `./start.sh` 或 `bash start.sh`
3. 按照菜单提示操作

### 手动操作
1. 安装依赖: `pip install -r requirements.txt`
2. 创建测试图片: `python test_system.py`
3. 启动监控: `python main.py`
4. 解密文件: `python decrypt_tool.py --list`

### 处理流程
1. **文件名冲突检查**: 检查今日文件夹中是否存在同名文件，如有冲突则自动重命名
2. **EXIF信息提取**: 在转换格式前提取原始图片的元数据信息
3. **缩略图生成**: 创建200px最大边长的JPG缩略图
4. **格式转换**: 转换为AVIF格式（失败则使用WebP）
5. **文件加密**: 使用AES256-GCM加密原图和缩略图
6. **按日期归档**: 移动到file目录下的日期子文件夹
7. **数据库记录**: 保存加密信息和EXIF元数据

## 技术特性

### 安全性
- **AES256-GCM加密**: 提供认证加密，确保数据完整性
- **唯一密码**: 每个文件使用独立的16位随机密码
- **安全随机数**: 使用`secrets`模块生成高质量随机数
- **SHA1验证**: 存储原文件哈希值用于完整性检查

### EXIF信息提取
- **拍摄日期**: 从EXIF中提取DateTime、DateTimeOriginal等字段
- **文件大小**: 自动计算文件大小（MB为单位）
- **图片尺寸**: 提取图片的宽度和高度（像素）
- **GPS坐标**: 解析GPS信息并转换为十进制度数格式
- **元数据保存**: 所有EXIF信息与加密数据一同存储在数据库中

### 性能优化
- **高效加密**: 使用`cryptography`库的AEAD接口
- **图片处理**: 基于PIL/Pillow的优化处理
- **数据库优化**: SQLite事务处理和索引优化
- **内存管理**: 流式处理大文件，避免内存溢出

### 跨平台兼容
- **路径处理**: 使用`pathlib`确保路径兼容性
- **编码支持**: UTF-8编码支持中文文件名
- **系统适配**: 同时支持Windows和Linux系统

## 扩展说明

### 支持的图片格式
- **JPG/JPEG**: 标准JPEG格式
- **PNG**: 支持透明度的PNG格式
- **WebP**: Google的WebP格式
- **AVIF**: 新一代AVIF格式
- **HEIC**: Apple的HEIC格式（需要额外插件）

### 输出文件命名规则
- **缩略图**: `原文件名_tagimg.jpg`
- **解密文件**: `原文件名_decrypted.原扩展名`
- **AVIF转换**: 保持原文件名，扩展名改为`.avif`

### 配置参数
- **监控间隔**: 2秒（可在代码中修改）
- **缩略图尺寸**: 200x200像素最大边
- **AVIF质量**: 80%
- **缩略图质量**: 85%
- **密码长度**: 16位

## 故障排除

### 常见问题
1. **依赖包安装失败**: 检查Python版本和网络连接
2. **AVIF格式不支持**: 确保安装了`pillow-avif-plugin`
3. **权限问题**: 确保对工作目录有读写权限
4. **数据库锁定**: 确保没有多个程序实例同时运行

### 日志和调试
- 程序会在控制台输出处理进度
- 错误信息会显示具体的失败原因
- 可以通过修改代码添加更详细的日志记录
