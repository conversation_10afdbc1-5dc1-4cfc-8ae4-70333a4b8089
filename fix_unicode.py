#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Unicode字符的脚本
"""

import re
from pathlib import Path

def fix_unicode_in_file(file_path):
    """修复文件中的Unicode字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换Unicode字符
        replacements = {
            '✓': '[OK]',
            '✗': '[ERROR]',
            '⚠': '[WARN]'
        }
        
        for old, new in replacements.items():
            content = content.replace(old, new)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"修复完成: {file_path}")
        return True
        
    except Exception as e:
        print(f"修复失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    files_to_fix = [
        Path('aes/database_queue.py'),
        Path('unified_launcher.py')
    ]
    
    for file_path in files_to_fix:
        if file_path.exists():
            fix_unicode_in_file(file_path)
        else:
            print(f"文件不存在: {file_path}")

if __name__ == "__main__":
    main()
