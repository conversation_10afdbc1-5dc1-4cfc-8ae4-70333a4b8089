#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库问题
"""

import sys
import time
import sqlite3
from pathlib import Path

# 添加aes目录到Python路径
sys.path.append(str(Path('aes')))

def check_database_directly():
    """直接检查数据库"""
    db_file = Path('aes/image_encryption.db')
    
    if not db_file.exists():
        print("数据库文件不存在")
        return
    
    print(f"数据库文件大小: {db_file.stat().st_size} 字节")
    
    try:
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"数据库中的表: {[table[0] for table in tables]}")
        
        # 检查每个表的记录数
        for table_name in ['encrypted_images', 'encrypted_videos', 'encrypted_files']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"{table_name} 表记录数: {count}")
                
                # 显示最新的几条记录
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 3")
                    records = cursor.fetchall()
                    print(f"  最新记录:")
                    for record in records:
                        print(f"    {record}")
                        
            except sqlite3.OperationalError as e:
                print(f"{table_name} 表不存在或有错误: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库检查失败: {e}")

def test_direct_insert():
    """直接插入测试数据"""
    db_file = Path('aes/image_encryption.db')
    
    try:
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        print("直接插入视频测试数据...")
        cursor.execute('''
            INSERT INTO encrypted_videos
            (sha1_hash, original_filename, m3u8_path, password, iv, file_size_mb, tag, txt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('direct_test_123', 'direct_test.mp4', 'video/direct_test/index.m3u8',
              '1234567890abcdef', 'fedcba0987654321', 10.5, 'direct_tag', 'direct_txt'))
        
        print("直接插入文件测试数据...")
        cursor.execute('''
            INSERT INTO encrypted_files
            (sha1_hash, original_filename, file_path, password, iv, file_size_mb, tag, txt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('direct_file_456', 'direct_test.txt', 'other/2025-07-30/direct_test.txt',
              'abcdef1234567890', '0987654321fedcba', 2.3, 'direct_tag', 'direct_txt'))
        
        conn.commit()
        conn.close()
        
        print("直接插入完成，检查结果...")
        check_database_directly()
        
    except Exception as e:
        print(f"直接插入失败: {e}")
        import traceback
        traceback.print_exc()

def test_queue_insert():
    """通过队列插入测试数据"""
    try:
        print("通过队列插入测试数据...")
        from database_queue import get_database_queue
        
        db_queue = get_database_queue()
        if not db_queue.running:
            db_queue.start()
        
        time.sleep(1)  # 等待启动
        
        print("添加视频记录到队列...")
        db_queue.save_encrypted_video(
            sha1_hash='queue_test_789',
            original_filename='queue_test.mp4',
            m3u8_path='video/queue_test/index.m3u8',
            password='queue1234567890ab',
            iv='queuefedcba098765',
            file_size_mb=20.5,
            tag='queue_tag',
            txt='queue_txt'
        )
        
        print("添加文件记录到队列...")
        db_queue.save_encrypted_file(
            sha1_hash='queue_file_abc',
            original_filename='queue_test.txt',
            file_path='other/2025-07-30/queue_test.txt',
            password='queueabcdef123456',
            iv='queue0987654321fe',
            file_size_mb=3.7,
            tag='queue_tag',
            txt='queue_txt'
        )
        
        print("等待队列处理...")
        time.sleep(3)
        
        print("检查队列插入结果...")
        check_database_directly()
        
    except Exception as e:
        print(f"队列插入失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=== 数据库调试工具 ===")
    
    print("\n1. 检查当前数据库状态:")
    check_database_directly()
    
    print("\n2. 测试直接插入:")
    test_direct_insert()
    
    print("\n3. 测试队列插入:")
    test_queue_insert()

if __name__ == "__main__":
    main()
