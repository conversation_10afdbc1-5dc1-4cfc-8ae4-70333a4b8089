#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库监控功能
"""

from pathlib import Path
import time
import threading

def test_database_monitor():
    """测试数据库监控功能"""
    print("=" * 60)
    print("数据库监控功能测试")
    print("=" * 60)
    
    # 清理环境
    db_file = Path('image_encryption.db')
    if db_file.exists():
        db_file.unlink()
        print("✓ 清理现有数据库文件")
    
    # 启动数据库队列
    print("\n启动数据库队列...")
    from database_queue import start_database_queue, stop_database_queue
    
    db_queue = start_database_queue()
    
    # 等待初始创建
    time.sleep(2)
    
    # 检查数据库是否被创建
    if db_file.exists():
        print("✓ 数据库文件已自动创建")
    else:
        print("✗ 数据库文件未创建")
        return False
    
    # 测试场景1: 删除数据库文件，看是否会在30秒内重新创建
    print("\n测试场景1: 删除数据库文件")
    db_file.unlink()
    print("✓ 删除数据库文件")
    
    print("等待监控线程检测并重新创建数据库...")
    print("(最多等待35秒)")
    
    # 等待最多35秒
    for i in range(35):
        if db_file.exists():
            print(f"✓ 数据库在 {i+1} 秒后被重新创建")
            break
        time.sleep(1)
        if (i + 1) % 5 == 0:
            print(f"  等待中... {i+1}/35 秒")
    else:
        print("✗ 数据库未在35秒内重新创建")
        stop_database_queue()
        return False
    
    # 测试场景2: 验证数据库结构
    print("\n测试场景2: 验证数据库结构")
    try:
        import sqlite3
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(encrypted_images)")
        columns = [row[1] for row in cursor.fetchall()]
        expected_columns = ['id', 'file_path', 'password', 'iv', 'sha1_hash', 'created_at', 'txt']
        
        print(f"✓ 数据库表结构: {columns}")
        
        missing_columns = [col for col in expected_columns if col not in columns]
        if missing_columns:
            print(f"✗ 缺少列: {missing_columns}")
            conn.close()
            stop_database_queue()
            return False
        else:
            print("✓ 数据库表结构完整")
        
        conn.close()
        
    except Exception as e:
        print(f"✗ 验证数据库结构失败: {e}")
        stop_database_queue()
        return False
    
    # 测试场景3: 测试数据库操作
    print("\n测试场景3: 测试数据库操作")
    
    # 添加一些测试数据
    db_queue.save_encrypted_image(
        file_path="test/monitor_test.jpg",
        password="test_password",
        iv="test_iv_hex",
        sha1_hash="test_sha1_hash",
        txt="monitor test"
    )
    
    # 等待处理
    time.sleep(2)
    
    # 验证数据是否保存
    try:
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM encrypted_images")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"✓ 数据库操作正常，记录数: {count}")
            
            # 显示记录
            cursor.execute("SELECT file_path, txt FROM encrypted_images")
            records = cursor.fetchall()
            for file_path, txt in records:
                print(f"  - {file_path}: {txt}")
        else:
            print("✗ 数据库操作失败，无记录")
            conn.close()
            stop_database_queue()
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"✗ 验证数据库操作失败: {e}")
        stop_database_queue()
        return False
    
    # 停止数据库队列
    print("\n停止数据库队列...")
    stop_database_queue()
    
    return True

def test_continuous_monitoring():
    """测试持续监控功能（较短时间）"""
    print("\n" + "=" * 60)
    print("持续监控测试（60秒）")
    print("=" * 60)
    
    from database_queue import start_database_queue, stop_database_queue
    
    # 启动队列
    db_queue = start_database_queue()
    db_file = Path('image_encryption.db')
    
    def delete_database_periodically():
        """定期删除数据库文件"""
        for i in range(3):  # 删除3次
            time.sleep(20)  # 每20秒删除一次
            if db_file.exists():
                db_file.unlink()
                print(f"⚠ 第 {i+1} 次删除数据库文件")
    
    # 启动删除线程
    delete_thread = threading.Thread(target=delete_database_periodically, daemon=True)
    delete_thread.start()
    
    # 监控60秒
    print("监控60秒，期间会定期删除数据库文件...")
    
    for i in range(60):
        time.sleep(1)
        if (i + 1) % 10 == 0:
            exists = "存在" if db_file.exists() else "不存在"
            print(f"  {i+1}秒: 数据库文件{exists}")
    
    # 最终检查
    if db_file.exists():
        print("✓ 监控测试成功，数据库文件最终存在")
        result = True
    else:
        print("✗ 监控测试失败，数据库文件最终不存在")
        result = False
    
    stop_database_queue()
    return result

def main():
    """主函数"""
    print("数据库监控功能测试")
    
    try:
        # 基本监控测试
        success1 = test_database_monitor()
        
        # 持续监控测试
        success2 = test_continuous_monitoring()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("✓ 所有测试通过！数据库监控功能正常")
        else:
            print("✗ 部分测试失败")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
