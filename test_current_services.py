#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前服务的数据库写入
"""

import os
import sys
import time
import sqlite3
from pathlib import Path

def get_current_db_counts():
    """获取当前数据库记录数"""
    db_file = Path('aes/image_encryption.db')
    if not db_file.exists():
        return 0, 0, 0
    
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM encrypted_images")
    image_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
    video_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM encrypted_files")
    file_count = cursor.fetchone()[0]
    
    conn.close()
    return image_count, video_count, file_count

def test_video_service():
    """测试视频服务"""
    print("1. 测试视频服务...")
    
    # 记录初始状态
    initial_images, initial_videos, initial_files = get_current_db_counts()
    print(f"   初始记录数 - 图片: {initial_images}, 视频: {initial_videos}, 文件: {initial_files}")
    
    # 创建测试视频文件
    test_video = Path('m3u8/temp/current_test.mp4')
    test_video.parent.mkdir(parents=True, exist_ok=True)
    
    with open(test_video, 'wb') as f:
        f.write(b'fake video content for current test' * 100)
    
    print(f"   ✓ 创建测试视频: {test_video}")
    
    # 等待一段时间让服务处理
    print("   等待视频服务处理...")
    time.sleep(5)
    
    # 检查结果
    final_images, final_videos, final_files = get_current_db_counts()
    print(f"   最终记录数 - 图片: {final_images}, 视频: {final_videos}, 文件: {final_files}")
    
    video_added = final_videos > initial_videos
    print(f"   视频记录增加: {video_added} (+{final_videos - initial_videos})")
    
    return video_added

def test_file_service():
    """测试文件服务"""
    print("2. 测试文件服务...")
    
    # 记录初始状态
    initial_images, initial_videos, initial_files = get_current_db_counts()
    print(f"   初始记录数 - 图片: {initial_images}, 视频: {initial_videos}, 文件: {initial_files}")
    
    # 创建测试文件
    test_file = Path('m3u8/scanfile/current_test.txt')
    test_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write('这是当前测试文件\n' * 50)
    
    print(f"   ✓ 创建测试文件: {test_file}")
    
    # 等待一段时间让服务处理
    print("   等待文件服务处理...")
    time.sleep(5)
    
    # 检查结果
    final_images, final_videos, final_files = get_current_db_counts()
    print(f"   最终记录数 - 图片: {final_images}, 视频: {final_videos}, 文件: {final_files}")
    
    file_added = final_files > initial_files
    print(f"   文件记录增加: {file_added} (+{final_files - initial_files})")
    
    return file_added

def show_recent_records():
    """显示最新的数据库记录"""
    print("3. 显示最新数据库记录...")
    
    db_file = Path('aes/image_encryption.db')
    if not db_file.exists():
        print("   数据库文件不存在")
        return
    
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    
    # 显示最新视频记录
    cursor.execute("SELECT original_filename, m3u8_path, encryption_date FROM encrypted_videos ORDER BY id DESC LIMIT 3")
    videos = cursor.fetchall()
    if videos:
        print("   最新视频记录:")
        for video in videos:
            print(f"     - {video[0]} -> {video[1]} ({video[2]})")
    
    # 显示最新文件记录
    cursor.execute("SELECT original_filename, file_path, encryption_date FROM encrypted_files ORDER BY id DESC LIMIT 3")
    files = cursor.fetchall()
    if files:
        print("   最新文件记录:")
        for file in files:
            print(f"     - {file[0]} -> {file[1]} ({file[2]})")
    
    conn.close()

def main():
    """主函数"""
    print("=== 测试当前服务的数据库写入 ===")
    print("注意: 请确保统一启动脚本正在运行！")
    print()
    
    # 显示当前状态
    current_images, current_videos, current_files = get_current_db_counts()
    print(f"当前数据库记录数 - 图片: {current_images}, 视频: {current_videos}, 文件: {current_files}")
    print()
    
    # 测试视频服务
    video_success = test_video_service()
    print()
    
    # 测试文件服务
    file_success = test_file_service()
    print()
    
    # 显示最新记录
    show_recent_records()
    print()
    
    # 总结
    print("="*50)
    print("测试结果:")
    print(f"  视频服务: {'✅ 正常工作' if video_success else '❌ 未工作'}")
    print(f"  文件服务: {'✅ 正常工作' if file_success else '❌ 未工作'}")
    
    if video_success and file_success:
        print("\n🎉 所有服务都正常工作并写入数据库！")
        print("如果您看不到数据，请检查您使用的数据库查看工具是否有缓存。")
    else:
        print("\n⚠ 部分服务未正常工作，请检查统一启动脚本是否在运行。")

if __name__ == "__main__":
    main()
