#!/usr/bin/env python3
"""
测试 M3U8 文件内容
"""

import requests

def test_m3u8():
    url = "http://localhost:8080/data/video/2025-07-30/17a603598b0bb7733e783e60ddc677320c65b81f/playlist.m3u8"
    
    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print("=" * 50)
        print("M3U8 内容:")
        print("=" * 50)
        print(response.text)
        print("=" * 50)
        
        # 检查是否包含加密信息
        if "#EXT-X-KEY:" in response.text:
            print("✓ 包含加密信息")
        else:
            print("✗ 不包含加密信息")
            
        # 检查是否包含 BYTERANGE
        if "#EXT-X-BYTERANGE:" in response.text:
            print("✗ 仍包含 BYTERANGE 信息")
        else:
            print("✓ BYTERANGE 信息已移除")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_m3u8()
