#!/usr/bin/env python3
"""
测试网络连接的脚本
"""

import socket
import requests
import sys

def test_port(host, port):
    """测试端口是否可达"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"端口测试异常: {e}")
        return False

def test_http(url):
    """测试HTTP连接"""
    try:
        response = requests.get(url, timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"HTTP测试异常: {e}")
        return False

def main():
    print("=== 网络连接测试 ===")
    
    # 测试本地连接
    print("\n1. 测试本地连接:")
    if test_port('127.0.0.1', 8081):
        print("✅ 本地端口8081可达")
    else:
        print("❌ 本地端口8081不可达")
    
    # 测试局域网IP连接
    print("\n2. 测试局域网IP连接:")
    if test_port('*************', 8081):
        print("✅ *************:8081可达")
    else:
        print("❌ *************:8081不可达")
    
    # 测试HTTP连接
    print("\n3. 测试HTTP连接:")
    urls = [
        'http://127.0.0.1:8081/fixed_player.html',
        'http://*************:8081/fixed_player.html'
    ]
    
    for url in urls:
        if test_http(url):
            print(f"✅ {url} 可访问")
        else:
            print(f"❌ {url} 不可访问")
    
    print("\n=== 测试完成 ===")
    print("\n如果*************不可达，可能的原因:")
    print("1. Windows防火墙阻止了连接")
    print("2. 网络适配器设置问题")
    print("3. 路由表配置问题")
    
    print("\n建议解决方案:")
    print("1. 临时关闭Windows防火墙测试")
    print("2. 添加防火墙规则允许8081端口")
    print("3. 检查网络适配器是否启用")

if __name__ == "__main__":
    main()
