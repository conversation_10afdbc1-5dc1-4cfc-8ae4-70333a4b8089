# 🚀 快速启动指南

## 项目概述

这是一个基于UniApp开发的备份助手APP，主要功能是展示照片和视频文件。项目使用WebView套装HTML5页面的方式实现，具有以下特点：

- ✅ **代码简单精炼**：核心代码不超过500行
- ✅ **易于维护**：清晰的项目结构和代码组织
- ✅ **性能优异**：使用本地HTML5页面，加载速度快
- ✅ **美观界面**：现代化的渐变设计和响应式布局

## 📁 项目结构

```
backup-assistant/
├── manifest.json          # 应用配置
├── pages.json             # 页面路由
├── main.js               # 入口文件
├── App.vue               # 根组件
├── uni.scss              # 全局样式变量
├── package.json          # 依赖配置
├── pages/
│   ├── index/
│   │   └── index.vue     # 备份助手页面（WebView容器）
│   └── gallery/
│       └── gallery.vue   # 本机相册页面
└── static/
    ├── html/
    │   └── gallery.html  # HTML5展示页面
    └── images/           # 图片资源
```

## 🛠️ 开发环境设置

### 方法一：使用HBuilderX（推荐）

1. 下载并安装 [HBuilderX](https://www.dcloud.io/hbuilderx.html)
2. 打开HBuilderX，选择"文件" -> "打开目录"
3. 选择本项目目录
4. 点击工具栏的"运行" -> 选择目标平台

### 方法二：使用命令行

```bash
# 安装uni-app CLI
npm install -g @vue/cli @vue/cli-init
vue init dcloudio/uni-preset-vue backup-assistant

# 进入项目目录
cd backup-assistant

# 安装依赖
npm install

# 运行到H5
npm run dev:h5

# 运行到App
npm run dev:app-plus

# 运行到微信小程序
npm run dev:mp-weixin
```

## 📱 运行和测试

### H5平台
```bash
npm run dev:h5
```
浏览器访问：http://localhost:8080

### App平台
```bash
npm run dev:app-plus
```
需要连接真机或模拟器进行测试

### 小程序平台
```bash
npm run dev:mp-weixin
```
需要在微信开发者工具中打开生成的dist目录

## 🎯 核心功能说明

### 1. 底部标签导航
- **备份助手**：原有的WebView展示功能
- **本机相册**：新增的本机相册浏览功能
- **原生标签栏**：使用uni-app原生tabBar组件

### 2. WebView集成
- **文件**：`pages/index/index.vue` 和 `pages/gallery/gallery.vue`
- **功能**：加载本地HTML5页面，实现双向通信
- **关键代码**：
```vue
<web-view
    :src="webviewSrc"
    @message="handleMessage"
    @load="handleLoad"
/>
```

### 3. 本机相册功能
- **文件**：`pages/gallery/gallery.vue`
- **功能**：读取和展示本机照片视频
- **特色**：权限管理、模拟数据、图片预览

### 4. HTML5展示页面
- **文件**：`static/html/gallery.html`
- **功能**：美观的图片视频列表展示
- **特色**：响应式设计、流畅动画、统计面板

### 5. 数据通信
- **uni-app → HTML5**：使用evalJS方法
- **HTML5 → uni-app**：使用postMessage方法

## 🎨 界面预览

### 主要特色
- 🌈 渐变背景设计
- 📱 响应式布局
- 🎯 卡片式文件展示
- 📊 实时统计信息
- 🔄 流畅切换动画

### 支持的文件类型
- 📷 图片：JPG、PNG、GIF等
- 🎬 视频：MP4、AVI、MOV等

## 🔧 自定义开发

### 修改示例数据
编辑 `pages/index/index.vue` 中的 `mockData`：

```javascript
const mockData = {
    images: [
        {
            id: 1,
            url: '/static/images/your-image.jpg',
            name: '你的图片.jpg',
            size: '2.5MB',
            date: '2024-01-15'
        }
    ],
    videos: [
        // 添加你的视频数据
    ]
}
```

### 添加新功能
1. **文件操作**：在 `handleImageClick` 和 `handleVideoClick` 方法中添加逻辑
2. **界面美化**：修改 `static/html/gallery.html` 中的CSS样式
3. **数据源**：集成真实的文件系统API

### 扩展建议
- 📁 集成文件管理API
- 🔍 添加搜索和筛选功能
- ☁️ 实现云端备份
- 🎵 支持音频文件
- 📋 添加文件批量操作

## 🐛 常见问题

### Q: WebView无法加载HTML页面？
A: 确保HTML文件路径正确，使用相对路径 `/static/html/gallery.html`

### Q: 通信不工作？
A: 检查是否正确引入uni.webview.js，确保在UniAppJSBridgeReady事件后调用API

### Q: 样式显示异常？
A: 检查CSS媒体查询，确保响应式设计适配目标设备

### Q: 性能问题？
A: 大量文件时考虑虚拟滚动，优化图片加载策略

## 📚 相关文档

- [UniApp官方文档](https://uniapp.dcloud.net.cn/)
- [WebView组件文档](https://uniapp.dcloud.net.cn/component/web-view)
- [Vue 3文档](https://v3.vuejs.org/)

## 🤝 技术支持

如有问题，请查看：
1. 项目README文档
2. UniApp官方社区
3. 提交Issue到项目仓库

---

**开始你的开发之旅吧！** 🎉
