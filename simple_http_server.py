#!/usr/bin/env python3
"""
最简单的HTTP服务器，用于测试外网访问
"""

import http.server
import socketserver
import os

class SimpleHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def guess_type(self, path):
        if path.endswith('.m3u8'):
            return 'application/vnd.apple.mpegurl'
        elif path.endswith('.ts'):
            return 'video/mp2t'
        elif path.endswith('.key'):
            return 'application/octet-stream'
        return super().guess_type(path)

    def do_GET(self):
        # 如果请求M3U8文件，移除BYTERANGE并检查密钥文件
        if self.path.endswith('/playlist.m3u8'):
            try:
                file_path = self.path.lstrip('/')
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 获取M3U8文件所在目录
                    m3u8_dir = os.path.dirname(file_path)

                    # 处理每一行
                    lines = content.split('\n')
                    fixed_lines = []

                    for line in lines:
                        # 移除BYTERANGE行
                        if line.startswith('#EXT-X-BYTERANGE:'):
                            continue
                        # 检查加密密钥行
                        elif line.startswith('#EXT-X-KEY:'):
                            # 提取密钥文件名
                            if 'URI="' in line:
                                start = line.find('URI="') + 5
                                end = line.find('"', start)
                                key_file = line[start:end]
                                key_path = os.path.join(m3u8_dir, key_file)

                                # 如果密钥文件存在，保留加密行
                                if os.path.exists(key_path):
                                    fixed_lines.append(line)
                                else:
                                    # 密钥文件不存在，返回错误信息而不是移除加密指令
                                    print(f"错误：密钥文件不存在: {key_file}")
                                    self.send_response(404)
                                    self.send_header('Content-Type', 'text/plain')
                                    self.end_headers()
                                    error_msg = f"Error: Encryption key file '{key_file}' not found. The video segments are encrypted but the key file is missing."
                                    self.wfile.write(error_msg.encode('utf-8'))
                                    return
                            else:
                                fixed_lines.append(line)
                        else:
                            fixed_lines.append(line)

                    fixed_content = '\n'.join(fixed_lines)

                    self.send_response(200)
                    self.send_header('Content-Type', 'application/vnd.apple.mpegurl')
                    self.send_header('Cache-Control', 'no-cache')
                    self.end_headers()
                    self.wfile.write(fixed_content.encode('utf-8'))
                    return
            except Exception as e:
                print(f"处理M3U8文件时出错: {e}")
                pass

        super().do_GET()

def main():
    port = 18888
    
    print(f"启动简单HTTP服务器在端口 {port}")
    print(f"访问地址:")
    print(f"  本地: http://localhost:{port}/fixed_player.html")
    print(f"  外网: http://*************:{port}/fixed_player.html")
    print("按 Ctrl+C 停止")
    
    with socketserver.TCPServer(("0.0.0.0", port), SimpleHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")

if __name__ == "__main__":
    main()
