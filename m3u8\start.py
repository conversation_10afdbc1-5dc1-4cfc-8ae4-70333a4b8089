#!/usr/bin/env python3
"""
启动脚本 - 带有更好的错误处理和用户交互
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_directories():
    """检查并创建必要的目录"""
    temp_dir = Path("temp")
    video_dir = Path("video")
    
    temp_dir.mkdir(exist_ok=True)
    video_dir.mkdir(exist_ok=True)
    
    print(f"✓ 目录检查完成")
    print(f"  - temp目录: {temp_dir.absolute()}")
    print(f"  - video目录: {video_dir.absolute()}")
    
    return True

def main():
    """主启动函数"""
    print("=" * 50)
    print("视频处理程序启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查目录
    if not check_directories():
        sys.exit(1)
    
    print("\n正在启动视频处理程序...")
    print("提示: 使用 Ctrl+C 停止程序")
    print("-" * 50)
    
    # 导入并运行主程序
    try:
        from video_processor import main as run_processor
        run_processor()
    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"\n程序运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
