#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# Read the file with proper encoding handling
try:
    with open('pages/gallery/gallery.vue', 'r', encoding='utf-8') as f:
        content = f.read()
except UnicodeDecodeError:
    with open('pages/gallery/gallery.vue', 'r', encoding='gbk') as f:
        content = f.read()

print("Original file length:", len(content))

# Remove header section
content = re.sub(r'(\s*)<!-- 顶部导航栏 -->.*?</view>', '', content, flags=re.DOTALL)
print("After removing header")

# Remove filter bar section  
content = re.sub(r'(\s*)<!-- 筛选栏 -->.*?</view>', '', content, flags=re.DOTALL)
print("After removing filter bar")

# Remove stats bar section
content = re.sub(r'(\s*)<!-- 统计信息 -->.*?</view>', '', content, flags=re.DOTALL)
print("After removing stats bar")

# Remove scroll indicator
content = re.sub(r'(\s*)<!-- 滚动指示器 -->.*?</view>', '', content, flags=re.DOTALL)
print("After removing scroll indicator")

# Remove performance panel
content = re.sub(r'(\s*)<!-- 性能监控面板 -->.*?</view>', '', content, flags=re.DOTALL)
print("After removing performance panel")

# Remove floating buttons
content = re.sub(r'(\s*)<!-- 浮动操作按钮 -->.*?</view>', '', content, flags=re.DOTALL)
print("After removing floating buttons")

# Remove date headers from the gallery
content = re.sub(r'(\s*)<!-- 日期标题 -->.*?</view>', '', content, flags=re.DOTALL)
print("After removing date headers")

# Clean up extra whitespace
content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

print("Final file length:", len(content))

# Write the cleaned content back
with open('pages/gallery/gallery.vue', 'w', encoding='utf-8') as f:
    f.write(content)

print("UI elements removed successfully!")
