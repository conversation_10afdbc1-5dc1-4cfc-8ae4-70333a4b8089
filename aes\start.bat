@echo off
chcp 65001 >nul
title 图片处理和加密系统

echo ========================================
echo 图片处理和加密系统 - Windows启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python已安装，版本信息:
python --version
echo.

REM 检查是否存在requirements.txt
if not exist requirements.txt (
    echo 错误: 未找到requirements.txt文件
    pause
    exit /b 1
)

echo 正在检查并安装依赖包...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo 警告: 依赖包安装可能存在问题
    echo.
)

echo.
echo 启动图片处理和加密系统...
echo.
python run.py

pause
