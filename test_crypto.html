<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密解密测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            word-break: break-all;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>加密解密功能测试</h1>
    
    <div class="test-section">
        <h3>URL构建测试</h3>
        <input type="text" id="filePath" placeholder="文件路径" value="../data/file/2025-07-31/IMG20190621141002.jpg">
        <input type="text" id="domain" placeholder="域名" value="https://example.com">
        <input type="text" id="accessKey" placeholder="访问密钥" value="testkey123">
        <button onclick="testUrlBuilder()">测试URL构建</button>
        <div id="urlResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>SHA256测试</h3>
        <input type="text" id="sha256Input" placeholder="输入文本" value="testkey1232025-07-30">
        <button onclick="testSHA256()">生成SHA256</button>
        <div id="sha256Result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>AES解密测试</h3>
        <input type="text" id="password" placeholder="密码" value="lkW4dKvjULCKkW7y">
        <input type="text" id="iv" placeholder="IV (hex)" value="8571c8692d1ded7e9060b167">
        <button onclick="testAESDecrypt()">测试AES解密</button>
        <div id="aesResult" class="result"></div>
    </div>

    <script type="module">
        // 导入工具函数
        import { generateSHA256, getTodayDateString } from './jiami-app/utils/urlBuilder.js';
        import { hexToUint8Array, deriveKeyFromPassword } from './jiami-app/utils/crypto.js';

        // 将函数暴露到全局作用域
        window.generateSHA256 = generateSHA256;
        window.getTodayDateString = getTodayDateString;
        window.hexToUint8Array = hexToUint8Array;
        window.deriveKeyFromPassword = deriveKeyFromPassword;

        // URL构建测试
        window.testUrlBuilder = async function() {
            try {
                const filePath = document.getElementById('filePath').value;
                const domain = document.getElementById('domain').value;
                const accessKey = document.getElementById('accessKey').value;
                
                const todayDate = getTodayDateString();
                const keyWithDate = accessKey + todayDate;
                const hashedKey = await generateSHA256(keyWithDate);
                
                // 构建URL
                let relativePath = filePath.replace('../data/file', '');
                if (!relativePath.startsWith('/')) {
                    relativePath = '/' + relativePath;
                }
                
                const fullUrl = `${domain}/file${relativePath.substring(0, relativePath.lastIndexOf('/'))}/${hashedKey}${relativePath.substring(relativePath.lastIndexOf('/'))}`;
                
                document.getElementById('urlResult').innerHTML = `
                    <strong>今日日期:</strong> ${todayDate}<br>
                    <strong>密钥+日期:</strong> ${keyWithDate}<br>
                    <strong>SHA256哈希:</strong> ${hashedKey}<br>
                    <strong>相对路径:</strong> ${relativePath}<br>
                    <strong>完整URL:</strong> ${fullUrl}
                `;
            } catch (error) {
                document.getElementById('urlResult').innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        };

        // SHA256测试
        window.testSHA256 = async function() {
            try {
                const input = document.getElementById('sha256Input').value;
                const hash = await generateSHA256(input);
                document.getElementById('sha256Result').innerHTML = `
                    <strong>输入:</strong> ${input}<br>
                    <strong>SHA256:</strong> ${hash}
                `;
            } catch (error) {
                document.getElementById('sha256Result').innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        };

        // AES解密测试
        window.testAESDecrypt = async function() {
            try {
                const password = document.getElementById('password').value;
                const ivHex = document.getElementById('iv').value;
                
                // 测试密钥生成
                const key = await deriveKeyFromPassword(password);
                const iv = hexToUint8Array(ivHex);
                
                document.getElementById('aesResult').innerHTML = `
                    <strong>密码:</strong> ${password}<br>
                    <strong>IV (hex):</strong> ${ivHex}<br>
                    <strong>IV长度:</strong> ${iv.length} 字节<br>
                    <strong>密钥生成:</strong> 成功<br>
                    <strong>说明:</strong> 密钥和IV准备就绪，可用于解密
                `;
            } catch (error) {
                document.getElementById('aesResult').innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        };
    </script>
</body>
</html>
