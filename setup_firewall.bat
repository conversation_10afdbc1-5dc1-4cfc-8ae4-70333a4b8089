@echo off
echo 正在设置防火墙规则...
echo 请以管理员身份运行此脚本

REM 添加入站规则
netsh advfirewall firewall add rule name="HLS Server 8081 In" dir=in action=allow protocol=TCP localport=8081
netsh advfirewall firewall add rule name="HLS Server 80 In" dir=in action=allow protocol=TCP localport=80

REM 添加出站规则
netsh advfirewall firewall add rule name="HLS Server 8081 Out" dir=out action=allow protocol=TCP localport=8081
netsh advfirewall firewall add rule name="HLS Server 80 Out" dir=out action=allow protocol=TCP localport=80

echo 防火墙规则设置完成
echo 现在可以通过以下地址访问:
echo http://192.168.31.93:8081/fixed_player.html
echo http://192.168.31.93/fixed_player.html (如果使用80端口)

pause
