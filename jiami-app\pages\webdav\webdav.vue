<template>
  <view class="webdav-container">
    <!-- 连接配置区域 -->
    <view v-if="!isConnected" class="config-section">
      <view class="config-header">
        <text class="config-title">WebDAV云盘连接</text>
        <text class="config-subtitle">请配置您的WebDAV服务器信息</text>
      </view>

      <view class="config-form">
        <view class="form-item">
          <text class="form-label">服务器地址</text>
          <input
            class="form-input"
            v-model="config.serverUrl"
            placeholder="https://cloud.example.com/remote.php/dav/files/username/"
            type="text"
          />
          <view class="form-help">
            <text class="help-text">常见格式示例：</text>
            <text class="help-example">• Nextcloud: https://your-domain.com/remote.php/dav/files/username/</text>
            <text class="help-example">• ownCloud: https://your-domain.com/remote.php/webdav/</text>
            <text class="help-example">• 通用WebDAV: https://your-domain.com/webdav/</text>
            <text class="help-example">• 本地服务器: http://*************:8080/webdav/</text>
            <button class="help-btn" @click="showServerTemplates">选择常用服务器模板</button>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label">用户名</text>
          <input 
            class="form-input" 
            v-model="config.username" 
            placeholder="请输入用户名"
            type="text"
          />
        </view>

        <view class="form-item">
          <text class="form-label">密码</text>
          <input 
            class="form-input" 
            v-model="config.password" 
            placeholder="请输入密码"
            type="password"
          />
        </view>

        <view class="form-item">
          <text class="form-label">根路径 (可选)</text>
          <input 
            class="form-input" 
            v-model="config.rootPath" 
            placeholder="/"
            type="text"
          />
        </view>

        <view class="form-actions">
          <button 
            class="connect-btn" 
            @click="connectToWebDAV"
            :disabled="connecting || !isConfigValid"
          >
            <text v-if="connecting">连接中...</text>
            <text v-else>连接</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 文件浏览区域 -->
    <view v-else class="file-browser">
      <!-- 工具栏 -->
      <view class="toolbar">
        <view class="path-bar">
          <text class="current-path">{{ currentPath }}</text>
        </view>
        <view class="toolbar-actions">
          <button class="action-btn" @click="showCreateFolderDialog">
            📁 新建文件夹
          </button>
          <button class="action-btn" @click="showUploadDialog">
            📤 上传文件
          </button>
          <button class="action-btn disconnect-btn" @click="disconnect">
            🔌 断开连接
          </button>
        </view>
      </view>

      <!-- 导航栏 -->
      <view class="breadcrumb" v-if="pathSegments.length > 1">
        <text 
          v-for="(segment, index) in pathSegments" 
          :key="index"
          class="breadcrumb-item"
          @click="navigateToPath(index)"
        >
          {{ segment.name }}
          <text v-if="index < pathSegments.length - 1" class="breadcrumb-separator">></text>
        </text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-section">
        <view class="spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 文件列表 -->
      <scroll-view v-else class="file-list" scroll-y="true">
        <!-- 返回上级目录 -->
        <view 
          v-if="currentPath !== '/'" 
          class="file-item directory-item"
          @click="goBack"
        >
          <view class="file-icon">📁</view>
          <view class="file-info">
            <text class="file-name">..</text>
            <text class="file-details">返回上级目录</text>
          </view>
        </view>

        <!-- 文件和文件夹列表 -->
        <view 
          v-for="file in fileList" 
          :key="file.path"
          class="file-item"
          :class="{ 'directory-item': file.isDirectory }"
          @click="handleFileClick(file)"
          @longpress="showFileActions(file)"
        >
          <view class="file-icon">
            <text v-if="file.isDirectory">📁</text>
            <text v-else>{{ getFileIcon(file.name) }}</text>
          </view>
          <view class="file-info">
            <text class="file-name">{{ file.name }}</text>
            <text class="file-details">
              <text v-if="!file.isDirectory">{{ formatFileSize(file.size) }}</text>
              <text class="file-date">{{ formatDate(file.lastModified) }}</text>
            </text>
          </view>
          <view class="file-actions" @click.stop="showFileActions(file)">
            <text class="action-icon">⋮</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="fileList.length === 0" class="empty-state">
          <text class="empty-icon">📂</text>
          <text class="empty-text">此文件夹为空</text>
        </view>
      </scroll-view>
    </view>

    <!-- 进度提示 -->
    <view v-if="showProgress" class="progress-overlay">
      <view class="progress-dialog">
        <text class="progress-title">{{ progressTitle }}</text>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
        </view>
        <text class="progress-text">{{ progressText }}</text>
        <button v-if="canCancelProgress" class="cancel-btn" @click="cancelOperation">
          取消
        </button>
      </view>
    </view>
  </view>
</template>

<script>
// WebDAV客户端实现 - 内联版本
class WebDAVClient {
  constructor(baseUrl, options = {}) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // 移除末尾斜杠
    this.user = options.user || ''
    this.password = options.password || ''
    this.timeout = options.timeout || 30000
    this.debug = options.debug || false

    // 设置认证头
    this.authHeader = (this.user && this.password) ?
      'Basic ' + btoa(this.user + ':' + this.password) : null
  }

  // 发送HTTP请求
  async _request(method, url, body = null, headers = {}) {
    const requestHeaders = {
      ...headers
    }

    // 添加认证头
    if (this.authHeader) {
      requestHeaders['Authorization'] = this.authHeader
    }

    if (this.debug) {
      console.log(`WebDAV Request: ${method} ${url}`, { headers: requestHeaders, body })
    }

    return new Promise((resolve, reject) => {
      const requestConfig = {
        url,
        method,
        header: requestHeaders,
        timeout: this.timeout,
        success: (res) => {
          if (this.debug) {
            console.log(`WebDAV Response: ${res.statusCode}`, res)
          }

          // 创建兼容的Response对象
          const response = {
            status: res.statusCode,
            statusText: res.statusCode === 200 ? 'OK' : 'Error',
            ok: res.statusCode >= 200 && res.statusCode < 300,
            headers: {
              get: (name) => {
                const lowerName = name.toLowerCase()
                for (const key in res.header) {
                  if (key.toLowerCase() === lowerName) {
                    return res.header[key]
                  }
                }
                return null
              }
            },
            text: () => Promise.resolve(typeof res.data === 'string' ? res.data : JSON.stringify(res.data)),
            arrayBuffer: () => {
              if (res.data instanceof ArrayBuffer) {
                return Promise.resolve(res.data)
              } else if (typeof res.data === 'string') {
                const encoder = new TextEncoder()
                return Promise.resolve(encoder.encode(res.data).buffer)
              } else {
                return Promise.resolve(new ArrayBuffer(0))
              }
            }
          }

          resolve(response)
        },
        fail: (error) => {
          if (this.debug) {
            console.error('WebDAV Request failed:', error)
          }
          reject(new Error(`Request failed: ${error.errMsg || error.message}`))
        }
      }

      // 根据请求类型设置数据
      if (body) {
        requestConfig.data = body
      }

      uni.request(requestConfig)
    })
  }

  // 测试连接 - 简化版本
  async ping() {
    try {
      // 直接测试根路径的PROPFIND
      const response = await this._request('PROPFIND', this.baseUrl + '/', `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
        'Content-Type': 'application/xml; charset=utf-8',
        'Depth': '0'
      })

      return response.status === 207 || response.status === 200
    } catch (error) {
      if (this.debug) console.error('Ping failed:', error)
      return false
    }
  }

  // 读取目录内容
  async readDir(path = '/') {
    const normalizedPath = this._normalizePath(path)

    const propfindXml = `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:displayname/>
    <D:getcontentlength/>
    <D:getcontenttype/>
    <D:getlastmodified/>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`

    try {
      const response = await this._request('PROPFIND', this.baseUrl + normalizedPath, propfindXml, {
        'Content-Type': 'application/xml; charset=utf-8',
        'Depth': '1'
      })

      if (response.status === 207 || response.status === 200) {
        const xmlText = await response.text()
        return this._parseMultiStatus(xmlText, normalizedPath)
      } else {
        if (this.debug) {
          console.log(`ReadDir failed with status ${response.status}`)
          const errorText = await response.text()
          console.log('Response:', errorText)
        }
        throw new Error(`Failed to read directory: ${response.status}`)
      }
    } catch (error) {
      if (this.debug) console.error('ReadDir failed:', error)
      throw error
    }
  }

  // 规范化路径
  _normalizePath(path) {
    if (!path.startsWith('/')) {
      path = '/' + path
    }
    path = path.replace(/\/+/g, '/')

    const pathParts = path.split('/')
    const encodedParts = pathParts.map(part => {
      if (part === '') return part
      return encodeURIComponent(part)
    })

    return encodedParts.join('/')
  }

  // 解析多状态XML响应
  _parseMultiStatus(xmlText, basePath) {
    const files = []

    try {
      const responseRegex = /<D:response[^>]*>([\s\S]*?)<\/D:response>/gi
      let match

      while ((match = responseRegex.exec(xmlText)) !== null) {
        const responseXml = match[1]
        const file = this._parseFileFromResponse(responseXml, basePath)
        if (file && file.path !== basePath) {
          files.push(file)
        }
      }
    } catch (error) {
      if (this.debug) console.error('XML parsing failed:', error)
    }

    return files
  }

  // 从响应中解析文件信息
  _parseFileFromResponse(responseXml, basePath) {
    try {
      const hrefMatch = responseXml.match(/<D:href[^>]*>([^<]+)<\/D:href>/i)
      if (!hrefMatch) return null

      let path = decodeURIComponent(hrefMatch[1])
      if (path.startsWith(this.baseUrl)) {
        path = path.substring(this.baseUrl.length)
      }

      const name = path.split('/').filter(p => p).pop() || ''
      const isDirectory = responseXml.includes('<D:collection') || responseXml.includes('<D:resourcetype><D:collection/></D:resourcetype>')

      const sizeMatch = responseXml.match(/<D:getcontentlength[^>]*>([^<]+)<\/D:getcontentlength>/i)
      const size = sizeMatch ? parseInt(sizeMatch[1]) : 0

      const typeMatch = responseXml.match(/<D:getcontenttype[^>]*>([^<]+)<\/D:getcontenttype>/i)
      const contentType = typeMatch ? typeMatch[1] : ''

      const modifiedMatch = responseXml.match(/<D:getlastmodified[^>]*>([^<]+)<\/D:getlastmodified>/i)
      const lastModified = modifiedMatch ? new Date(modifiedMatch[1]) : new Date()

      return {
        name,
        path,
        size,
        isDirectory,
        lastModified,
        contentType
      }
    } catch (error) {
      if (this.debug) console.error('File parsing failed:', error)
      return null
    }
  }

  // 创建目录
  async mkdir(path) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('MKCOL', this.baseUrl + normalizedPath)
      if (response.status !== 201 && response.status !== 405) {
        throw new Error(`Failed to create directory: ${response.status}`)
      }
      return true
    } catch (error) {
      if (this.debug) console.error('Mkdir failed:', error)
      throw error
    }
  }

  // 删除文件或目录
  async remove(path) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('DELETE', this.baseUrl + normalizedPath)
      if (response.status !== 200 && response.status !== 204 && response.status !== 404) {
        throw new Error(`Failed to remove: ${response.status}`)
      }
      return true
    } catch (error) {
      if (this.debug) console.error('Remove failed:', error)
      throw error
    }
  }

  // 重命名/移动文件或目录
  async rename(oldPath, newPath, overwrite = true) {
    const normalizedOldPath = this._normalizePath(oldPath)
    const normalizedNewPath = this._normalizePath(newPath)
    const destination = this.baseUrl + normalizedNewPath

    try {
      const response = await this._request('MOVE', this.baseUrl + normalizedOldPath, null, {
        'Destination': destination,
        'Overwrite': overwrite ? 'T' : 'F'
      })

      if (response.status !== 201 && response.status !== 204) {
        throw new Error(`Failed to rename: ${response.status}`)
      }
      return true
    } catch (error) {
      if (this.debug) console.error('Rename failed:', error)
      throw error
    }
  }

  // 下载文件
  async downloadFile(path, onProgress = null) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('GET', this.baseUrl + normalizedPath)
      if (response.status !== 200) {
        throw new Error(`Failed to download file: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()

      if (onProgress) {
        onProgress(arrayBuffer.byteLength, arrayBuffer.byteLength)
      }

      return new Uint8Array(arrayBuffer)
    } catch (error) {
      if (this.debug) console.error('Download failed:', error)
      throw error
    }
  }

  // 上传文件
  async uploadFile(path, fileData, onProgress = null) {
    const normalizedPath = this._normalizePath(path)
    try {
      const response = await this._request('PUT', this.baseUrl + normalizedPath, fileData, {
        'Content-Type': 'application/octet-stream'
      })

      if (response.status !== 200 && response.status !== 201 && response.status !== 204) {
        throw new Error(`Failed to upload file: ${response.status}`)
      }

      if (onProgress) {
        const size = fileData.byteLength || fileData.length || 0
        onProgress(size, size)
      }

      return true
    } catch (error) {
      if (this.debug) console.error('Upload failed:', error)
      throw error
    }
  }

  // 测试路径是否存在
  async pathExists(path) {
    try {
      const normalizedPath = this._normalizePath(path)
      const response = await this._request('PROPFIND', this.baseUrl + normalizedPath, `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
        'Content-Type': 'application/xml; charset=utf-8',
        'Depth': '0'
      })

      return response.status === 207 || response.status === 200
    } catch (error) {
      return false
    }
  }
}

export default {
  name: 'WebDAV',
  data() {
    return {
      // 连接配置
      config: {
        serverUrl: '',
        username: '',
        password: '',
        rootPath: '/'
      },
      
      // 连接状态
      isConnected: false,
      connecting: false,
      webdavClient: null,
      
      // 文件浏览
      currentPath: '/',
      fileList: [],
      loading: false,
      
      // 进度显示
      showProgress: false,
      progressTitle: '',
      progressText: '',
      progressPercent: 0,
      canCancelProgress: false,
      
      // 其他状态
      selectedFile: null
    }
  },
  
  computed: {
    // 检查配置是否有效
    isConfigValid() {
      return this.config.serverUrl.trim() && 
             this.config.username.trim() && 
             this.config.password.trim()
    },
    
    // 路径分段
    pathSegments() {
      const segments = [{ name: '根目录', path: '/' }]
      if (this.currentPath !== '/') {
        const parts = this.currentPath.split('/').filter(p => p)
        let currentPath = ''
        parts.forEach(part => {
          currentPath += '/' + part
          segments.push({ name: part, path: currentPath })
        })
      }
      return segments
    }
  },
  
  onLoad() {
    console.log('WebDAV页面加载')
    this.loadSavedConfig()
    this.checkNetworkStatus()
  },

  onShow() {
    // 页面显示时检查网络状态
    this.checkNetworkStatus()
  },
  
  methods: {
    // 加载保存的配置
    loadSavedConfig() {
      try {
        const savedConfig = uni.getStorageSync('webdav_config')
        if (savedConfig) {
          this.config = { ...this.config, ...savedConfig }
        }
      } catch (error) {
        console.error('加载配置失败:', error)
      }
    },
    
    // 保存配置
    saveConfig() {
      try {
        uni.setStorageSync('webdav_config', this.config)
      } catch (error) {
        console.error('保存配置失败:', error)
      }
    },
    
    // 连接到WebDAV服务器
    async connectToWebDAV() {
      if (!this.isConfigValid) {
        uni.showToast({
          title: '请填写完整的连接信息',
          icon: 'none'
        })
        return
      }

      // 验证URL格式
      const urlPattern = /^https?:\/\/.+/i
      if (!urlPattern.test(this.config.serverUrl.trim())) {
        uni.showModal({
          title: '服务器地址格式错误',
          content: '请输入正确的服务器地址格式：\n\n示例：\n• https://cloud.example.com/remote.php/dav/files/username/\n• http://*************:8080/webdav/\n• https://your-domain.com/webdav\n\n注意：路径中的中文字符会自动进行URL编码',
          showCancel: false,
          confirmText: '我知道了'
        })
        return
      }

      this.connecting = true

      try {
        // 创建WebDAV客户端
        this.webdavClient = new WebDAVClient(this.config.serverUrl, {
          user: this.config.username,
          password: this.config.password,
          debug: true
        })

        // 测试Basic认证编码
        const testAuth = btoa(`${this.config.username}:${this.config.password}`)
        console.log('Expected Basic auth:', `Basic ${testAuth}`)
        console.log('WebDAV Base URL:', this.config.serverUrl)
        console.log('Root Path:', this.config.rootPath || '/')

        let connectionSuccessful = false
        let actualRootPath = '/'

        try {
          // 严格按照Cryptomator实现：先测试服务器根路径
          console.log('Testing WebDAV server root path...')

          // 创建一个简单的PROPFIND请求测试根路径
          const rootTestResponse = await this.webdavClient._request('PROPFIND', this.webdavClient.baseUrl + '/', `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
            'Content-Type': 'application/xml; charset=utf-8',
            'Depth': '0'
          })

          if (rootTestResponse.status === 207 || rootTestResponse.status === 200) {
            console.log('WebDAV server root accessible')

            // 然后测试用户指定的路径
            const userPath = this.config.rootPath || '/'
            if (userPath !== '/') {
              console.log('Testing user specified path:', userPath)
              try {
                const userPathResponse = await this.webdavClient._request('PROPFIND', this.webdavClient.baseUrl + this.webdavClient._normalizePath(userPath), `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
                  'Content-Type': 'application/xml; charset=utf-8',
                  'Depth': '0'
                })

                if (userPathResponse.status === 207 || userPathResponse.status === 200) {
                  actualRootPath = userPath
                  console.log('User specified path accessible')
                } else {
                  console.log('User specified path not accessible, status:', userPathResponse.status)
                  uni.showModal({
                    title: '路径提示',
                    content: `指定的路径 "${userPath}" 不存在或无权限访问，将使用服务器根路径。`,
                    showCancel: false,
                    confirmText: '确定'
                  })
                }
              } catch (pathError) {
                console.log('User specified path test failed:', pathError.message)
                uni.showModal({
                  title: '路径提示',
                  content: `指定的路径 "${userPath}" 不存在或无权限访问，将使用服务器根路径。`,
                  showCancel: false,
                  confirmText: '确定'
                })
              }
            }

            connectionSuccessful = true
          } else {
            throw new Error(`WebDAV server root not accessible: ${rootTestResponse.status}`)
          }
        } catch (error) {
          console.log('WebDAV connection test failed:', error.message)
          throw error
        }

        if (connectionSuccessful) {
          // 保存配置
          this.saveConfig()

          // 设置连接状态
          this.isConnected = true
          this.currentPath = actualRootPath

          // 加载文件列表
          await this.loadFileList()

          uni.showToast({
            title: '连接成功',
            icon: 'success'
          })
        }

      } catch (error) {
        console.error('连接失败:', error)
        let errorMessage = '连接失败'

        if (error.message.includes('401') || error.message.includes('Authentication failed')) {
          errorMessage = '用户名或密码错误，请检查认证信息'
        } else if (error.message.includes('403')) {
          errorMessage = '访问被拒绝，请检查：\n1. 用户名密码是否正确\n2. 账户是否有WebDAV权限\n3. 服务器路径是否正确'
        } else if (error.message.includes('404')) {
          errorMessage = 'WebDAV路径不存在，请检查：\n1. 服务器地址是否正确\n2. 是否包含正确的WebDAV路径\n3. 服务器是否启用WebDAV'
        } else if (error.message.includes('timeout')) {
          errorMessage = '连接超时，请检查网络连接'
        } else if (error.message.includes('network')) {
          errorMessage = '网络连接失败'
        } else {
          errorMessage = error.message || '未知错误'
        }

        uni.showModal({
          title: '连接失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '确定'
        })
      } finally {
        this.connecting = false
      }
    },
    
    // 断开连接
    disconnect() {
      this.isConnected = false
      this.webdavClient = null
      this.fileList = []
      this.currentPath = '/'
      
      uni.showToast({
        title: '已断开连接',
        icon: 'none'
      })
    },
    
    // 加载文件列表
    async loadFileList() {
      if (!this.webdavClient) return

      this.loading = true

      try {
        const files = await this.webdavClient.readDir(this.currentPath)
        this.fileList = files.sort((a, b) => {
          // 文件夹排在前面
          if (a.isDirectory && !b.isDirectory) return -1
          if (!a.isDirectory && b.isDirectory) return 1
          // 按名称排序
          return a.name.localeCompare(b.name)
        })

        // 如果文件列表为空且不是根目录，显示提示
        if (this.fileList.length === 0 && this.currentPath !== '/') {
          console.log('当前目录为空:', this.currentPath)
        }

      } catch (error) {
        console.error('加载文件列表失败:', error)

        let errorMessage = '加载失败'
        if (error.message.includes('403')) {
          errorMessage = '没有访问权限'
        } else if (error.message.includes('404')) {
          errorMessage = '目录不存在'
        } else if (error.message.includes('timeout')) {
          errorMessage = '请求超时'
        } else {
          errorMessage = error.message || '未知错误'
        }

        uni.showModal({
          title: '加载失败',
          content: errorMessage,
          showCancel: true,
          cancelText: '返回上级',
          confirmText: '重试',
          success: (res) => {
            if (res.confirm) {
              // 重试
              setTimeout(() => this.loadFileList(), 1000)
            } else if (res.cancel && this.currentPath !== '/') {
              // 返回上级目录
              this.goBack()
            }
          }
        })
      } finally {
        this.loading = false
      }
    },
    
    // 处理文件点击
    handleFileClick(file) {
      if (file.isDirectory) {
        this.currentPath = file.path
        this.loadFileList()
      } else {
        this.showFileActions(file)
      }
    },
    
    // 返回上级目录
    goBack() {
      const pathParts = this.currentPath.split('/').filter(p => p)
      if (pathParts.length > 0) {
        pathParts.pop()
        this.currentPath = '/' + pathParts.join('/')
      } else {
        this.currentPath = '/'
      }
      this.loadFileList()
    },
    
    // 导航到指定路径
    navigateToPath(segmentIndex) {
      if (segmentIndex === 0) {
        this.currentPath = '/'
      } else {
        const segments = this.pathSegments.slice(1, segmentIndex + 1)
        this.currentPath = '/' + segments.map(s => s.name).join('/')
      }
      this.loadFileList()
    },
    
    // 显示文件操作菜单
    showFileActions(file) {
      const actions = []
      
      if (!file.isDirectory) {
        actions.push('下载')
      }
      
      actions.push('重命名', '删除')
      
      uni.showActionSheet({
        itemList: actions,
        success: (res) => {
          const action = actions[res.tapIndex]
          this.handleFileAction(file, action)
        }
      })
    },
    
    // 处理文件操作
    async handleFileAction(file, action) {
      switch (action) {
        case '下载':
          await this.downloadFile(file)
          break
        case '重命名':
          this.showRenameDialog(file)
          break
        case '删除':
          this.confirmDelete(file)
          break
      }
    },
    
    // 下载文件
    async downloadFile(file) {
      this.showProgressDialog('下载文件', '准备下载...')

      try {
        const fileData = await this.webdavClient.downloadFile(file.path, (received, total) => {
          if (total > 0) {
            this.updateProgress(Math.round((received / total) * 100),
              `${this.formatFileSize(received)} / ${this.formatFileSize(total)}`)
          }
        })

        // 保存文件到本地
        await this.saveFileToLocal(file.name, fileData)

        this.hideProgressDialog()
        uni.showToast({
          title: '下载完成',
          icon: 'success'
        })

      } catch (error) {
        this.hideProgressDialog()
        console.error('下载失败:', error)
        uni.showToast({
          title: `下载失败: ${error.message}`,
          icon: 'none'
        })
      }
    },

    // 保存文件到本地
    async saveFileToLocal(fileName, fileData) {
      return new Promise((resolve, reject) => {
        // #ifdef APP-PLUS
        // App端保存到下载目录
        const downloadPath = plus.io.convertLocalFileSystemURL('_downloads/') + fileName
        plus.io.requestFileSystem(plus.io.PUBLIC_DOWNLOADS, (fs) => {
          fs.root.getFile(fileName, { create: true }, (fileEntry) => {
            fileEntry.createWriter((writer) => {
              writer.onwriteend = () => {
                console.log('文件保存成功:', downloadPath)
                resolve(downloadPath)
              }
              writer.onerror = reject
              writer.write(fileData)
            }, reject)
          }, reject)
        }, reject)
        // #endif

        // #ifndef APP-PLUS
        // 非App端保存到临时目录
        const fs = uni.getFileSystemManager()
        const tempPath = `${uni.env.USER_DATA_PATH}/${fileName}`

        fs.writeFile({
          filePath: tempPath,
          data: fileData,
          success: () => {
            console.log('文件保存成功:', tempPath)
            resolve(tempPath)
          },
          fail: reject
        })
        // #endif
      })
    },
    
    // 显示创建文件夹对话框
    showCreateFolderDialog() {
      uni.showModal({
        title: '新建文件夹',
        editable: true,
        placeholderText: '请输入文件夹名称',
        success: async (res) => {
          if (res.confirm && res.content) {
            await this.createFolder(res.content.trim())
          }
        }
      })
    },
    
    // 创建文件夹
    async createFolder(folderName) {
      if (!folderName) return

      // 验证文件夹名称
      if (!/^[^<>:"/\\|?*]+$/.test(folderName)) {
        uni.showToast({
          title: '文件夹名称包含非法字符',
          icon: 'none'
        })
        return
      }

      const folderPath = this.currentPath + (this.currentPath.endsWith('/') ? '' : '/') + folderName

      // 显示加载状态
      uni.showLoading({
        title: '创建中...'
      })

      try {
        await this.webdavClient.mkdir(folderPath)
        await this.loadFileList()

        uni.hideLoading()
        uni.showToast({
          title: '文件夹创建成功',
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        console.error('创建文件夹失败:', error)

        let errorMessage = '创建失败'
        if (error.message.includes('409')) {
          errorMessage = '文件夹已存在'
        } else if (error.message.includes('403')) {
          errorMessage = '没有创建权限'
        } else {
          errorMessage = error.message || '未知错误'
        }

        uni.showModal({
          title: '创建失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '确定'
        })
      }
    },
    
    // 显示上传对话框
    showUploadDialog() {
      // #ifdef APP-PLUS
      // App端使用plus.io选择文件
      plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS, (fs) => {
        fs.root.createReader().readEntries((entries) => {
          const fileList = entries.filter(entry => !entry.isDirectory).map(entry => entry.name)
          if (fileList.length === 0) {
            uni.showToast({
              title: '没有找到可上传的文件',
              icon: 'none'
            })
            return
          }

          uni.showActionSheet({
            itemList: fileList,
            success: async (res) => {
              const selectedFile = entries[res.tapIndex]
              await this.uploadFileFromPath(selectedFile.fullPath, selectedFile.name)
            }
          })
        })
      })
      // #endif

      // #ifndef APP-PLUS
      // 非App端使用uni.chooseImage或uni.chooseVideo
      uni.showActionSheet({
        itemList: ['选择图片', '选择视频'],
        success: async (res) => {
          if (res.tapIndex === 0) {
            uni.chooseImage({
              count: 1,
              success: async (imageRes) => {
                if (imageRes.tempFilePaths && imageRes.tempFilePaths.length > 0) {
                  const filePath = imageRes.tempFilePaths[0]
                  const fileName = 'image_' + Date.now() + '.jpg'
                  await this.uploadFileFromPath(filePath, fileName)
                }
              }
            })
          } else {
            uni.chooseVideo({
              count: 1,
              success: async (videoRes) => {
                if (videoRes.tempFilePath) {
                  const fileName = 'video_' + Date.now() + '.mp4'
                  await this.uploadFileFromPath(videoRes.tempFilePath, fileName)
                }
              }
            })
          }
        }
      })
      // #endif
    },
    
    // 从文件路径上传文件
    async uploadFileFromPath(filePath, fileName) {
      const remotePath = this.currentPath + (this.currentPath.endsWith('/') ? '' : '/') + fileName

      this.showProgressDialog('上传文件', '准备上传...')

      try {
        // 读取文件数据
        const fileData = await this.readFileData(filePath)

        await this.webdavClient.uploadFile(remotePath, fileData, (sent, total) => {
          if (total > 0) {
            this.updateProgress(Math.round((sent / total) * 100),
              `${this.formatFileSize(sent)} / ${this.formatFileSize(total)}`)
          }
        })

        await this.loadFileList()
        this.hideProgressDialog()

        uni.showToast({
          title: '上传完成',
          icon: 'success'
        })

      } catch (error) {
        this.hideProgressDialog()
        console.error('上传失败:', error)
        uni.showToast({
          title: `上传失败: ${error.message}`,
          icon: 'none'
        })
      }
    },
    
    // 读取文件数据
    async readFileData(filePath) {
      return new Promise((resolve, reject) => {
        // #ifdef APP-PLUS
        // App端使用plus.io读取文件
        plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
          entry.file((file) => {
            const reader = new plus.io.FileReader()
            reader.onloadend = (e) => {
              resolve(e.target.result)
            }
            reader.onerror = reject
            reader.readAsArrayBuffer(file)
          }, reject)
        }, reject)
        // #endif

        // #ifndef APP-PLUS
        // 非App端使用uni.getFileSystemManager
        const fs = uni.getFileSystemManager()
        fs.readFile({
          filePath,
          success: (res) => {
            // 确保返回ArrayBuffer
            if (res.data instanceof ArrayBuffer) {
              resolve(res.data)
            } else if (typeof res.data === 'string') {
              // 如果是base64字符串，转换为ArrayBuffer
              const binaryString = atob(res.data)
              const bytes = new Uint8Array(binaryString.length)
              for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i)
              }
              resolve(bytes.buffer)
            } else {
              resolve(res.data)
            }
          },
          fail: reject
        })
        // #endif
      })
    },
    
    // 显示重命名对话框
    showRenameDialog(file) {
      uni.showModal({
        title: '重命名',
        editable: true,
        content: file.name,
        success: async (res) => {
          if (res.confirm && res.content && res.content !== file.name) {
            await this.renameFile(file, res.content.trim())
          }
        }
      })
    },
    
    // 重命名文件
    async renameFile(file, newName) {
      if (!newName) return
      
      const newPath = file.path.substring(0, file.path.lastIndexOf('/') + 1) + newName
      
      try {
        await this.webdavClient.rename(file.path, newPath)
        await this.loadFileList()
        
        uni.showToast({
          title: '重命名成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('重命名失败:', error)
        uni.showToast({
          title: `重命名失败: ${error.message}`,
          icon: 'none'
        })
      }
    },
    
    // 确认删除
    confirmDelete(file) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除 "${file.name}" 吗？`,
        success: async (res) => {
          if (res.confirm) {
            await this.deleteFile(file)
          }
        }
      })
    },
    
    // 删除文件
    async deleteFile(file) {
      try {
        await this.webdavClient.remove(file.path)
        await this.loadFileList()
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('删除失败:', error)
        uni.showToast({
          title: `删除失败: ${error.message}`,
          icon: 'none'
        })
      }
    },
    
    // 显示进度对话框
    showProgressDialog(title, text) {
      this.showProgress = true
      this.progressTitle = title
      this.progressText = text
      this.progressPercent = 0
      this.canCancelProgress = false
    },
    
    // 更新进度
    updateProgress(percent, text) {
      this.progressPercent = percent
      this.progressText = text
    },
    
    // 隐藏进度对话框
    hideProgressDialog() {
      this.showProgress = false
    },
    
    // 取消操作
    cancelOperation() {
      // 实现取消逻辑
      this.hideProgressDialog()
    },
    
    // 获取文件图标
    getFileIcon(fileName) {
      const ext = fileName.split('.').pop()?.toLowerCase()
      const iconMap = {
        'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
        'mp4': '🎬', 'avi': '🎬', 'mov': '🎬', 'wmv': '🎬', 'flv': '🎬',
        'mp3': '🎵', 'wav': '🎵', 'flac': '🎵', 'aac': '🎵',
        'pdf': '📄', 'doc': '📄', 'docx': '📄', 'txt': '📄',
        'zip': '📦', 'rar': '📦', '7z': '📦', 'tar': '📦',
        'js': '📜', 'html': '📜', 'css': '📜', 'json': '📜'
      }
      return iconMap[ext] || '📄'
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.toLocaleDateString() + ' ' + d.toLocaleTimeString()
    },

    // 检查网络状态
    checkNetworkStatus() {
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            uni.showToast({
              title: '网络连接不可用',
              icon: 'none',
              duration: 3000
            })
          }
        }
      })
    },

    // 显示操作确认对话框
    showConfirmDialog(title, content) {
      return new Promise((resolve) => {
        uni.showModal({
          title,
          content,
          success: (res) => {
            resolve(res.confirm)
          }
        })
      })
    },

    // 显示输入对话框
    showInputDialog(title, placeholder = '', defaultValue = '') {
      return new Promise((resolve) => {
        uni.showModal({
          title,
          editable: true,
          placeholderText: placeholder,
          content: defaultValue,
          success: (res) => {
            if (res.confirm) {
              resolve(res.content?.trim() || '')
            } else {
              resolve(null)
            }
          }
        })
      })
    },

    // 重试操作
    async retryOperation(operation, maxRetries = 3) {
      let retries = 0
      while (retries < maxRetries) {
        try {
          return await operation()
        } catch (error) {
          retries++
          if (retries >= maxRetries) {
            throw error
          }

          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * retries))
        }
      }
    },

    // 显示服务器模板选择
    showServerTemplates() {
      const templates = [
        'Nextcloud (需要替换域名和用户名)',
        'ownCloud (需要替换域名)',
        '通用WebDAV (需要替换域名)',
        '本地服务器 (需要替换IP)',
        '自定义输入'
      ]

      uni.showActionSheet({
        itemList: templates,
        success: (res) => {
          const templateUrls = [
            'https://your-domain.com/remote.php/dav/files/username/',
            'https://your-domain.com/remote.php/webdav/',
            'https://your-domain.com/webdav/',
            'http://*************:8080/webdav/',
            ''
          ]

          if (res.tapIndex < templateUrls.length - 1) {
            this.config.serverUrl = templateUrls[res.tapIndex]
            uni.showToast({
              title: '已填入模板，请修改为实际地址',
              icon: 'none',
              duration: 3000
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.webdav-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 配置区域样式 */
.config-section {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.config-header {
  text-align: center;
  margin-bottom: 30px;
}

.config-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.config-subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.config-form {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 16px;
  background: #fff;
}

.form-input:focus {
  border-color: #007AFF;
  outline: none;
}

.form-actions {
  margin-top: 30px;
}

.connect-btn {
  width: 100%;
  height: 48px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}

.connect-btn:disabled {
  background: #ccc;
}

.form-help {
  margin-top: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007AFF;
}

.help-text {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}

.help-example {
  display: block;
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
  font-family: monospace;
  word-break: break-all;
}

.help-btn {
  margin-top: 8px;
  padding: 6px 12px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
}

/* 文件浏览区域样式 */
.file-browser {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.toolbar {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.path-bar {
  flex: 1;
  min-width: 0;
}

.current-path {
  font-size: 14px;
  color: #666;
  word-break: break-all;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
}

.disconnect-btn {
  background: #ff3b30;
}

.breadcrumb {
  background: #f8f9fa;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.breadcrumb-item {
  font-size: 14px;
  color: #007AFF;
  cursor: pointer;
  margin-right: 4px;
}

.breadcrumb-separator {
  color: #999;
  margin: 0 4px;
}

.loading-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.file-list {
  flex: 1;
  background: white;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.file-item:active {
  background: #f8f9fa;
}

.directory-item {
  background: #f8f9ff;
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  display: block;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  word-break: break-all;
  margin-bottom: 4px;
}

.file-details {
  display: block;
  font-size: 12px;
  color: #999;
}

.file-date {
  margin-left: 8px;
}

.file-actions {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-icon {
  font-size: 16px;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

/* 进度对话框样式 */
.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.progress-dialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin: 20px;
  min-width: 280px;
  max-width: 90%;
}

.progress-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 16px;
  display: block;
}

.progress-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: #007AFF;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
  text-align: center;
  display: block;
  margin-bottom: 16px;
}

.cancel-btn {
  width: 100%;
  height: 40px;
  background: #ff3b30;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
}
</style>
