<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AES-256-GCM 图片加密器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .upload-area.dragover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
        }
        
        #fileInput {
            display: none;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .key-section {
            margin-bottom: 30px;
        }
        
        .key-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1.1em;
            margin-bottom: 15px;
            transition: border-color 0.3s ease;
        }
        
        .key-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .results {
            margin-top: 30px;
        }
        
        .file-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: bold;
            color: #333;
        }
        
        .file-size {
            color: #666;
            font-size: 0.9em;
        }
        
        .download-btn {
            background: #28a745;
            padding: 8px 16px;
            font-size: 0.9em;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .performance-stats {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }

        .performance-stats h4 {
            color: #1976d2;
            margin-bottom: 10px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #1976d2;
        }

        .file-type-selector {
            text-align: center;
            margin-bottom: 20px;
        }

        .mode-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background: #ddd;
            color: #666;
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .encryption-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-size: 0.9em;
            color: #856404;
        }

        .file-size-warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-size: 0.9em;
            color: #721c24;
            display: none;
        }

        .memory-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-size: 0.9em;
            color: #0c5460;
        }

        /* 队列管理样式 */
        .queue-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }

        .queue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .queue-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .queue-controls {
            display: flex;
            gap: 10px;
        }

        .queue-control-btn {
            padding: 8px 16px;
            font-size: 0.9em;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .queue-control-btn.pause {
            background: #ffc107;
            color: #212529;
        }

        .queue-control-btn.resume {
            background: #28a745;
            color: white;
        }

        .queue-control-btn.clear {
            background: #dc3545;
            color: white;
        }

        .queue-control-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .queue-settings {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .queue-setting {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .queue-setting label {
            font-size: 0.9em;
            color: #666;
            white-space: nowrap;
        }

        .queue-setting input, .queue-setting select {
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .queue-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .queue-stat {
            background: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .queue-stat-label {
            font-size: 0.8em;
            color: #666;
            margin-bottom: 5px;
        }

        .queue-stat-value {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
        }

        .queue-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: white;
        }

        .queue-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #f1f3f4;
            transition: background-color 0.2s ease;
        }

        .queue-item:hover {
            background-color: #f8f9fa;
        }

        .queue-item:last-child {
            border-bottom: none;
        }

        .queue-item-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .queue-item-status.waiting {
            background: #6c757d;
        }

        .queue-item-status.processing {
            background: #007bff;
            animation: pulse 1.5s infinite;
        }

        .queue-item-status.completed {
            background: #28a745;
        }

        .queue-item-status.failed {
            background: #dc3545;
        }

        .queue-item-status.paused {
            background: #ffc107;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .queue-item-info {
            flex: 1;
            min-width: 0;
        }

        .queue-item-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .queue-item-details {
            font-size: 0.85em;
            color: #666;
            display: flex;
            gap: 15px;
        }

        .queue-item-progress {
            width: 100px;
            margin: 0 12px;
            flex-shrink: 0;
        }

        .queue-item-progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .queue-item-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .queue-item-progress-text {
            font-size: 0.75em;
            color: #666;
            text-align: center;
            margin-top: 2px;
        }

        .queue-item-actions {
            display: flex;
            gap: 5px;
            flex-shrink: 0;
        }

        .queue-item-btn {
            padding: 4px 8px;
            font-size: 0.75em;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .queue-item-btn.priority {
            background: #17a2b8;
            color: white;
        }

        .queue-item-btn.remove {
            background: #dc3545;
            color: white;
        }

        .queue-item-btn:hover {
            opacity: 0.8;
        }

        .queue-overall-progress {
            margin: 15px 0;
        }

        .queue-overall-progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #666;
        }

        .queue-overall-progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .queue-overall-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 AES-256-GCM 图片加密器</h1>
        
        <div class="key-section">
            <input type="password" id="encryptionKey" class="key-input" placeholder="请输入加密密钥（至少8位字符）" />
            <button id="generateKey" class="btn">生成随机密钥</button>
        </div>
        
        <div class="file-type-selector">
            <button id="imageMode" class="btn mode-btn active">📷 图片模式</button>
            <button id="videoMode" class="btn mode-btn">🎬 视频模式</button>
        </div>

        <div class="upload-area" id="uploadArea">
            <div class="upload-text" id="uploadText">
                <strong>点击选择图片文件或拖拽到此处</strong><br>
                支持批量上传 JPG, PNG, GIF, WebP 格式
            </div>
            <input type="file" id="fileInput" multiple accept="image/*">
        </div>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">准备中...</div>
        </div>
        
        <div id="status"></div>

        <div class="encryption-info" id="encryptionInfo">
            📷 图片模式：使用 AES-256-GCM 加密，提供最高安全级别
        </div>

        <div class="file-size-warning" id="fileSizeWarning">
            ⚠️ 注意：支持最大10GB文件，大文件将自动使用高性能分块处理
        </div>

        <div class="memory-info" id="memoryInfo" style="display: none;">
            <div id="memoryStatus"></div>
        </div>

        <!-- 队列管理区域 -->
        <div class="queue-section" id="queueSection" style="display: none;">
            <div class="queue-header">
                <div class="queue-title">🔄 加密队列</div>
                <div class="queue-controls">
                    <button id="pauseQueueBtn" class="queue-control-btn pause">⏸️ 暂停</button>
                    <button id="resumeQueueBtn" class="queue-control-btn resume" style="display: none;">▶️ 继续</button>
                    <button id="clearQueueBtn" class="queue-control-btn clear">🗑️ 清空</button>
                </div>
            </div>

            <div class="queue-settings">
                <div class="queue-setting">
                    <label for="concurrentLimit">并发数量:</label>
                    <input type="number" id="concurrentLimit" min="1" max="5" value="2">
                </div>
                <div class="queue-setting">
                    <label for="priorityMode">优先级:</label>
                    <select id="priorityMode">
                        <option value="fifo">先进先出</option>
                        <option value="size-asc">小文件优先</option>
                        <option value="size-desc">大文件优先</option>
                    </select>
                </div>
                <div class="queue-setting">
                    <label>
                        <input type="checkbox" id="autoPause"> 错误时自动暂停
                    </label>
                </div>
            </div>

            <div class="queue-stats">
                <div class="queue-stat">
                    <div class="queue-stat-label">队列总数</div>
                    <div class="queue-stat-value" id="queueTotal">0</div>
                </div>
                <div class="queue-stat">
                    <div class="queue-stat-label">等待中</div>
                    <div class="queue-stat-value" id="queueWaiting">0</div>
                </div>
                <div class="queue-stat">
                    <div class="queue-stat-label">处理中</div>
                    <div class="queue-stat-value" id="queueProcessing">0</div>
                </div>
                <div class="queue-stat">
                    <div class="queue-stat-label">已完成</div>
                    <div class="queue-stat-value" id="queueCompleted">0</div>
                </div>
                <div class="queue-stat">
                    <div class="queue-stat-label">失败</div>
                    <div class="queue-stat-value" id="queueFailed">0</div>
                </div>
                <div class="queue-stat">
                    <div class="queue-stat-label">预计剩余</div>
                    <div class="queue-stat-value" id="queueETA">--</div>
                </div>
            </div>

            <div class="queue-overall-progress">
                <div class="queue-overall-progress-label">
                    <span>整体进度</span>
                    <span id="queueOverallPercent">0%</span>
                </div>
                <div class="queue-overall-progress-bar">
                    <div class="queue-overall-progress-fill" id="queueOverallFill"></div>
                </div>
            </div>

            <div class="queue-list" id="queueList">
                <!-- 队列项将动态添加到这里 -->
            </div>
        </div>

        <div class="performance-stats" id="performanceStats">
            <h4>📊 性能统计</h4>
            <div class="stats-grid" id="statsGrid"></div>
        </div>

        <div class="results" id="results"></div>
    </div>

    <script>
        class AESImageEncryptor {
            constructor() {
                this.initializeElements();
                this.setupEventListeners();
                this.encryptedFiles = [];
                this.currentMode = 'image'; // 'image' or 'video'

                // 队列管理
                this.encryptionQueue = [];
                this.queueId = 0;
                this.isQueuePaused = false;
                this.activeWorkers = 0;
                this.maxConcurrentWorkers = 2;
                this.queueStartTime = null;
                this.completedItems = 0;

                // 加载保存的队列设置
                this.loadQueueSettings();
            }
            
            initializeElements() {
                this.uploadArea = document.getElementById('uploadArea');
                this.fileInput = document.getElementById('fileInput');
                this.encryptionKeyInput = document.getElementById('encryptionKey');
                this.generateKeyBtn = document.getElementById('generateKey');
                this.progressContainer = document.getElementById('progressContainer');
                this.progressFill = document.getElementById('progressFill');
                this.progressText = document.getElementById('progressText');
                this.status = document.getElementById('status');
                this.results = document.getElementById('results');
                this.performanceStats = document.getElementById('performanceStats');
                this.statsGrid = document.getElementById('statsGrid');
                this.imageModeBtn = document.getElementById('imageMode');
                this.videoModeBtn = document.getElementById('videoMode');
                this.uploadText = document.getElementById('uploadText');
                this.encryptionInfo = document.getElementById('encryptionInfo');
                this.fileSizeWarning = document.getElementById('fileSizeWarning');
                this.memoryInfo = document.getElementById('memoryInfo');
                this.memoryStatus = document.getElementById('memoryStatus');

                // 队列管理元素
                this.queueSection = document.getElementById('queueSection');
                this.pauseQueueBtn = document.getElementById('pauseQueueBtn');
                this.resumeQueueBtn = document.getElementById('resumeQueueBtn');
                this.clearQueueBtn = document.getElementById('clearQueueBtn');
                this.concurrentLimitInput = document.getElementById('concurrentLimit');
                this.priorityModeSelect = document.getElementById('priorityMode');
                this.autoPauseCheckbox = document.getElementById('autoPause');
                this.queueList = document.getElementById('queueList');
                this.queueTotal = document.getElementById('queueTotal');
                this.queueWaiting = document.getElementById('queueWaiting');
                this.queueProcessing = document.getElementById('queueProcessing');
                this.queueCompleted = document.getElementById('queueCompleted');
                this.queueFailed = document.getElementById('queueFailed');
                this.queueETA = document.getElementById('queueETA');
                this.queueOverallPercent = document.getElementById('queueOverallPercent');
                this.queueOverallFill = document.getElementById('queueOverallFill');

                // 初始化内存监控
                this.initMemoryMonitoring();
            }
            
            setupEventListeners() {
                this.uploadArea.addEventListener('click', () => this.fileInput.click());
                this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
                this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                this.generateKeyBtn.addEventListener('click', this.generateRandomKey.bind(this));
                this.imageModeBtn.addEventListener('click', () => this.switchMode('image'));
                this.videoModeBtn.addEventListener('click', () => this.switchMode('video'));

                // 队列控制事件监听器
                this.pauseQueueBtn.addEventListener('click', this.pauseQueue.bind(this));
                this.resumeQueueBtn.addEventListener('click', this.resumeQueue.bind(this));
                this.clearQueueBtn.addEventListener('click', this.clearQueue.bind(this));
                this.concurrentLimitInput.addEventListener('change', this.updateConcurrentLimit.bind(this));
                this.priorityModeSelect.addEventListener('change', this.updatePriorityMode.bind(this));
                this.autoPauseCheckbox.addEventListener('change', this.saveQueueSettings.bind(this));
            }
            
            handleDragOver(e) {
                e.preventDefault();
                this.uploadArea.classList.add('dragover');
            }
            
            handleDragLeave(e) {
                e.preventDefault();
                this.uploadArea.classList.remove('dragover');
            }
            
            handleDrop(e) {
                e.preventDefault();
                this.uploadArea.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files).filter(file =>
                    this.currentMode === 'image' ? file.type.startsWith('image/') : file.type.startsWith('video/')
                );
                if (files.length > 0) {
                    this.processFiles(files);
                }
            }
            
            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    this.processFiles(files);
                }
            }
            
            switchMode(mode) {
                this.currentMode = mode;

                if (mode === 'image') {
                    this.imageModeBtn.classList.add('active');
                    this.videoModeBtn.classList.remove('active');
                    this.fileInput.accept = 'image/*';
                    this.uploadText.innerHTML = `
                        <strong>点击选择图片文件或拖拽到此处</strong><br>
                        支持批量上传 JPG, PNG, GIF, WebP 格式
                    `;
                    this.encryptionInfo.textContent = '📷 图片模式：使用 AES-256-GCM 加密，提供最高安全级别';
                    this.fileSizeWarning.style.display = 'none';
                } else {
                    this.videoModeBtn.classList.add('active');
                    this.imageModeBtn.classList.remove('active');
                    this.fileInput.accept = 'video/*';
                    this.uploadText.innerHTML = `
                        <strong>点击选择视频文件或拖拽到此处</strong><br>
                        支持批量上传 MP4, AVI, MOV, WebM, MKV 格式
                    `;
                    this.encryptionInfo.textContent = '🎬 视频模式：使用 AES-128-GCM 加密，优化大文件处理性能';
                    this.fileSizeWarning.style.display = 'block';
                }

                // 清空之前的结果
                this.results.innerHTML = '';
                this.performanceStats.style.display = 'none';
                this.encryptedFiles = [];

                // 清空队列
                this.clearQueue();
            }

            // 队列设置管理
            loadQueueSettings() {
                try {
                    const settings = JSON.parse(localStorage.getItem('encryptorQueueSettings') || '{}');
                    this.maxConcurrentWorkers = settings.concurrentLimit || 2;
                    this.concurrentLimitInput.value = this.maxConcurrentWorkers;
                    this.priorityModeSelect.value = settings.priorityMode || 'fifo';
                    this.autoPauseCheckbox.checked = settings.autoPause || false;
                } catch (error) {
                    console.warn('加载队列设置失败:', error);
                }
            }

            saveQueueSettings() {
                const settings = {
                    concurrentLimit: this.maxConcurrentWorkers,
                    priorityMode: this.priorityModeSelect.value,
                    autoPause: this.autoPauseCheckbox.checked
                };
                localStorage.setItem('encryptorQueueSettings', JSON.stringify(settings));
            }

            updateConcurrentLimit() {
                this.maxConcurrentWorkers = Math.max(1, Math.min(5, parseInt(this.concurrentLimitInput.value) || 2));
                this.concurrentLimitInput.value = this.maxConcurrentWorkers;
                this.saveQueueSettings();
                this.processQueue(); // 重新处理队列以应用新的并发限制
            }

            updatePriorityMode() {
                this.saveQueueSettings();
                this.sortQueue();
                this.updateQueueDisplay();
            }

            // 队列控制方法
            pauseQueue() {
                this.isQueuePaused = true;
                this.pauseQueueBtn.style.display = 'none';
                this.resumeQueueBtn.style.display = 'inline-block';
                this.showStatus('队列已暂停', 'success');
                this.updateQueueDisplay();
            }

            resumeQueue() {
                this.isQueuePaused = false;
                this.pauseQueueBtn.style.display = 'inline-block';
                this.resumeQueueBtn.style.display = 'none';
                this.showStatus('队列已恢复', 'success');
                this.processQueue();
            }

            clearQueue() {
                // 停止所有处理
                this.isQueuePaused = true;

                // 清空队列
                this.encryptionQueue = [];
                this.activeWorkers = 0;
                this.completedItems = 0;
                this.queueStartTime = null;

                // 隐藏队列区域
                this.queueSection.style.display = 'none';

                // 重置按钮状态
                this.pauseQueueBtn.style.display = 'inline-block';
                this.resumeQueueBtn.style.display = 'none';
                this.isQueuePaused = false;

                this.updateQueueDisplay();
                this.showStatus('队列已清空', 'success');
            }

            addToQueue(files) {
                if (files.length === 0) return;

                // 显示队列区域
                this.queueSection.style.display = 'block';

                // 添加文件到队列
                files.forEach(file => {
                    const queueItem = {
                        id: ++this.queueId,
                        file: file,
                        status: 'waiting', // waiting, processing, completed, failed, paused
                        progress: 0,
                        error: null,
                        startTime: null,
                        endTime: null,
                        encryptedData: null
                    };
                    this.encryptionQueue.push(queueItem);
                });

                // 设置队列开始时间
                if (!this.queueStartTime) {
                    this.queueStartTime = Date.now();
                }

                // 排序队列
                this.sortQueue();

                // 更新显示
                this.updateQueueDisplay();

                // 开始处理队列
                this.processQueue();
            }

            generateRandomKey() {
                const keySize = this.currentMode === 'image' ? 32 : 16; // 256位 for images, 128位 for videos
                const array = new Uint8Array(keySize);
                crypto.getRandomValues(array);
                const key = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
                this.encryptionKeyInput.value = key;
                const bits = keySize * 8;
                this.showStatus(`已生成${bits}位随机密钥`, 'success');
            }
            
            // 队列排序
            sortQueue() {
                const priorityMode = this.priorityModeSelect.value;

                // 只对等待中的项目进行排序
                const waitingItems = this.encryptionQueue.filter(item => item.status === 'waiting');
                const otherItems = this.encryptionQueue.filter(item => item.status !== 'waiting');

                switch (priorityMode) {
                    case 'size-asc':
                        waitingItems.sort((a, b) => a.file.size - b.file.size);
                        break;
                    case 'size-desc':
                        waitingItems.sort((a, b) => b.file.size - a.file.size);
                        break;
                    case 'fifo':
                    default:
                        waitingItems.sort((a, b) => a.id - b.id);
                        break;
                }

                this.encryptionQueue = [...otherItems, ...waitingItems];
            }

            // 更新队列显示
            updateQueueDisplay() {
                // 更新统计信息
                const stats = this.getQueueStats();
                this.queueTotal.textContent = stats.total;
                this.queueWaiting.textContent = stats.waiting;
                this.queueProcessing.textContent = stats.processing;
                this.queueCompleted.textContent = stats.completed;
                this.queueFailed.textContent = stats.failed;

                // 更新ETA
                this.updateETA();

                // 更新整体进度
                const overallProgress = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;
                this.queueOverallPercent.textContent = `${overallProgress.toFixed(1)}%`;
                this.queueOverallFill.style.width = `${overallProgress}%`;

                // 更新队列列表
                this.updateQueueList();
            }

            getQueueStats() {
                return {
                    total: this.encryptionQueue.length,
                    waiting: this.encryptionQueue.filter(item => item.status === 'waiting').length,
                    processing: this.encryptionQueue.filter(item => item.status === 'processing').length,
                    completed: this.encryptionQueue.filter(item => item.status === 'completed').length,
                    failed: this.encryptionQueue.filter(item => item.status === 'failed').length
                };
            }

            updateETA() {
                if (!this.queueStartTime || this.completedItems === 0) {
                    this.queueETA.textContent = '--';
                    return;
                }

                const elapsedTime = Date.now() - this.queueStartTime;
                const avgTimePerItem = elapsedTime / this.completedItems;
                const remainingItems = this.encryptionQueue.filter(item =>
                    item.status === 'waiting' || item.status === 'processing'
                ).length;

                if (remainingItems === 0) {
                    this.queueETA.textContent = '完成';
                    return;
                }

                const estimatedRemainingTime = avgTimePerItem * remainingItems;
                this.queueETA.textContent = this.formatTime(estimatedRemainingTime);
            }

            formatTime(milliseconds) {
                const seconds = Math.floor(milliseconds / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);

                if (hours > 0) {
                    return `${hours}h ${minutes % 60}m`;
                } else if (minutes > 0) {
                    return `${minutes}m ${seconds % 60}s`;
                } else {
                    return `${seconds}s`;
                }
            }

            async deriveKey(password) {
                const encoder = new TextEncoder();
                const keyMaterial = await crypto.subtle.importKey(
                    'raw',
                    encoder.encode(password),
                    { name: 'PBKDF2' },
                    false,
                    ['deriveKey']
                );

                const salt = new Uint8Array(16);
                crypto.getRandomValues(salt);

                // 根据模式选择密钥长度：图片256位，视频128位
                const keyLength = this.currentMode === 'image' ? 256 : 128;

                const key = await crypto.subtle.deriveKey(
                    {
                        name: 'PBKDF2',
                        salt: salt,
                        iterations: 100000,
                        hash: 'SHA-256'
                    },
                    keyMaterial,
                    { name: 'AES-GCM', length: keyLength },
                    false,
                    ['encrypt']
                );

                return { key, salt };
            }
            
            async encryptFile(file, key, progressCallback) {
                const iv = new Uint8Array(12);
                crypto.getRandomValues(iv);

                try {
                    // 根据文件大小和可用内存选择处理策略
                    const shouldUseChunking = this.shouldUseChunking(file.size);

                    if (shouldUseChunking) {
                        console.log(`文件 ${file.name} 使用分块加密，大小: ${(file.size/1024/1024).toFixed(2)}MB`);
                        return await this.encryptFileInChunks(file, key, iv, progressCallback);
                    } else {
                        // 小文件直接加密
                        console.log(`文件 ${file.name} 使用直接加密，大小: ${(file.size/1024/1024).toFixed(2)}MB`);
                        const fileBuffer = await file.arrayBuffer();
                        const encryptedData = await crypto.subtle.encrypt(
                            {
                                name: 'AES-GCM',
                                iv: iv,
                                tagLength: 128
                            },
                            key,
                            fileBuffer
                        );
                        return { encryptedData, iv };
                    }
                } catch (error) {
                    console.error('文件加密失败:', error);
                    // 如果是内存错误，尝试强制垃圾回收后重试
                    if (error.message.includes('memory') || error.message.includes('allocation')) {
                        if (window.gc) {
                            window.gc();
                        }
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    throw new Error(`文件 ${file.name} 加密失败: ${error.message}`);
                }
            }

            shouldUseChunking(fileSize) {
                // 基于文件大小和可用内存决定是否使用分块
                const threshold = 50 * 1024 * 1024; // 50MB基础阈值

                if (fileSize < threshold) {
                    return false;
                }

                // 检查可用内存
                if (performance.memory) {
                    const availableMemory = performance.memory.jsHeapSizeLimit - performance.memory.usedJSHeapSize;
                    const memoryRatio = fileSize / availableMemory;

                    // 如果文件大小超过可用内存的20%，使用分块
                    if (memoryRatio > 0.2) {
                        return true;
                    }
                }

                // 大于50MB的文件默认使用分块
                return fileSize > threshold;
            }

            async encryptFileInChunks(file, key, iv, progressCallback) {
                // 动态调整分块大小基于可用内存
                let chunkSize = this.calculateOptimalChunkSize(file.size);
                const totalChunks = Math.ceil(file.size / chunkSize);

                console.log(`分块加密: 文件大小 ${(file.size/1024/1024).toFixed(2)}MB, 分块大小 ${(chunkSize/1024/1024).toFixed(2)}MB, 总分块数 ${totalChunks}`);

                // 使用Blob流式处理，避免大内存分配
                const encryptedBlobs = [];
                const batchSize = Math.max(2, Math.min(5, Math.floor(100 * 1024 * 1024 / chunkSize))); // 减小批次大小

                for (let i = 0; i < totalChunks; i++) {
                    try {
                        const start = i * chunkSize;
                        const end = Math.min(start + chunkSize, file.size);
                        const chunk = file.slice(start, end);

                        // 为每个分块生成独立的IV
                        const chunkIv = new Uint8Array(12);
                        crypto.getRandomValues(chunkIv);

                        // 使用FileReader流式读取，避免arrayBuffer()的内存问题
                        const chunkBuffer = await this.readChunkAsArrayBuffer(chunk);

                        const encryptedChunk = await crypto.subtle.encrypt(
                            {
                                name: 'AES-GCM',
                                iv: chunkIv,
                                tagLength: 128
                            },
                            key,
                            chunkBuffer
                        );

                        const encryptedData = new Uint8Array(encryptedChunk);
                        encryptedBlobs.push({
                            iv: chunkIv,
                            data: encryptedData
                        });

                        // 更新进度
                        if (progressCallback) {
                            progressCallback((i + 1) / totalChunks * 100);
                        }

                        // 分批清理内存，避免积累过多
                        if (encryptedBlobs.length % batchSize === 0) {
                            // 强制垃圾回收提示
                            if (window.gc) {
                                window.gc();
                            }
                        }

                        // 让出控制权，避免阻塞UI
                        await new Promise(resolve => setTimeout(resolve, 1));

                    } catch (error) {
                        console.error(`处理分块 ${i} 失败:`, error);
                        throw new Error(`分块 ${i} 加密失败: ${error.message}`);
                    }
                }

                // 所有分块处理完成，组合最终结果
                console.log(`开始组合 ${encryptedBlobs.length} 个加密分块`);
                const combinedBlob = await this.combineEncryptedChunksAsBlob(encryptedBlobs);

                // 清理临时数据
                encryptedBlobs.length = 0;

                return { encryptedData: combinedBlob, iv: iv, isChunked: true, isBlob: true };
            }

            async readChunkAsArrayBuffer(chunk) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        resolve(e.target.result);
                    };

                    reader.onerror = function(e) {
                        reject(new Error('分块读取失败'));
                    };

                    reader.onabort = function(e) {
                        reject(new Error('分块读取被中断'));
                    };

                    reader.readAsArrayBuffer(chunk);
                });
            }

            initializeCombinedArray(chunksInfo, totalChunks) {
                // 计算总大小
                let totalSize = 4; // 分块数量 (4字节)

                // 由于chunksInfo在这个时候可能还不完整，我们需要重新计算
                // 使用预估的方式计算总大小
                const avgChunkSize = 8 * 1024 * 1024; // 8MB平均分块大小
                const estimatedEncryptedChunkSize = avgChunkSize + 16; // 加上GCM标签
                const estimatedTotalSize = 4 + totalChunks * (4 + 12 + 4 + estimatedEncryptedChunkSize);

                console.log(`预估总大小: ${(estimatedTotalSize/1024/1024).toFixed(2)}MB`);

                try {
                    const combined = new Uint8Array(estimatedTotalSize);

                    // 写入分块数量
                    const chunkCount = new Uint32Array([totalChunks]);
                    combined.set(new Uint8Array(chunkCount.buffer), 0);

                    return combined;
                } catch (error) {
                    console.error('内存分配失败:', error);
                    throw new Error(`无法分配${(estimatedTotalSize/1024/1024).toFixed(2)}MB内存，请尝试更小的文件`);
                }
            }

            calculateOptimalChunkSize(fileSize) {
                // 基于文件大小和可用内存计算最优分块大小
                let chunkSize = 8 * 1024 * 1024; // 默认8MB

                if (performance.memory) {
                    const availableMemory = performance.memory.jsHeapSizeLimit - performance.memory.usedJSHeapSize;
                    const maxChunkSize = Math.floor(availableMemory / 10); // 使用可用内存的10%

                    // 根据文件大小调整
                    if (fileSize > 1024 * 1024 * 1024) { // 大于1GB
                        chunkSize = Math.min(4 * 1024 * 1024, maxChunkSize); // 4MB
                    } else if (fileSize > 500 * 1024 * 1024) { // 大于500MB
                        chunkSize = Math.min(6 * 1024 * 1024, maxChunkSize); // 6MB
                    } else {
                        chunkSize = Math.min(8 * 1024 * 1024, maxChunkSize); // 8MB
                    }
                }

                return Math.max(1024 * 1024, chunkSize); // 最小1MB
            }

            // 这个方法已被简化的处理方式替代

            // 使用Blob流式组合，避免大内存分配
            async combineEncryptedChunksAsBlob(chunks) {
                try {
                    console.log(`开始流式组合 ${chunks.length} 个加密分块`);

                    // 创建分块数量的头部
                    const chunkCountBuffer = new Uint32Array([chunks.length]);
                    const headerBlob = new Blob([chunkCountBuffer.buffer]);

                    // 创建所有分块的Blob数组
                    const blobParts = [headerBlob];

                    chunks.forEach((chunk, index) => {
                        try {
                            if (!chunk || !chunk.iv || !chunk.data) {
                                throw new Error(`分块 ${index} 数据不完整`);
                            }

                            // IV长度
                            const ivLengthBuffer = new Uint32Array([chunk.iv.length]);
                            blobParts.push(new Blob([ivLengthBuffer.buffer]));

                            // IV数据
                            blobParts.push(new Blob([chunk.iv]));

                            // 数据长度
                            const dataLengthBuffer = new Uint32Array([chunk.data.length]);
                            blobParts.push(new Blob([dataLengthBuffer.buffer]));

                            // 数据
                            blobParts.push(new Blob([chunk.data]));

                        } catch (error) {
                            console.error(`处理分块 ${index} 失败:`, error);
                            throw new Error(`处理分块 ${index} 失败: ${error.message}`);
                        }
                    });

                    // 使用Blob组合所有部分，避免大内存分配
                    const finalBlob = new Blob(blobParts);
                    console.log(`成功组合 ${chunks.length} 个分块，最终大小: ${(finalBlob.size/1024/1024).toFixed(2)}MB`);

                    return finalBlob;

                } catch (error) {
                    console.error('组合加密分块失败:', error);
                    throw new Error(`组合加密分块失败: ${error.message}`);
                }
            }

            // 保留原方法用于小文件的向后兼容
            combineEncryptedChunks(chunks) {
                try {
                    // 计算总大小
                    let totalSize = 4; // 分块数量 (4字节)
                    chunks.forEach((chunk, index) => {
                        if (!chunk || !chunk.iv || !chunk.data) {
                            throw new Error(`分块 ${index} 数据不完整`);
                        }
                        totalSize += 4 + chunk.iv.length + 4 + chunk.data.length;
                    });

                    const combined = new Uint8Array(totalSize);
                    let offset = 0;

                    // 写入分块数量
                    const chunkCount = new Uint32Array([chunks.length]);
                    combined.set(new Uint8Array(chunkCount.buffer), offset);
                    offset += 4;

                    // 写入每个分块
                    chunks.forEach((chunk, index) => {
                        // IV长度
                        const ivLength = new Uint32Array([chunk.iv.length]);
                        combined.set(new Uint8Array(ivLength.buffer), offset);
                        offset += 4;

                        // IV数据
                        combined.set(chunk.iv, offset);
                        offset += chunk.iv.length;

                        // 数据长度
                        const dataLength = new Uint32Array([chunk.data.length]);
                        combined.set(new Uint8Array(dataLength.buffer), offset);
                        offset += 4;

                        // 数据
                        combined.set(chunk.data, offset);
                        offset += chunk.data.length;
                    });

                    return combined.buffer;

                } catch (error) {
                    console.error('组合加密分块失败:', error);
                    throw new Error(`组合加密分块失败: ${error.message}`);
                }
            }

            async readLargeFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        resolve(e.target.result);
                    };

                    reader.onerror = function(e) {
                        reject(new Error('文件读取失败'));
                    };

                    reader.onabort = function(e) {
                        reject(new Error('文件读取被中断'));
                    };

                    // 使用readAsArrayBuffer读取文件
                    reader.readAsArrayBuffer(file);
                });
            }
            
            // 更新队列列表显示
            updateQueueList() {
                this.queueList.innerHTML = '';

                this.encryptionQueue.forEach(item => {
                    const queueItemElement = this.createQueueItemElement(item);
                    this.queueList.appendChild(queueItemElement);
                });
            }

            createQueueItemElement(item) {
                const element = document.createElement('div');
                element.className = 'queue-item';
                element.dataset.itemId = item.id;

                const statusClass = item.status;
                const statusIcon = this.getStatusIcon(item.status);
                const fileSize = this.formatFileSize(item.file.size);

                element.innerHTML = `
                    <div class="queue-item-status ${statusClass}"></div>
                    <div class="queue-item-info">
                        <div class="queue-item-name" title="${item.file.name}">${item.file.name}</div>
                        <div class="queue-item-details">
                            <span>大小: ${fileSize}</span>
                            <span>状态: ${statusIcon}</span>
                            ${item.error ? `<span style="color: #dc3545;">错误: ${item.error}</span>` : ''}
                        </div>
                    </div>
                    <div class="queue-item-progress">
                        <div class="queue-item-progress-bar">
                            <div class="queue-item-progress-fill" style="width: ${item.progress}%"></div>
                        </div>
                        <div class="queue-item-progress-text">${item.progress.toFixed(1)}%</div>
                    </div>
                    <div class="queue-item-actions">
                        ${item.status === 'waiting' ? `
                            <button class="queue-item-btn priority" onclick="encryptor.moveToTop(${item.id})">置顶</button>
                        ` : ''}
                        ${item.status !== 'processing' ? `
                            <button class="queue-item-btn remove" onclick="encryptor.removeFromQueue(${item.id})">移除</button>
                        ` : ''}
                    </div>
                `;

                return element;
            }

            getStatusIcon(status) {
                const icons = {
                    waiting: '⏳ 等待中',
                    processing: '🔄 处理中',
                    completed: '✅ 已完成',
                    failed: '❌ 失败',
                    paused: '⏸️ 已暂停'
                };
                return icons[status] || '❓ 未知';
            }

            moveToTop(itemId) {
                const itemIndex = this.encryptionQueue.findIndex(item => item.id === itemId);
                if (itemIndex > -1 && this.encryptionQueue[itemIndex].status === 'waiting') {
                    const item = this.encryptionQueue.splice(itemIndex, 1)[0];

                    // 找到第一个等待中的项目的位置
                    const firstWaitingIndex = this.encryptionQueue.findIndex(item => item.status === 'waiting');
                    if (firstWaitingIndex > -1) {
                        this.encryptionQueue.splice(firstWaitingIndex, 0, item);
                    } else {
                        this.encryptionQueue.push(item);
                    }

                    this.updateQueueDisplay();
                }
            }

            removeFromQueue(itemId) {
                const itemIndex = this.encryptionQueue.findIndex(item => item.id === itemId);
                if (itemIndex > -1 && this.encryptionQueue[itemIndex].status !== 'processing') {
                    this.encryptionQueue.splice(itemIndex, 1);
                    this.updateQueueDisplay();

                    // 如果队列为空，隐藏队列区域
                    if (this.encryptionQueue.length === 0) {
                        this.queueSection.style.display = 'none';
                    }
                }
            }

            // 队列处理核心方法
            async processQueue() {
                if (this.isQueuePaused) return;

                // 启动新的工作线程，直到达到并发限制
                while (this.activeWorkers < this.maxConcurrentWorkers) {
                    const nextItem = this.encryptionQueue.find(item => item.status === 'waiting');
                    if (!nextItem) break;

                    this.activeWorkers++;
                    nextItem.status = 'processing';
                    nextItem.startTime = Date.now();

                    // 异步处理单个文件
                    this.processQueueItem(nextItem).finally(() => {
                        this.activeWorkers--;
                        this.processQueue(); // 处理完成后继续处理队列
                    });
                }

                this.updateQueueDisplay();
            }

            async processQueueItem(queueItem) {
                try {
                    const password = this.encryptionKeyInput.value.trim();
                    if (password.length < 8) {
                        throw new Error('密钥长度至少需要8位字符');
                    }

                    // 派生密钥
                    const { key, salt } = await this.deriveKey(password);

                    // 进度回调
                    const progressCallback = (progress) => {
                        queueItem.progress = progress;
                        this.updateQueueItemProgress(queueItem.id, progress);
                    };

                    // 加密文件
                    const { encryptedData, iv, isChunked, isBlob } = await this.encryptFile(
                        queueItem.file,
                        key,
                        progressCallback
                    );

                    // 创建最终的加密文件
                    const finalData = await this.createFinalEncryptedFile(
                        queueItem.file,
                        encryptedData,
                        salt,
                        iv,
                        isChunked,
                        isBlob
                    );

                    // 标记完成
                    queueItem.status = 'completed';
                    queueItem.progress = 100;
                    queueItem.endTime = Date.now();
                    queueItem.encryptedData = finalData;

                    // 添加到结果列表
                    this.encryptedFiles.push({
                        name: queueItem.file.name,
                        originalSize: queueItem.file.size,
                        encryptedSize: finalData.size || finalData.length,
                        data: finalData.data,
                        isChunked: isChunked || false,
                        isBlob: isBlob || false
                    });

                    this.completedItems++;

                } catch (error) {
                    queueItem.status = 'failed';
                    queueItem.error = error.message;

                    console.error(`文件 ${queueItem.file.name} 处理失败:`, error);

                    // 如果启用了自动暂停，则暂停队列
                    if (this.autoPauseCheckbox.checked) {
                        this.pauseQueue();
                        this.showStatus(`文件 ${queueItem.file.name} 处理失败，队列已自动暂停`, 'error');
                    }
                }

                this.updateQueueDisplay();

                // 如果所有项目都完成了，显示最终结果
                if (this.isQueueComplete()) {
                    this.displayFinalResults();
                }
            }

            async processFiles(files) {
                const password = this.encryptionKeyInput.value.trim();
                if (password.length < 8) {
                    this.showStatus('密钥长度至少需要8位字符', 'error');
                    return;
                }

                // 将文件添加到队列而不是直接处理
                this.addToQueue(files);
            }

            // 辅助方法
            updateQueueItemProgress(itemId, progress) {
                const itemElement = this.queueList.querySelector(`[data-item-id="${itemId}"]`);
                if (itemElement) {
                    const progressFill = itemElement.querySelector('.queue-item-progress-fill');
                    const progressText = itemElement.querySelector('.queue-item-progress-text');
                    if (progressFill) progressFill.style.width = `${progress}%`;
                    if (progressText) progressText.textContent = `${progress.toFixed(1)}%`;
                }
            }

            async createFinalEncryptedFile(file, encryptedData, salt, iv, isChunked, isBlob) {
                // 创建包含元数据的加密文件格式
                const metadata = {
                    originalName: file.name,
                    originalSize: file.size,
                    mimeType: file.type,
                    timestamp: Date.now(),
                    fileType: this.currentMode,
                    encryptionAlgorithm: this.currentMode === 'image' ? 'AES-256-GCM' : 'AES-128-GCM',
                    isChunked: isChunked || false,
                    version: '2.0' // 版本标识
                };

                const metadataBuffer = new TextEncoder().encode(JSON.stringify(metadata));
                const metadataLength = new Uint32Array([metadataBuffer.length]);

                let finalData, finalSize;

                if (isBlob) {
                    // 对于Blob格式，使用流式组合
                    const headerParts = [
                        new Blob([salt]),
                        new Blob([iv]),
                        new Blob([metadataLength.buffer]),
                        new Blob([metadataBuffer]),
                        encryptedData // 这已经是一个Blob
                    ];

                    finalData = new Blob(headerParts);
                    finalSize = finalData.size;
                } else {
                    // 传统方式处理小文件
                    const combinedBuffer = new Uint8Array(
                        salt.length + iv.length + 4 + metadataBuffer.length + encryptedData.byteLength
                    );

                    let offset = 0;
                    combinedBuffer.set(salt, offset);
                    offset += salt.length;
                    combinedBuffer.set(iv, offset);
                    offset += iv.length;
                    combinedBuffer.set(new Uint8Array(metadataLength.buffer), offset);
                    offset += 4;
                    combinedBuffer.set(metadataBuffer, offset);
                    offset += metadataBuffer.length;
                    combinedBuffer.set(new Uint8Array(encryptedData), offset);

                    finalData = combinedBuffer;
                    finalSize = combinedBuffer.length;
                }

                return {
                    data: finalData,
                    size: finalSize
                };
            }

            isQueueComplete() {
                return this.encryptionQueue.length > 0 &&
                       this.encryptionQueue.every(item =>
                           item.status === 'completed' || item.status === 'failed'
                       );
            }

            displayFinalResults() {
                // 计算统计信息
                const completedItems = this.encryptionQueue.filter(item => item.status === 'completed');
                const failedItems = this.encryptionQueue.filter(item => item.status === 'failed');

                if (completedItems.length > 0) {
                    const totalTime = Date.now() - this.queueStartTime;
                    const totalOriginalSize = completedItems.reduce((sum, item) => sum + item.file.size, 0);
                    const totalEncryptedSize = this.encryptedFiles.reduce((sum, file) => sum + file.encryptedSize, 0);

                    // 显示性能统计
                    this.displayPerformanceStats({
                        totalTime,
                        keyDerivationTime: 0, // 队列模式下不单独计算
                        encryptionTime: totalTime,
                        totalOriginalSize,
                        totalEncryptedSize,
                        fileCount: completedItems.length,
                        chunkedFiles: this.encryptedFiles.filter(f => f.isChunked).length,
                        avgFileSize: totalOriginalSize / completedItems.length,
                        throughput: totalOriginalSize / (totalTime / 1000),
                        mode: this.currentMode,
                        algorithm: this.currentMode === 'image' ? 'AES-256-GCM' : 'AES-128-GCM'
                    });

                    this.displayResults();
                    this.showStatus(`队列处理完成！成功: ${completedItems.length}, 失败: ${failedItems.length}`,
                                  failedItems.length > 0 ? 'error' : 'success');
                } else {
                    this.showStatus('队列处理完成，但没有文件成功加密', 'error');
                }
            }
            
            displayResults() {
                this.results.innerHTML = '<h3>加密结果</h3>';
                
                this.encryptedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">
                                原始大小: ${this.formatFileSize(file.originalSize)} → 
                                加密后: ${this.formatFileSize(file.encryptedSize)}
                            </div>
                        </div>
                        <button class="btn download-btn" onclick="encryptor.downloadEncryptedFile(${index})">
                            下载加密文件
                        </button>
                    `;
                    this.results.appendChild(fileItem);
                });
            }
            
            downloadEncryptedFile(index) {
                const file = this.encryptedFiles[index];
                let blob;

                if (file.isBlob) {
                    // 文件数据已经是Blob格式
                    blob = file.data;
                } else {
                    // 传统ArrayBuffer格式
                    blob = new Blob([file.data], { type: 'application/octet-stream' });
                }

                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${file.name}.encrypted`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
            
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            showProgress(show) {
                this.progressContainer.style.display = show ? 'block' : 'none';
                if (!show) {
                    this.progressFill.style.width = '0%';
                }
            }
            
            updateProgress(percent, text) {
                this.progressFill.style.width = percent + '%';
                this.progressText.textContent = text;
            }
            
            displayPerformanceStats(stats) {
                this.performanceStats.style.display = 'block';

                const compressionRatio = ((stats.totalEncryptedSize - stats.totalOriginalSize) / stats.totalOriginalSize * 100).toFixed(2);
                const avgFileSize = (stats.totalOriginalSize / stats.fileCount / 1024 / 1024).toFixed(2);
                const throughputMBps = (stats.throughput / 1024 / 1024).toFixed(2);
                const modeIcon = stats.mode === 'image' ? '📷' : '🎬';
                const modeText = stats.mode === 'image' ? '图片' : '视频';

                this.statsGrid.innerHTML = `
                    <div class="stat-item">
                        <div class="stat-label">处理模式</div>
                        <div class="stat-value">${modeIcon} ${modeText}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">加密算法</div>
                        <div class="stat-value">${stats.algorithm}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">总用时</div>
                        <div class="stat-value">${stats.totalTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">密钥派生时间</div>
                        <div class="stat-value">${stats.keyDerivationTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">加密时间</div>
                        <div class="stat-value">${stats.encryptionTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">处理文件数</div>
                        <div class="stat-value">${stats.fileCount} 个</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">原始总大小</div>
                        <div class="stat-value">${this.formatFileSize(stats.totalOriginalSize)}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">加密后总大小</div>
                        <div class="stat-value">${this.formatFileSize(stats.totalEncryptedSize)}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">大小增长</div>
                        <div class="stat-value">${compressionRatio}%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">平均文件大小</div>
                        <div class="stat-value">${avgFileSize} MB</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">处理速度</div>
                        <div class="stat-value">${throughputMBps} MB/s</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">平均每文件用时</div>
                        <div class="stat-value">${(stats.totalTime / stats.fileCount).toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">分块处理文件</div>
                        <div class="stat-value">${stats.chunkedFiles || 0} 个</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">处理策略</div>
                        <div class="stat-value">${stats.avgFileSize > 50 * 1024 * 1024 ? '🚀 高性能分块' : '⚡ 直接处理'}</div>
                    </div>
                `;
            }

            initMemoryMonitoring() {
                if (performance.memory) {
                    this.memoryInfo.style.display = 'block';
                    this.updateMemoryStatus();

                    // 每5秒更新一次内存状态
                    setInterval(() => {
                        this.updateMemoryStatus();
                    }, 5000);
                }
            }

            updateMemoryStatus() {
                if (performance.memory) {
                    const used = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                    const total = (performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(1);
                    const limit = (performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(1);
                    const available = (limit - used).toFixed(1);

                    const usagePercent = ((used / limit) * 100).toFixed(1);

                    let statusColor = '#0c5460';
                    if (usagePercent > 80) {
                        statusColor = '#721c24';
                    } else if (usagePercent > 60) {
                        statusColor = '#856404';
                    }

                    this.memoryStatus.innerHTML = `
                        💾 内存使用: ${used}MB / ${limit}MB (${usagePercent}%) | 可用: ${available}MB
                    `;
                    this.memoryStatus.style.color = statusColor;
                }
            }

            showStatus(message, type) {
                this.status.innerHTML = `<div class="status ${type}">${message}</div>`;
                setTimeout(() => {
                    this.status.innerHTML = '';
                }, 5000);
            }
        }

        const encryptor = new AESImageEncryptor();
    </script>
</body>
</html>
