/**
 * 加密解密工具类
 * 使用WebCrypto API实现AES-256-GCM加密解密
 */

/**
 * 将十六进制字符串转换为Uint8Array
 */
function hexToUint8Array(hexString) {
  if (hexString.length % 2 !== 0) {
    throw new Error('十六进制字符串长度必须是偶数')
  }
  
  const result = new Uint8Array(hexString.length / 2)
  for (let i = 0; i < hexString.length; i += 2) {
    result[i / 2] = parseInt(hexString.substr(i, 2), 16)
  }
  return result
}

/**
 * 将Uint8Array转换为十六进制字符串
 */
function uint8ArrayToHex(uint8Array) {
  return Array.from(uint8Array)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

/**
 * 将字符串转换为Uint8Array
 */
function stringToUint8Array(str) {
  const encoder = new TextEncoder()
  return encoder.encode(str)
}

/**
 * 将Uint8Array转换为字符串
 */
function uint8ArrayToString(uint8Array) {
  const decoder = new TextDecoder()
  return decoder.decode(uint8Array)
}

/**
 * 从密码字符串生成AES密钥
 */
async function deriveKeyFromPassword(password) {
  try {
    // 将密码转换为Uint8Array
    const passwordBuffer = stringToUint8Array(password)
    
    // 对于AES-256-GCM，我们需要32字节的密钥
    // 如果密码不足32字节，用0填充；如果超过32字节，截取前32字节
    const keyBuffer = new Uint8Array(32)
    const copyLength = Math.min(passwordBuffer.length, 32)
    keyBuffer.set(passwordBuffer.slice(0, copyLength))
    
    // 导入密钥
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-GCM' },
      false,
      ['encrypt', 'decrypt']
    )
    
    return cryptoKey
  } catch (error) {
    console.error('密钥生成失败:', error)
    throw error
  }
}

/**
 * AES-256-GCM解密
 * @param {ArrayBuffer|Uint8Array} encryptedData - 加密的数据
 * @param {string} password - 解密密码
 * @param {string} ivHex - IV的十六进制字符串
 * @returns {Promise<Uint8Array>} 解密后的数据
 */
async function decryptAES256GCM(encryptedData, password, ivHex) {
  try {
    console.log('开始AES-256-GCM解密:', {
      dataLength: encryptedData.byteLength || encryptedData.length,
      password: password.substring(0, 4) + '...',
      ivHex: ivHex.substring(0, 8) + '...'
    })

    // 转换IV
    const iv = hexToUint8Array(ivHex)
    console.log('IV长度:', iv.length, '字节')

    // 生成密钥
    const key = await deriveKeyFromPassword(password)

    // 确保数据是ArrayBuffer格式
    let dataBuffer
    if (encryptedData instanceof Uint8Array) {
      dataBuffer = encryptedData.buffer.slice(
        encryptedData.byteOffset,
        encryptedData.byteOffset + encryptedData.byteLength
      )
    } else if (encryptedData instanceof ArrayBuffer) {
      dataBuffer = encryptedData
    } else {
      throw new Error('不支持的数据格式')
    }

    // 执行解密
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      key,
      dataBuffer
    )

    const decryptedData = new Uint8Array(decryptedBuffer)
    console.log('解密成功，数据长度:', decryptedData.length, '字节')
    
    return decryptedData
  } catch (error) {
    console.error('AES-256-GCM解密失败:', error)
    throw error
  }
}

/**
 * 从URL下载并解密图片
 * @param {string} url - 图片URL
 * @param {string} password - 解密密码
 * @param {string} ivHex - IV的十六进制字符串
 * @returns {Promise<Blob>} 解密后的图片Blob
 */
async function downloadAndDecryptImage(url, password, ivHex) {
  try {
    console.log('开始下载并解密图片:', url)

    // 下载加密的图片数据
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }

    const encryptedData = await response.arrayBuffer()
    console.log('下载完成，数据大小:', encryptedData.byteLength, '字节')

    // 解密数据
    const decryptedData = await decryptAES256GCM(encryptedData, password, ivHex)

    // 创建Blob对象
    const blob = new Blob([decryptedData], { type: 'image/jpeg' })
    console.log('图片解密完成，Blob大小:', blob.size, '字节')

    return blob
  } catch (error) {
    console.error('下载并解密图片失败:', error)
    throw error
  }
}

/**
 * 创建图片的Object URL
 * @param {Blob} imageBlob - 图片Blob
 * @returns {string} Object URL
 */
function createImageObjectURL(imageBlob) {
  try {
    const objectURL = URL.createObjectURL(imageBlob)
    console.log('创建Object URL成功:', objectURL.substring(0, 50) + '...')
    return objectURL
  } catch (error) {
    console.error('创建Object URL失败:', error)
    throw error
  }
}

/**
 * 释放Object URL
 * @param {string} objectURL - 要释放的Object URL
 */
function revokeImageObjectURL(objectURL) {
  try {
    URL.revokeObjectURL(objectURL)
    console.log('Object URL已释放')
  } catch (error) {
    console.error('释放Object URL失败:', error)
  }
}

/**
 * 完整的图片解密流程
 * @param {string} url - 加密图片URL
 * @param {string} password - 解密密码
 * @param {string} ivHex - IV的十六进制字符串
 * @returns {Promise<string>} 解密后图片的Object URL
 */
async function decryptImageFromUrl(url, password, ivHex) {
  try {
    // 下载并解密图片
    const imageBlob = await downloadAndDecryptImage(url, password, ivHex)
    
    // 创建Object URL
    const objectURL = createImageObjectURL(imageBlob)
    
    return objectURL
  } catch (error) {
    console.error('图片解密流程失败:', error)
    throw error
  }
}

/**
 * 批量解密图片
 * @param {Array} imageList - 包含URL、密码、IV的图片列表
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Array>} 包含解密后Object URL的图片列表
 */
async function batchDecryptImages(imageList, onProgress = null) {
  try {
    const results = []
    const total = imageList.length
    
    for (let i = 0; i < imageList.length; i++) {
      const image = imageList[i]
      
      try {
        if (image.thumbnailUrl && image.password && image.iv) {
          const objectURL = await decryptImageFromUrl(
            image.thumbnailUrl,
            image.password,
            image.iv
          )
          
          results.push({
            ...image,
            decryptedThumbnailUrl: objectURL,
            decryptionSuccess: true
          })
        } else {
          results.push({
            ...image,
            decryptedThumbnailUrl: '',
            decryptionSuccess: false,
            decryptionError: '缺少必要的解密参数'
          })
        }
      } catch (error) {
        console.error(`图片解密失败 (ID: ${image.id}):`, error)
        results.push({
          ...image,
          decryptedThumbnailUrl: '',
          decryptionSuccess: false,
          decryptionError: error.message
        })
      }
      
      // 调用进度回调
      if (onProgress) {
        onProgress(i + 1, total)
      }
    }
    
    return results
  } catch (error) {
    console.error('批量解密图片失败:', error)
    throw error
  }
}

export {
  decryptAES256GCM,
  downloadAndDecryptImage,
  decryptImageFromUrl,
  batchDecryptImages,
  createImageObjectURL,
  revokeImageObjectURL,
  deriveKeyFromPassword,
  hexToUint8Array,
  uint8ArrayToHex,
  stringToUint8Array,
  uint8ArrayToString
}
