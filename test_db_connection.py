#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接的脚本
"""

import sys
import os
from pathlib import Path

# 添加aes目录到Python路径
current_dir = Path(__file__).parent
aes_dir = current_dir / 'aes'
sys.path.append(str(aes_dir))

def test_database_connection():
    """测试数据库连接"""
    try:
        print("1. 导入数据库队列模块...")
        from database_queue import get_database_queue
        print("   ✓ 成功导入数据库队列")
        
        print("2. 获取数据库队列实例...")
        db_queue = get_database_queue()
        print("   ✓ 成功获取数据库队列实例")
        
        print("3. 启动数据库队列...")
        if not db_queue.running:
            db_queue.start()
            print("   ✓ 数据库队列已启动")
        else:
            print("   ✓ 数据库队列已在运行")
        
        print("4. 测试保存视频信息...")
        db_queue.save_encrypted_video(
            sha1_hash='test123',
            original_filename='test.mp4',
            m3u8_path='/test/path',
            password='abcd1234',
            iv='efgh5678',
            file_size_mb=10.5
        )
        print("   ✓ 视频信息保存测试成功")
        
        print("5. 测试保存文件信息...")
        db_queue.save_encrypted_file(
            sha1_hash='test456',
            original_filename='test.txt',
            file_path='/test/file.txt',
            password='ijkl9012',
            iv='mnop3456',
            file_size_mb=1.2
        )
        print("   ✓ 文件信息保存测试成功")
        
        print("6. 检查数据库中的记录...")
        import sqlite3
        db_file = aes_dir / 'image_encryption.db'
        if db_file.exists():
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()
            
            # 检查视频表
            cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
            video_count = cursor.fetchone()[0]
            print(f"   ✓ 视频表记录数: {video_count}")
            
            # 检查文件表
            cursor.execute("SELECT COUNT(*) FROM encrypted_files")
            file_count = cursor.fetchone()[0]
            print(f"   ✓ 文件表记录数: {file_count}")
            
            conn.close()
        else:
            print("   ⚠ 数据库文件不存在")
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_processor_connection():
    """测试视频处理器的数据库连接"""
    try:
        print("\n=== 测试视频处理器数据库连接 ===")
        
        # 切换到m3u8目录
        os.chdir(str(current_dir / 'm3u8'))
        
        # 添加路径
        sys.path.append(str(current_dir / 'm3u8'))
        
        print("1. 导入视频处理器...")
        from video_processor import VideoProcessor
        print("   ✓ 成功导入视频处理器")
        
        print("2. 创建视频处理器实例...")
        processor = VideoProcessor()
        print("   ✓ 成功创建视频处理器实例")
        
        print("3. 检查数据库队列连接...")
        if processor.db_queue:
            print("   ✓ 数据库队列连接成功")
            print(f"   ✓ 数据库队列运行状态: {processor.db_queue.running}")
        else:
            print("   ❌ 数据库队列连接失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n❌ 视频处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 切换回原目录
        os.chdir(str(current_dir))

def test_file_encryptor_connection():
    """测试文件加密器的数据库连接"""
    try:
        print("\n=== 测试文件加密器数据库连接 ===")
        
        # 切换到m3u8目录
        os.chdir(str(current_dir / 'm3u8'))
        
        print("1. 导入文件加密器...")
        from file_encryptor import FileEncryptor
        print("   ✓ 成功导入文件加密器")
        
        print("2. 创建文件加密器实例...")
        encryptor = FileEncryptor()
        print("   ✓ 成功创建文件加密器实例")
        
        print("3. 检查数据库队列连接...")
        if encryptor.db_queue:
            print("   ✓ 数据库队列连接成功")
            print(f"   ✓ 数据库队列运行状态: {encryptor.db_queue.running}")
        else:
            print("   ❌ 数据库队列连接失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n❌ 文件加密器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 切换回原目录
        os.chdir(str(current_dir))

def main():
    """主函数"""
    print("开始测试数据库连接...")
    
    # 测试基本数据库连接
    db_test = test_database_connection()
    
    # 测试视频处理器连接
    video_test = test_video_processor_connection()
    
    # 测试文件加密器连接
    file_test = test_file_encryptor_connection()
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"  数据库连接测试: {'✅ 通过' if db_test else '❌ 失败'}")
    print(f"  视频处理器测试: {'✅ 通过' if video_test else '❌ 失败'}")
    print(f"  文件加密器测试: {'✅ 通过' if file_test else '❌ 失败'}")
    
    if db_test and video_test and file_test:
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠ 部分测试失败，请检查相关组件")

if __name__ == "__main__":
    main()
