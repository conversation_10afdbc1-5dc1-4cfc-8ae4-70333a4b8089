#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本
提供简单的命令行界面来运行不同的功能
"""

import sys
import subprocess
from pathlib import Path

def show_menu():
    """显示主菜单"""
    print("=" * 50)
    print("图片处理和加密系统")
    print("=" * 50)
    print("1. 安装依赖包")
    print("2. 创建测试图片")
    print("3. 启动图片监控和处理")
    print("4. 列出加密文件")
    print("5. 解密指定文件")
    print("6. 解密所有文件")
    print("7. 查看帮助")
    print("0. 退出")
    print("-" * 50)

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("依赖包安装成功！")
    except subprocess.CalledProcessError:
        print("依赖包安装失败，请手动运行: pip install -r requirements.txt")

def create_test_images():
    """创建测试图片"""
    print("创建测试图片...")
    try:
        subprocess.run([sys.executable, "test_system.py"], check=True)
    except subprocess.CalledProcessError:
        print("创建测试图片失败")

def start_monitoring():
    """启动图片监控"""
    print("启动图片监控和处理系统...")
    print("按 Ctrl+C 停止程序")
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n程序已停止")

def list_encrypted_files():
    """列出加密文件"""
    try:
        subprocess.run([sys.executable, "decrypt_tool.py", "--list"])
    except subprocess.CalledProcessError:
        print("列出加密文件失败")

def decrypt_file():
    """解密指定文件"""
    file_path = input("请输入要解密的文件路径: ").strip()
    if not file_path:
        print("文件路径不能为空")
        return
    
    output_path = input("请输入输出路径（留空使用默认路径）: ").strip()
    
    try:
        if output_path:
            subprocess.run([sys.executable, "decrypt_tool.py", "--file", file_path, "--output", output_path])
        else:
            subprocess.run([sys.executable, "decrypt_tool.py", "--file", file_path])
    except subprocess.CalledProcessError:
        print("解密文件失败")

def decrypt_all_files():
    """解密所有文件"""
    output_dir = input("请输入输出目录（默认: decrypted）: ").strip()
    if not output_dir:
        output_dir = "decrypted"
    
    try:
        subprocess.run([sys.executable, "decrypt_tool.py", "--all", output_dir])
    except subprocess.CalledProcessError:
        print("批量解密失败")

def show_help():
    """显示帮助信息"""
    print("\n" + "=" * 60)
    print("帮助信息")
    print("=" * 60)
    print("这是一个自动化的图片处理和加密系统。")
    print("\n主要功能:")
    print("• 监控temp目录中的图片文件")
    print("• 自动生成缩略图")
    print("• 转换为AVIF格式")
    print("• 使用AES256-GCM加密")
    print("• 安全存储加密信息")
    
    print("\n使用流程:")
    print("1. 首先安装依赖包")
    print("2. 可以创建测试图片进行测试")
    print("3. 启动监控系统")
    print("4. 将图片文件放入temp目录")
    print("5. 系统会自动处理这些图片")
    print("6. 使用解密工具恢复图片")
    
    print("\n支持的图片格式:")
    print("• JPG/JPEG")
    print("• PNG")
    print("• WebP")
    print("• AVIF")
    print("• HEIC")
    
    print("\n文件说明:")
    print("• main.py - 主程序，负责监控和处理图片")
    print("• decrypt_tool.py - 解密工具")
    print("• test_system.py - 测试脚本")
    print("• image_encryption.db - 加密信息数据库")
    print("• temp/ - 监控目录")
    
    print("\n注意事项:")
    print("• 程序会覆盖原始图片文件，请提前备份")
    print("• 加密后的文件需要使用解密工具才能查看")
    print("• 数据库文件包含解密密钥，请妥善保管")

def main():
    """主函数"""
    while True:
        try:
            show_menu()
            choice = input("请选择操作 (0-7): ").strip()
            
            if choice == "0":
                print("再见！")
                break
            elif choice == "1":
                install_dependencies()
            elif choice == "2":
                create_test_images()
            elif choice == "3":
                start_monitoring()
            elif choice == "4":
                list_encrypted_files()
            elif choice == "5":
                decrypt_file()
            elif choice == "6":
                decrypt_all_files()
            elif choice == "7":
                show_help()
            else:
                print("无效选择，请输入0-7之间的数字")
            
            if choice != "0":
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n程序已退出")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
