@echo off
echo ========================================
echo 视频处理程序 - Windows启动脚本
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    echo 请安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo 错误: FFmpeg未安装或不在PATH中
    echo 请安装FFmpeg并添加到PATH
    pause
    exit /b 1
)

REM 安装依赖
echo 安装Python依赖...
pip install -r requirements.txt

REM 运行程序
echo 启动视频处理程序...
python start.py

pause
