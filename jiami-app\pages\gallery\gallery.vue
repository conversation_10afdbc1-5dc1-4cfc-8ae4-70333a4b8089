﻿<template>
    <view class="container">
        <!-- 加载状态 -->
        <view v-if="initialLoading" class="loading">
            <view class="spinner"></view>
            <text>正在扫描本机相册...</text>
            <text class="loading-tip">首次加载可能需要较长时间</text>
            <!-- 调试按钮 -->
            <view class="debug-actions">
                <button @click="skipToMockData" class="debug-btn">跳过使用模拟数据</button>
                <button @click="loadQuickMode" class="debug-btn quick-btn">快速加载模式</button>
            </view>
        </view>

        <!-- 媒体类型切换按钮 -->
        <view v-if="!initialLoading" class="media-tabs">
            <view
                class="media-tab"
                :class="{ active: currentMediaType === 'image' }"
                @click="switchMediaType('image')"
            >
                📷 图片
            </view>
            <view
                class="media-tab"
                :class="{ active: currentMediaType === 'video' }"
                @click="switchMediaType('video')"
            >
                🎬 视频
            </view>
        </view>

        <!-- 🚀 高性能相册列表 - 支持下拉加载，优化滚动性能 -->
        <scroll-view v-if="!initialLoading" class="gallery-scroll" scroll-y="true" @scroll="handleScroll" @scrolltolower="loadMoreImages" :style="{ height: containerHeight + 'px' }" :lower-threshold="150" :throttle="false" :enable-back-to-top="true" :scroll-with-animation="false">

            <!-- 图片网格布局 -->
            <view v-if="currentMediaType === 'image'" class="media-grid">
                <view
                    v-for="item in allMediaItems"
                    :key="item.id"
                    class="media-item"
                    @click="handleItemClick(item)"
                >
                    <image
                        :src="item.url"
                        class="media-image"
                        mode="aspectFill"
                        :lazy-load="true"
                        :webp="true"
                        :fade-show="false"
                        :show-menu-by-longpress="false"
                        @load="handleImageLoad(item)"
                        @error="handleImageError(item)"
                    />

                    <!-- 加载状态 -->
                    <view v-if="item.loading" class="loading-overlay">
                        <view class="mini-spinner"></view>
                    </view>
                </view>
            </view>

            <!-- 视频列表布局 -->
            <view v-else class="video-list">
                <view
                    v-for="item in allMediaItems"
                    :key="item.id"
                    class="video-item"
                    @click="handleItemClick(item)"
                >
                    <view class="video-icon">🎬</view>
                    <view class="video-info">
                        <view class="video-name">{{ item.name }}</view>
                        <view class="video-details">
                            <text class="video-size">{{ item.size }}</text>
                            <text class="video-date">{{ item.date }}</text>
                        </view>
                    </view>
                    <view class="video-arrow">›</view>
                </view>
            </view>

            <!-- 🚀 高性能下拉加载更多状态 -->
            <view v-if="hasMoreData && allMediaItems.length > 0" class="load-more">
                <view v-if="loadingMore" class="loading-more">
                    <view class="mini-spinner"></view>
                    <text class="loading-text">加载更多图片...</text>
                </view>
                <text v-else class="load-more-text">下拉加载更多</text>
            </view>

            <!-- 没有更多数据 -->
            <view v-if="!hasMoreData && allMediaItems.length > 0" class="no-more">
                <text class="no-more-text">已加载全部图片</text>
            </view>

            <!-- 空状态 -->
            <view v-if="allMediaItems.length === 0" class="empty-state">
                <text class="empty-icon">🖼️</text>
                <text class="empty-text">暂无图片文件</text>
            </view>
        </scroll-view>
    </view>
</template>

<script>
export default {
    name: 'Gallery',
    data() {
        return {
            // 基础数据
            allMediaItems: [],
            allImageItems: [], // 图片数据
            allVideoItems: [], // 视频数据
            totalImages: 0,
            totalVideos: 0,
            currentMediaType: 'image', // 当前显示的媒体类型：'image' 或 'video'
            initialLoading: true,
            containerHeight: 0,
            imageCache: new Map(),
            
            // 🚀 高性能下拉加载控制 - 优化批量大小
            loadBatchSize: 30, // 减少批量大小，提升响应速度
            loadOffset: 0,
            hasMoreData: true,
            loadingMore: false,
            loadMoreTimer: null,

            // 性能优化 - 增强节流和缓存
            backgroundLoadTimer: null,
            isScrolling: false,
            scrollTimer: null,
            renderTimer: null,
            visibleItems: new Set(), // 可见项目缓存
            intersectionObserver: null // 交叉观察器
        }
    },
    computed: {
        // 当前显示的媒体项目
        allMediaItems() {
            return this.currentMediaType === 'image' ? this.allImageItems : this.allVideoItems
        },

        // 图片数量
        imageCount() {
            return this.allImageItems.length
        },

        // 视频数量
        videoCount() {
            return this.allVideoItems.length
        },

        // 当前媒体总数
        totalMediaCount() {
            return this.currentMediaType === 'image' ? this.totalImages : this.totalVideos
        },

        groupedMedia() {
            if (!this.allMediaItems || this.allMediaItems.length === 0) {
                return {}
            }

            const groups = {}
            this.allMediaItems.forEach(item => {
                const dateKey = item.date || '未知日期'
                if (!groups[dateKey]) {
                    groups[dateKey] = []
                }
                groups[dateKey].push(item)
            })

            const sortedGroups = {}
            Object.keys(groups)
                .sort((a, b) => {
                    if (a === '未知日期') return 1
                    if (b === '未知日期') return -1
                    return new Date(b) - new Date(a)
                })
                .forEach(key => {
                    sortedGroups[key] = groups[key]
                })

            return sortedGroups
        }
    },
    onLoad() {
        console.log('🚀 高性能相册页面加载')
        this.initializeGallery()
    },
    onUnload() {
        this.cleanup()
    },
    onHide() {
        // 页面隐藏时暂停不必要的操作
        this.pauseBackgroundTasks()
    },
    onShow() {
        // 页面显示时恢复操作
        this.resumeBackgroundTasks()
    },
    methods: {
        async initializeGallery() {
            console.log('开始初始化高性能相册...')

            try {
                this.initializeContainer()
                await this.loadAllMediaData()
                this.initialLoading = false



            } catch (error) {
                console.error('❌ 相册初始化失败:', error)
                this.initialLoading = false

                uni.showModal({
                    title: '初始化失败',
                    content: `相册初始化失败: ${error.message || '未知错误'}`,
                    showCancel: false,
                    confirmText: '重试',
                    success: (res) => {
                        if (res.confirm) {
                            this.initialLoading = true
                            setTimeout(() => {
                                this.initializeGallery()
                            }, 1000)
                        }
                    }
                })
            }
        },

        initializeContainer() {
            const systemInfo = uni.getSystemInfoSync()
            // 减去切换按钮的高度（约50px）
            this.containerHeight = systemInfo.windowHeight - 50
            console.log('容器高度设置为:', this.containerHeight + 'px')
        },

        // 🚀 支持分批加载的数据加载方法
        async loadAllMediaData() {
            console.log('开始加载媒体数据...')

            try {
                const hasPermission = await this.requestPermission()
                if (!hasPermission) {
                    throw new Error('相册权限被拒绝')
                }

                // 并行加载图片和视频数据
                const [firstImageBatch, firstVideoBatch] = await Promise.all([
                    this.loadBatchImages(0, this.loadBatchSize),
                    this.loadBatchVideos(0, this.loadBatchSize)
                ])

                // 设置图片数据
                this.allImageItems = firstImageBatch || []
                this.totalImages = this.allImageItems.length

                // 设置视频数据
                this.allVideoItems = firstVideoBatch || []
                this.totalVideos = this.allVideoItems.length

                this.loadOffset = this.loadBatchSize

                // 检查是否还有更多数据（基于当前显示的媒体类型）
                const currentBatch = this.currentMediaType === 'image' ? firstImageBatch : firstVideoBatch
                this.hasMoreData = currentBatch.length >= this.loadBatchSize

                console.log(`✅ 首批媒体数据加载完成: ${this.totalImages}张图片, ${this.totalVideos}个视频, 还有更多: ${this.hasMoreData}`)

            } catch (error) {
                console.error('❌ 加载媒体数据失败:', error)
                this.allImageItems = []
                this.allVideoItems = []
                this.totalImages = 0
                this.totalVideos = 0
                this.hasMoreData = false
                throw error
            }
        },

        // 🚀 高性能下拉加载功能
        
        // 加载更多图片（防抖优化）
        loadMoreImages() {
            // 防抖处理，避免频繁触发
            if (this.loadMoreTimer) {
                clearTimeout(this.loadMoreTimer)
            }

            this.loadMoreTimer = setTimeout(() => {
                this.doLoadMoreImages()
            }, 200) // 减少防抖时间，提升响应速度
        },
        
        async doLoadMoreImages() {
            if (this.loadingMore || !this.hasMoreData) {
                console.log('⚠️ 正在加载或没有更多数据')
                return
            }
            
            this.loadingMore = true
            console.log(`📱 开始加载更多图片, offset: ${this.loadOffset}`)
            
            try {
                // 根据当前媒体类型加载对应的数据
                const moreBatch = this.currentMediaType === 'image'
                    ? await this.loadBatchImages(this.loadOffset, this.loadBatchSize)
                    : await this.loadBatchVideos(this.loadOffset, this.loadBatchSize)

                if (moreBatch && moreBatch.length > 0) {
                    // 使用requestAnimationFrame优化DOM更新
                    if (this.renderTimer) {
                        clearTimeout(this.renderTimer)
                    }

                    this.renderTimer = setTimeout(() => {
                        this.$nextTick(() => {
                            // 批量添加到现有数据，避免频繁的响应式更新
                            if (this.currentMediaType === 'image') {
                                this.allImageItems.push(...moreBatch)
                                this.totalImages = this.allImageItems.length
                            } else {
                                this.allVideoItems.push(...moreBatch)
                                this.totalVideos = this.allVideoItems.length
                            }
                            this.loadOffset += moreBatch.length

                            // 检查是否还有更多数据
                            this.hasMoreData = moreBatch.length >= this.loadBatchSize

                            const mediaType = this.currentMediaType === 'image' ? '图片' : '视频'
                            const totalCount = this.currentMediaType === 'image' ? this.totalImages : this.totalVideos
                            console.log(`✅ 加载更多完成: 新增${moreBatch.length}个${mediaType}, 总计${totalCount}个, 还有更多: ${this.hasMoreData}`)
                        })
                    }, 16) // 约一帧的时间
                } else {
                    this.hasMoreData = false
                    const mediaType = this.currentMediaType === 'image' ? '图片' : '视频'
                    console.log(`⚠️ 没有更多${mediaType}了`)

                }
                
            } catch (error) {
                console.error('❌ 加载更多图片失败:', error)
                this.hasMoreData = false
                
                uni.showToast({
                    title: '加载失败，请重试',
                    icon: 'error'
                })
            } finally {
                this.loadingMore = false
            }
        },
        
        async loadBatchImages(offset = 0, limit = 50) {
            // #ifdef APP-PLUS
            console.log(`App端: 开始分批查询MediaStore, offset: ${offset}, limit: ${limit}`)
            return new Promise((resolve) => {
                try {
                    const main = plus.android.runtimeMainActivity()
                    const ContentResolver = plus.android.importClass('android.content.ContentResolver')
                    const MediaStore = plus.android.importClass('android.provider.MediaStore')
                    const ContentUris = plus.android.importClass('android.content.ContentUris')

                    const resolver = main.getContentResolver()
                    const uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI

                    // 分批查询图片
                    this.queryBatchMediaStore(resolver, uri, 'image', offset, limit).then((images) => {
                        console.log(`✅ MediaStore分批查询成功: ${images.length}张图片`)
                        resolve(images)
                    }).catch((error) => {
                        console.error('❌ MediaStore查询失败:', error)
                        resolve([])
                    })

                } catch (error) {
                    console.error('❌ 加载图片异常:', error)
                    resolve([])
                }
            })
            // #endif

            // #ifndef APP-PLUS
            // 非App端返回模拟数据
            console.log(`非App端: 生成分批模拟数据, offset: ${offset}, limit: ${limit}`)
            try {
                const allMockImages = this.generateAllMockImages(500) // 生成500张模拟图片
                const batchImages = allMockImages.slice(offset, offset + limit)
                console.log(`✅ 模拟图片分批生成成功: ${batchImages.length}张`)
                return Promise.resolve(batchImages)
            } catch (error) {
                console.error('❌ 生成模拟数据失败:', error)
                return Promise.resolve([])
            }
            // #endif
        },

        // 🚀 分批加载视频文件
        async loadBatchVideos(offset = 0, limit = 50) {
            // #ifdef APP-PLUS
            console.log(`App端: 开始分批查询视频MediaStore, offset: ${offset}, limit: ${limit}`)
            return new Promise((resolve) => {
                try {
                    const main = plus.android.runtimeMainActivity()
                    const ContentResolver = plus.android.importClass('android.content.ContentResolver')
                    const MediaStore = plus.android.importClass('android.provider.MediaStore')
                    const ContentUris = plus.android.importClass('android.content.ContentUris')

                    const resolver = main.getContentResolver()
                    const uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI

                    // 分批查询视频
                    this.queryBatchMediaStore(resolver, uri, 'video', offset, limit).then((videos) => {
                        console.log(`✅ 视频MediaStore分批查询成功: ${videos.length}个视频`)
                        resolve(videos)
                    }).catch((error) => {
                        console.error('❌ 视频MediaStore查询失败:', error)
                        resolve([])
                    })

                } catch (error) {
                    console.error('❌ 加载视频异常:', error)
                    resolve([])
                }
            })
            // #endif

            // #ifndef APP-PLUS
            // 非App端返回模拟数据
            console.log(`非App端: 生成分批模拟视频数据, offset: ${offset}, limit: ${limit}`)
            try {
                const allMockVideos = this.generateAllMockVideos(200) // 生成200个模拟视频
                const batchVideos = allMockVideos.slice(offset, offset + limit)
                console.log(`✅ 模拟视频分批生成成功: ${batchVideos.length}个`)
                return Promise.resolve(batchVideos)
            } catch (error) {
                console.error('❌ 生成模拟视频数据失败:', error)
                return Promise.resolve([])
            }
            // #endif
        },

        // 查询分批媒体文件（支持分页）
        queryBatchMediaStore(resolver, uri, type, offset = 0, limit = 50) {
            return new Promise((resolve) => {
                let cursor = null
                
                try {
                    const MediaStore = plus.android.importClass('android.provider.MediaStore')
                    const ContentUris = plus.android.importClass('android.content.ContentUris')

                    // 查询列 - 获取视频详细信息
                    const projection = [
                        MediaStore.MediaColumns._ID,
                        MediaStore.MediaColumns.DATE_MODIFIED,
                        MediaStore.MediaColumns.DATA, // 文件路径
                        MediaStore.MediaColumns.DISPLAY_NAME, // 文件名
                        MediaStore.MediaColumns.SIZE // 文件大小
                    ]

                    // 按修改时间降序排列
                    const sortOrder = `${MediaStore.MediaColumns.DATE_MODIFIED} DESC`

                    console.log(`📱 开始查询${type}文件, offset: ${offset}, limit: ${limit}`)
                    const startTime = Date.now()

                    cursor = resolver.query(uri, projection, null, null, sortOrder)
                    const mediaFiles = []
                    let processedCount = 0
                    let skippedCount = 0

                    if (cursor && plus.android.invoke(cursor, "moveToFirst")) {
                        const idColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns._ID)
                        const dateColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DATE_MODIFIED)
                        const dataColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DATA)
                        const nameColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DISPLAY_NAME)
                        const sizeColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.SIZE)

                        do {
                            try {
                                // 跳过offset之前的记录
                                if (skippedCount < offset) {
                                    skippedCount++
                                    continue
                                }
                                
                                // 达到limit数量后停止
                                if (processedCount >= limit) {
                                    console.log(`✅ 达到批次限制: ${limit}`)
                                    break
                                }

                                const id = plus.android.invoke(cursor, "getLong", idColumn)
                                const dateModified = plus.android.invoke(cursor, "getLong", dateColumn)
                                const filePath = plus.android.invoke(cursor, "getString", dataColumn)
                                const fileName = plus.android.invoke(cursor, "getString", nameColumn) || `${type}_${id}`
                                const fileSize = plus.android.invoke(cursor, "getLong", sizeColumn)

                                // 构建内容URI
                                const contentUri = ContentUris.withAppendedId(uri, id)
                                const uriString = plus.android.invoke(contentUri, "toString")

                                // 格式化文件大小
                                const formatSize = (bytes) => {
                                    if (bytes === 0) return '0 B'
                                    const k = 1024
                                    const sizes = ['B', 'KB', 'MB', 'GB']
                                    const i = Math.floor(Math.log(bytes) / Math.log(k))
                                    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
                                }

                                const mediaItem = {
                                    id: `${type}_${id}`,
                                    url: type === 'image' ? uriString : null, // 图片使用URI，视频不需要URL
                                    originalUrl: uriString, // 保存原始URL用于播放
                                    filePath: filePath,
                                    name: fileName,
                                    size: formatSize(fileSize),
                                    date: new Date(dateModified * 1000).toLocaleDateString(),
                                    dateTime: new Date(dateModified * 1000), // 保存完整日期时间用于排序
                                    type: type,
                                    loading: false,
                                    cached: false
                                }

                                mediaFiles.push(mediaItem)
                                processedCount++

                            } catch (itemError) {
                                console.error('❌ 处理媒体项失败:', itemError)
                            }
                        } while (plus.android.invoke(cursor, "moveToNext"))

                        console.log(`📋 ${type}文件遍历完成，共处理 ${processedCount} 个文件`)
                    } else {
                        console.log(`⚠️ 没有找到${type}文件或cursor为空`)
                    }

                    const loadTime = Date.now() - startTime
                    console.log(`✅ ${type}查询完成: ${mediaFiles.length}个文件, 耗时: ${loadTime}ms`)
                    resolve(mediaFiles)

                } catch (error) {
                    console.error(`❌ 查询${type}失败:`, error)
                    resolve([]) // 改为resolve空数组而不是reject
                } finally {
                    // 确保cursor被正确关闭
                    if (cursor) {
                        try {
                            plus.android.invoke(cursor, "close")
                            console.log('📝 Cursor已关闭')
                        } catch (closeError) {
                            console.error('❌ 关闭cursor失败:', closeError)
                        }
                    }
                }
            })
        },
        
        // 生成所有模拟图片数据（用于分批加载）
        generateAllMockImages(totalCount = 500) {
            const mockImages = []
            for (let i = 1; i <= totalCount; i++) {
                // 生成不同的日期，确保有多样性
                const daysAgo = Math.floor(i / 15) // 每15张图片一个日期
                const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000)

                mockImages.push({
                    id: `mock_image_${i}`,
                    url: `https://picsum.photos/300/300?random=${i}`,
                    name: `模拟图片${i}.jpg`,
                    size: `${(Math.random() * 3 + 0.5).toFixed(1)}MB`,
                    date: date.toLocaleDateString(),
                    type: 'image',
                    loading: false,
                    cached: false
                })
            }
            return mockImages
        },

        // 生成模拟图片数据（用于调试）
        generateMockImages(count = 30) {
            const mockImages = []
            for (let i = 1; i <= count; i++) {
                const daysAgo = Math.floor(i / 10)
                const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000)

                mockImages.push({
                    id: `mock_image_${i}`,
                    url: `https://picsum.photos/300/300?random=${i}`,
                    name: `模拟图片${i}.jpg`,
                    size: `${(Math.random() * 3 + 0.5).toFixed(1)}MB`,
                    date: date.toLocaleDateString(),
                    type: 'image',
                    loading: false,
                    cached: false
                })
            }
            return mockImages
        },





        // 生成所有模拟视频数据（用于分批加载）
        generateAllMockVideos(totalCount = 200) {
            const mockVideos = []
            for (let i = 1; i <= totalCount; i++) {
                // 生成不同的日期，确保有多样性
                const daysAgo = Math.floor(i / 10) // 每10个视频一个日期
                const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000)

                // 随机视频时长
                const duration = Math.floor(Math.random() * 300 + 30) // 30-330秒
                const minutes = Math.floor(duration / 60)
                const seconds = duration % 60
                const durationStr = `${minutes}:${seconds.toString().padStart(2, '0')}`

                mockVideos.push({
                    id: `mock_video_${i}`,
                    url: null, // 视频不需要URL
                    originalUrl: `https://sample-videos.com/zip/10/mp4/SampleVideo_${i}.mp4`,
                    filePath: `/storage/emulated/0/Movies/模拟视频${i}.mp4`,
                    name: `模拟视频${i}.mp4`,
                    size: `${(Math.random() * 50 + 10).toFixed(1)}MB`,
                    date: date.toLocaleDateString(),
                    dateTime: date,
                    type: 'video',
                    duration: durationStr,
                    loading: false,
                    cached: false
                })
            }
            return mockVideos
        },

        // 🚀 切换媒体类型
        switchMediaType(type) {
            if (this.currentMediaType === type) return

            console.log(`切换媒体类型: ${this.currentMediaType} -> ${type}`)
            this.currentMediaType = type

            // 重置加载状态
            this.loadOffset = this.loadBatchSize

            // 检查是否还有更多数据
            const currentItems = type === 'image' ? this.allImageItems : this.allVideoItems
            this.hasMoreData = currentItems.length >= this.loadBatchSize

            console.log(`✅ 切换完成，当前显示${type === 'image' ? '图片' : '视频'}: ${currentItems.length}个`)
        },

        async requestPermission() {
            // #ifdef APP-PLUS
            return new Promise((resolve) => {
                plus.android.requestPermissions(
                    ['android.permission.READ_EXTERNAL_STORAGE'],
                    (result) => {
                        resolve(result.granted.length > 0)
                    },
                    (error) => {
                        console.error('权限请求失败:', error)
                        resolve(false)
                    }
                )
            })
            // #endif

            // #ifndef APP-PLUS
            return Promise.resolve(true)
            // #endif
        },

        async skipToMockData() {
            try {
                this.initializeContainer()
                // 生成模拟图片和视频数据
                this.allImageItems = this.generateMockImages(30)
                this.allVideoItems = this.generateAllMockVideos(20).slice(0, 20)
                this.totalImages = this.allImageItems.length
                this.totalVideos = this.allVideoItems.length
                this.loadOffset = Math.max(this.allImageItems.length, this.allVideoItems.length)
                this.hasMoreData = false // 模拟数据不支持分批加载
                this.initialLoading = false

            } catch (error) {
                console.error('❌ 模拟数据模式失败:', error)
                this.initialLoading = false
            }
        },

        async loadQuickMode() {
            try {
                this.initializeContainer()

                const hasPermission = await this.requestPermission()
                if (!hasPermission) {
                    this.skipToMockData()
                    return
                }

                // 快速加载少量真实数据，支持后续分批加载
                const [quickImages, quickVideos] = await Promise.all([
                    this.loadBatchImages(0, 20),
                    this.loadBatchVideos(0, 10)
                ])

                this.allImageItems = quickImages
                this.allVideoItems = quickVideos
                this.totalImages = this.allImageItems.length
                this.totalVideos = this.allVideoItems.length
                this.loadOffset = 20
                this.hasMoreData = quickImages.length >= 20 || quickVideos.length >= 10
                this.initialLoading = false



            } catch (error) {
                console.error('❌ 快速加载模式失败:', error)
                this.skipToMockData()
            }
        },

        handleImageLoad(item) {
            item.loading = false
        },

        handleImageError(item) {
            item.loading = false

            // 如果是视频且缩略图加载失败，尝试使用原始视频URI
            if (item.type === 'video' && item.originalUrl && item.url !== item.originalUrl) {
                console.warn(`⚠️ 视频缩略图加载失败，尝试使用原始视频URI: ${item.id}`)
                // 在实际应用中，这里可以设置一个默认的视频封面图片
                // item.url = '/static/images/video-placeholder.png'
            }
        },

        handleItemClick(item) {
            if (item.type === 'image') {
                const imageUrls = this.allMediaItems
                    .filter(img => img.type === 'image')
                    .map(img => img.url)

                uni.previewImage({
                    urls: imageUrls,
                    current: item.url,
                    fail: (error) => {
                        uni.showToast({
                            title: '图片预览失败',
                            icon: 'error'
                        })
                    }
                })
            } else if (item.type === 'video') {
                // 处理视频点击 - 使用原始视频URL
                const videoUrl = item.originalUrl || item.url

                // 尝试播放视频
                uni.previewImage({
                    urls: [videoUrl],
                    current: videoUrl,
                    fail: (error) => {
                        console.warn('⚠️ 视频预览失败，尝试其他方式:', error)
                        uni.showToast({
                            title: '视频预览暂不支持',
                            icon: 'none',
                            duration: 2000
                        })
                    }
                })
            }
        },

        // 🚀 性能优化 - 滚动处理
        handleScroll(e) {
            // 节流处理滚动事件
            if (this.scrollTimer) {
                clearTimeout(this.scrollTimer)
            }

            this.isScrolling = true
            this.scrollTimer = setTimeout(() => {
                this.isScrolling = false
                this.optimizeVisibleItems(e.detail.scrollTop)
            }, 100)
        },

        // 优化可见项目渲染
        optimizeVisibleItems(scrollTop) {
            // 简单的可见性优化，避免复杂计算
            const viewportHeight = this.containerHeight
            const itemHeight = 100 // 估算的项目高度

            // 计算可见范围（增加缓冲区）
            const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 10)
            const endIndex = Math.min(this.allMediaItems.length, Math.ceil((scrollTop + viewportHeight) / itemHeight) + 10)

            // 更新可见项目集合
            this.visibleItems.clear()
            for (let i = startIndex; i < endIndex; i++) {
                if (this.allMediaItems[i]) {
                    this.visibleItems.add(this.allMediaItems[i].id)
                }
            }
        },

        // 暂停后台任务
        pauseBackgroundTasks() {
            if (this.backgroundLoadTimer) {
                clearTimeout(this.backgroundLoadTimer)
                this.backgroundLoadTimer = null
            }
        },

        // 恢复后台任务
        resumeBackgroundTasks() {
            // 恢复必要的后台任务
            this.scheduleBackgroundOptimization()
        },

        // 调度后台优化
        scheduleBackgroundOptimization() {
            if (this.backgroundLoadTimer) return

            this.backgroundLoadTimer = setTimeout(() => {
                this.performBackgroundOptimization()
                this.backgroundLoadTimer = null
            }, 2000)
        },

        // 执行后台优化
        performBackgroundOptimization() {
            // 清理不可见的图片缓存
            if (this.imageCache.size > 100) {
                const keysToDelete = []
                let count = 0
                for (const [key] of this.imageCache) {
                    if (count++ > 50) {
                        keysToDelete.push(key)
                    }
                }
                keysToDelete.forEach(key => this.imageCache.delete(key))
            }
        },

        cleanup() {
            // 清理定时器
            if (this.loadMoreTimer) {
                clearTimeout(this.loadMoreTimer)
                this.loadMoreTimer = null
            }

            if (this.scrollTimer) {
                clearTimeout(this.scrollTimer)
                this.scrollTimer = null
            }

            if (this.backgroundLoadTimer) {
                clearTimeout(this.backgroundLoadTimer)
                this.backgroundLoadTimer = null
            }

            if (this.renderTimer) {
                clearTimeout(this.renderTimer)
                this.renderTimer = null
            }

            // 清理缓存
            if (this.imageCache) {
                this.imageCache.clear()
            }

            if (this.visibleItems) {
                this.visibleItems.clear()
            }

            console.log('🚀 高性能相册组件已清理')
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    flex: 1;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* ==================== 加载状态样式 ==================== */

.loading {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-tip {
    color: #6b7280;
    font-size: 14px;
    margin-top: 10px;
}

.debug-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.debug-btn {
    background: #f59e0b;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
}

.quick-btn {
    background: #10b981;
}

/* ==================== 🚀 媒体类型切换按钮样式 ==================== */

.media-tabs {
    display: flex;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 16px;
    gap: 8px;
}

.media-tab {
    flex: 1;
    padding: 12px 16px;
    text-align: center;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    cursor: pointer;
    user-select: none;
    transform: translateZ(0);
    contain: layout style;
}

.media-tab:active {
    transform: scale(0.98) translateZ(0);
}

.media-tab.active {
    color: #ffffff;
    background: #3b82f6;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.media-tab:not(.active):hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
}

/* ==================== 🚀 高性能相册滚动样式 ==================== */

.gallery-scroll {
    width: 100%;
    background: #f8f9fa;
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    will-change: scroll-position;
    /* 优化滚动性能 */
    overflow-anchor: none;
    scroll-behavior: auto;
    contain: layout style paint;
}

/* 日期分组 - 优化渲染性能 */
.date-group {
    position: relative;
    width: 100%;
    contain: layout style paint;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 媒体网格 - 优化布局性能，确保整齐排列 */
.media-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
    padding: 4px;
    background: #ffffff;
    contain: layout style;
    transform: translateZ(0);
}

/* ==================== 🚀 视频列表样式 ==================== */

.video-list {
    background: #ffffff;
    padding: 8px 0;
    contain: layout style;
    transform: translateZ(0);
}

.video-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #ffffff;
    transition: background-color 0.2s ease;
    cursor: pointer;
    user-select: none;
    contain: layout style;
    transform: translateZ(0);
}

.video-item:active {
    background: #f8f9fa;
    transform: scale(0.99) translateZ(0);
}

.video-item:last-child {
    border-bottom: none;
}

.video-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    font-size: 24px;
    margin-right: 12px;
    flex-shrink: 0;
}

.video-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.video-name {
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.video-details {
    display: flex;
    gap: 12px;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.3;
}

.video-size {
    font-weight: 500;
    color: #3b82f6;
}

.video-date {
    color: #9ca3af;
}

.video-arrow {
    font-size: 20px;
    color: #d1d5db;
    margin-left: 8px;
    flex-shrink: 0;
    transition: color 0.2s ease;
}

.video-item:active .video-arrow {
    color: #9ca3af;
}

/* 媒体项 - 高性能优化，使用grid布局确保整齐 */
.media-item {
    width: 100%;
    aspect-ratio: 1;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: #f3f4f6;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transform: translateZ(0);
    will-change: transform;
    contain: layout style paint;
    /* 进一步优化性能 */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    isolation: isolate;
}

.media-item:active {
    transform: scale(0.95) translateZ(0);
}

.media-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    background: #e5e7eb;
    transition: opacity 0.2s ease;
    /* 优化图片渲染性能 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    transform: translateZ(0);
    backface-visibility: hidden;
}



/* 视频覆盖层 */
.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.video-duration {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
}

.video-play-icon {
    font-size: 24px;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}



/* 加载覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mini-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ==================== 🚀 高性能下拉加载样式 ==================== */

.load-more {
    padding: 20px;
    text-align: center;
    background: #ffffff;
    border-top: 1px solid #f0f0f0;
}

.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.loading-text {
    color: #666;
    font-size: 14px;
}

.load-more-text {
    color: #999;
    font-size: 14px;
}

.no-more {
    padding: 20px;
    text-align: center;
    background: #ffffff;
    border-top: 1px solid #f0f0f0;
}

.no-more-text {
    color: #999;
    font-size: 14px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.empty-text {
    font-size: 16px;
    color: #9ca3af;
}
</style>