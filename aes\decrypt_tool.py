#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片解密工具
用于解密被main.py加密的图片文件
"""

import os
import sqlite3
import hashlib
import argparse
from pathlib import Path
from typing import Optional, Tuple

# 检查cryptography包是否可用
try:
    from cryptography.hazmat.primitives.ciphers.aead import AESGCM
    CRYPTO_AVAILABLE = True
except ImportError:
    print("错误: cryptography 包未安装")
    print("请运行: pip install cryptography")
    exit(1)

DB_FILE = 'image_encryption.db'

class ImageDecryptor:
    def __init__(self):
        self.verify_database()

    def verify_database(self):
        """验证数据库文件是否存在且可访问"""
        if not Path(DB_FILE).exists():
            raise FileNotFoundError(f"数据库文件 {DB_FILE} 不存在，请先运行主程序处理图片")

        try:
            # 测试数据库连接和表结构
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='encrypted_images'")
            if not cursor.fetchone():
                conn.close()
                raise FileNotFoundError(f"数据库文件 {DB_FILE} 中没有找到加密图片表")

            # 检查表结构
            cursor.execute("PRAGMA table_info(encrypted_images)")
            columns = [row[1] for row in cursor.fetchall()]
            required_columns = ['file_path', 'password', 'iv', 'sha1_hash', 'created_at', 'txt']
            missing_columns = [col for col in required_columns if col not in columns]

            if missing_columns:
                conn.close()
                raise ValueError(f"数据库表结构不完整，缺少列: {missing_columns}")

            conn.close()
            print(f"✓ 数据库验证通过: {DB_FILE}")

        except sqlite3.Error as e:
            raise ValueError(f"数据库文件损坏或无法访问: {e}")
    
    def get_encryption_info(self, file_path: str) -> Optional[Tuple[str, bytes, str]]:
        """从数据库获取文件的加密信息"""
        try:
            # 确保数据库存在
            if not Path(DB_FILE).exists():
                print(f"✗ 数据库文件不存在: {DB_FILE}")
                return None

            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT password, iv, sha1_hash
                FROM encrypted_images
                WHERE file_path = ?
            ''', (file_path,))

            result = cursor.fetchone()
            conn.close()

            if result:
                password, iv_hex, sha1_hash = result
                try:
                    iv = bytes.fromhex(iv_hex)
                    return password, iv, sha1_hash
                except ValueError as e:
                    print(f"✗ IV数据格式错误: {e}")
                    return None
            return None

        except sqlite3.Error as e:
            print(f"✗ 数据库查询失败: {e}")
            return None
        except Exception as e:
            print(f"✗ 获取加密信息失败: {e}")
            return None
    
    def decrypt_file(self, file_path: Path, password: str, iv: bytes) -> bytes:
        """解密文件"""
        try:
            # 读取加密文件
            with open(file_path, 'rb') as f:
                encrypted_data = f.read()
            
            # 生成密钥
            key = hashlib.sha256(password.encode()).digest()
            aesgcm = AESGCM(key)
            
            # 解密数据
            decrypted_data = aesgcm.decrypt(iv, encrypted_data, None)
            return decrypted_data
            
        except Exception as e:
            print(f"解密失败 {file_path}: {e}")
            return None
    
    def decrypt_single_file(self, file_path: str, output_path: Optional[str] = None):
        """解密单个文件"""
        file_path_obj = Path(file_path)
        
        if not file_path_obj.exists():
            print(f"文件不存在: {file_path}")
            return False
        
        # 获取加密信息，处理路径问题
        try:
            if file_path_obj.is_absolute():
                relative_path = str(file_path_obj.relative_to(Path.cwd()))
            else:
                relative_path = str(file_path_obj)
        except ValueError:
            # 如果relative_to失败，直接使用文件路径
            relative_path = str(file_path_obj)

        # 统一使用正斜杠
        relative_path = relative_path.replace('\\', '/')
        encryption_info = self.get_encryption_info(relative_path)
        
        if not encryption_info:
            print(f"未找到文件的加密信息: {file_path}")
            return False
        
        password, iv, sha1_hash = encryption_info
        
        # 解密文件
        decrypted_data = self.decrypt_file(file_path_obj, password, iv)
        if decrypted_data is None:
            return False
        
        # 确定输出路径
        if output_path is None:
            output_path = f"{file_path_obj.stem}_decrypted{file_path_obj.suffix}"
        
        # 保存解密后的文件
        try:
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            print(f"解密成功: {file_path} -> {output_path}")
            return True
            
        except Exception as e:
            print(f"保存解密文件失败: {e}")
            return False
    
    def list_encrypted_files(self):
        """列出所有加密的文件"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT file_path, sha1_hash, created_at, capture_date, file_size_mb,
                       image_width, image_height, gps_latitude, gps_longitude
                FROM encrypted_images
                ORDER BY created_at DESC
            ''')

            results = cursor.fetchall()
            conn.close()

            if not results:
                print("数据库中没有加密文件记录")
                return

            print("加密文件列表:")
            print("=" * 120)
            print(f"{'文件路径':<35} {'尺寸':<12} {'大小(MB)':<8} {'拍摄日期':<19} {'GPS坐标':<20} {'创建时间':<19}")
            print("=" * 120)

            for record in results:
                file_path, sha1_hash, created_at, capture_date, file_size_mb, image_width, image_height, gps_lat, gps_lon = record

                # 格式化尺寸信息
                size_info = f"{image_width or '?'}x{image_height or '?'}" if image_width and image_height else "未知"

                # 格式化文件大小，精确到0.01MB
                size_mb = f"{file_size_mb:.2f}" if file_size_mb is not None else "?"

                # 格式化拍摄日期
                capture_str = capture_date[:19] if capture_date else "无"

                # 格式化GPS坐标
                if gps_lat and gps_lon:
                    gps_str = f"{gps_lat:.3f},{gps_lon:.3f}"
                else:
                    gps_str = "无"

                # 格式化创建时间
                created_str = created_at[:19] if created_at else "?"

                print(f"{file_path:<35} {size_info:<12} {size_mb:<8} {capture_str:<19} {gps_str:<20} {created_str:<19}")

        except Exception as e:
            print(f"列出文件失败: {e}")
    
    def decrypt_all_files(self, output_dir: str = "decrypted"):
        """解密所有文件到指定目录"""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            cursor.execute('SELECT DISTINCT file_path FROM encrypted_images')
            file_paths = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            if not file_paths:
                print("没有找到需要解密的文件")
                return
            
            # 创建输出目录
            output_dir_path = Path(output_dir)
            output_dir_path.mkdir(exist_ok=True)
            
            success_count = 0
            for file_path in file_paths:
                file_name = Path(file_path).name
                output_path = output_dir_path / f"{Path(file_path).stem}_decrypted{Path(file_path).suffix}"
                
                if self.decrypt_single_file(file_path, str(output_path)):
                    success_count += 1
            
            print(f"\n解密完成: {success_count}/{len(file_paths)} 个文件成功解密")
            print(f"解密文件保存在: {output_dir}")
            
        except Exception as e:
            print(f"批量解密失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='图片解密工具')
    parser.add_argument('--file', '-f', help='解密指定文件')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有加密文件')
    parser.add_argument('--all', '-a', help='解密所有文件到指定目录')
    
    args = parser.parse_args()
    
    try:
        decryptor = ImageDecryptor()
        
        if args.list:
            decryptor.list_encrypted_files()
        elif args.file:
            decryptor.decrypt_single_file(args.file, args.output)
        elif args.all:
            decryptor.decrypt_all_files(args.all)
        else:
            print("使用方法:")
            print("  python decrypt_tool.py --list                    # 列出所有加密文件")
            print("  python decrypt_tool.py --file <文件路径>          # 解密指定文件")
            print("  python decrypt_tool.py --file <文件路径> --output <输出路径>  # 解密到指定位置")
            print("  python decrypt_tool.py --all <输出目录>           # 解密所有文件到目录")
            
    except FileNotFoundError as e:
        print(f"错误: {e}")
    except Exception as e:
        print(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()
