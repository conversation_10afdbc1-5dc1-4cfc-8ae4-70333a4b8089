# Ubuntu 22 容器中安装 Docker 完整指南

## 方法一：使用官方 Docker-in-Docker 镜像（推荐）

### 1. 运行支持 DinD 的容器
```bash
# 使用官方 docker:dind 镜像
docker run -d \
  --name ubuntu-docker \
  --privileged \
  -v /var/lib/docker \
  -p 2376:2376 \
  ubuntu:22.04

# 或者如果你已有 Ubuntu 22 容器，需要重新创建为特权模式
docker run -it \
  --name ubuntu-docker \
  --privileged \
  -v /var/lib/docker \
  ubuntu:22.04 /bin/bash
```

### 2. 进入容器并安装 Docker
```bash
# 进入容器
docker exec -it ubuntu-docker /bin/bash

# 更新包管理器
apt update && apt upgrade -y

# 安装必要的依赖
apt install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    apt-transport-https \
    software-properties-common

# 添加 Docker 官方 GPG 密钥
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 添加 Docker 仓库
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# 更新包索引
apt update

# 安装 Docker Engine
apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 启动 Docker 服务
service docker start

# 验证安装
docker --version
docker run hello-world
```

## 方法二：手动配置现有容器

### 1. 如果你已有运行中的容器，需要重新创建
```bash
# 停止现有容器
docker stop your-container-name

# 创建新的特权容器
docker run -it \
  --name ubuntu-docker-new \
  --privileged \
  -v /var/lib/docker \
  -v /var/run/docker.sock:/var/run/docker.sock \
  ubuntu:22.04 /bin/bash
```

### 2. 在容器内安装 Docker（同上面步骤2）

## 方法三：使用 Docker Compose（推荐用于开发环境）

### 创建 docker-compose.yml
```yaml
version: '3.8'
services:
  ubuntu-docker:
    image: ubuntu:22.04
    container_name: ubuntu-docker
    privileged: true
    volumes:
      - /var/lib/docker
      - .:/workspace
    working_dir: /workspace
    command: /bin/bash -c "
      apt update && 
      apt install -y ca-certificates curl gnupg lsb-release apt-transport-https software-properties-common &&
      mkdir -p /etc/apt/keyrings &&
      curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg &&
      echo 'deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable' | tee /etc/apt/sources.list.d/docker.list > /dev/null &&
      apt update &&
      apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin &&
      service docker start &&
      tail -f /dev/null
      "
    stdin_open: true
    tty: true
```

### 启动容器
```bash
docker-compose up -d
docker-compose exec ubuntu-docker /bin/bash
```

## 重要注意事项

### 1. 特权模式必须启用
- `--privileged` 参数是必需的，它给予容器几乎所有主机权限
- 这允许容器内的 Docker 访问内核功能

### 2. 卷挂载
- `-v /var/lib/docker` 为 Docker 数据创建专用卷
- 可选：`-v /var/run/docker.sock:/var/run/docker.sock` 共享主机 Docker socket

### 3. 安全考虑
- 特权容器具有安全风险，仅在受信任环境中使用
- 生产环境建议使用其他方案如 Kaniko 或 Buildah

### 4. 验证功能
```bash
# 在容器内测试 Docker 功能
docker --version
docker info
docker run hello-world
docker build -t test .
docker run -d nginx
docker ps
```

## 故障排除

### 1. Docker 服务无法启动
```bash
# 检查服务状态
service docker status

# 手动启动
service docker start

# 查看日志
journalctl -u docker
```

### 2. 权限问题
```bash
# 确保容器以特权模式运行
docker inspect container-name | grep Privileged

# 检查用户组
usermod -aG docker $USER
```

### 3. 存储驱动问题
```bash
# 检查存储驱动
docker info | grep "Storage Driver"

# 如果需要，配置存储驱动
echo '{"storage-driver": "overlay2"}' > /etc/docker/daemon.json
service docker restart
```

这样配置后，你的 Ubuntu 22 容器内的 Docker 将具有与真实主机相同的功能，包括构建镜像、运行容器、网络管理等所有特性。
