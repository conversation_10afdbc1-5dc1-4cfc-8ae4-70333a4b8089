#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试数据库写入
"""

import sys
import time
import sqlite3
from pathlib import Path

# 添加aes目录到Python路径
sys.path.append(str(Path('aes')))

def test_final():
    """最终测试"""
    try:
        print("=== 最终数据库写入测试 ===")
        
        # 1. 导入数据库队列
        print("1. 导入数据库队列...")
        from database_queue import get_database_queue
        
        # 2. 获取数据库队列实例
        print("2. 获取数据库队列实例...")
        db_queue = get_database_queue()
        
        # 3. 启动数据库队列
        print("3. 启动数据库队列...")
        if not db_queue.running:
            db_queue.start()
        
        # 4. 等待启动完成
        print("4. 等待启动完成...")
        time.sleep(3)
        
        # 5. 检查初始状态
        print("5. 检查初始数据库状态...")
        db_file = Path('aes/image_encryption.db')
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
        initial_video_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM encrypted_files")
        initial_file_count = cursor.fetchone()[0]
        
        print(f"   初始视频记录数: {initial_video_count}")
        print(f"   初始文件记录数: {initial_file_count}")
        conn.close()
        
        # 6. 添加测试记录
        print("6. 添加测试记录...")
        test_video_hash = f"test_video_{int(time.time())}"
        test_file_hash = f"test_file_{int(time.time())}"
        
        db_queue.save_encrypted_video(
            sha1_hash=test_video_hash,
            original_filename='final_test.mp4',
            m3u8_path='video/final_test/index.m3u8',
            password='finaltest123456',
            iv='finaltest654321',
            file_size_mb=25.5,
            tag='final_test',
            txt='final_test_description'
        )
        print(f"   ✓ 视频记录已添加: {test_video_hash}")
        
        db_queue.save_encrypted_file(
            sha1_hash=test_file_hash,
            original_filename='final_test.txt',
            file_path='other/2025-07-30/final_test.txt',
            password='finalfile123456',
            iv='finalfile654321',
            file_size_mb=5.2,
            tag='final_test',
            txt='final_test_description'
        )
        print(f"   ✓ 文件记录已添加: {test_file_hash}")
        
        # 7. 等待队列处理
        print("7. 等待队列处理...")
        time.sleep(5)
        
        # 8. 检查最终状态
        print("8. 检查最终数据库状态...")
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
        final_video_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM encrypted_files")
        final_file_count = cursor.fetchone()[0]
        
        print(f"   最终视频记录数: {final_video_count}")
        print(f"   最终文件记录数: {final_file_count}")
        
        # 9. 查找我们添加的记录
        print("9. 查找测试记录...")
        cursor.execute("SELECT original_filename, m3u8_path FROM encrypted_videos WHERE sha1_hash = ?", (test_video_hash,))
        video_record = cursor.fetchone()
        if video_record:
            print(f"   ✓ 找到视频记录: {video_record[0]} -> {video_record[1]}")
        else:
            print(f"   ❌ 未找到视频记录: {test_video_hash}")
        
        cursor.execute("SELECT original_filename, file_path FROM encrypted_files WHERE sha1_hash = ?", (test_file_hash,))
        file_record = cursor.fetchone()
        if file_record:
            print(f"   ✓ 找到文件记录: {file_record[0]} -> {file_record[1]}")
        else:
            print(f"   ❌ 未找到文件记录: {test_file_hash}")
        
        conn.close()
        
        # 10. 结果分析
        print("10. 结果分析...")
        video_added = final_video_count > initial_video_count
        file_added = final_file_count > initial_file_count
        
        print(f"   视频记录增加: {video_added} ({final_video_count - initial_video_count})")
        print(f"   文件记录增加: {file_added} ({final_file_count - initial_file_count})")
        
        if video_added and file_added:
            print("\n✅ 测试成功！数据库写入正常工作")
            return True
        else:
            print("\n❌ 测试失败！数据库写入有问题")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_final()
