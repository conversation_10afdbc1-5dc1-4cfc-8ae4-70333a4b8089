#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从统一启动开始的完整测试
"""

import os
import sys
import time
import sqlite3
import subprocess
from pathlib import Path

def check_db_status():
    """检查数据库状态"""
    db_file = Path('aes/image_encryption.db')
    if not db_file.exists():
        return 0, 0, 0
    
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM encrypted_images")
    img_count = cursor.fetchone()[0]
    cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
    vid_count = cursor.fetchone()[0]
    cursor.execute("SELECT COUNT(*) FROM encrypted_files")
    file_count = cursor.fetchone()[0]
    
    conn.close()
    return img_count, vid_count, file_count

def create_test_files():
    """创建测试文件"""
    print("1. 创建测试文件...")
    
    # 确保目录存在
    Path('aes/temp').mkdir(parents=True, exist_ok=True)
    Path('m3u8/temp').mkdir(parents=True, exist_ok=True)
    Path('m3u8/scanfile').mkdir(parents=True, exist_ok=True)
    
    # 创建测试图片文件
    test_image = Path('aes/temp/test_unified.jpg')
    with open(test_image, 'wb') as f:
        # 创建一个简单的JPEG文件头
        f.write(b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00')
        f.write(b'fake jpeg content for testing' * 50)
        f.write(b'\xff\xd9')  # JPEG结束标记
    print(f"   ✓ 创建测试图片: {test_image}")
    
    # 创建测试视频文件
    test_video = Path('m3u8/temp/test_unified.mp4')
    with open(test_video, 'wb') as f:
        f.write(b'fake mp4 content for unified testing' * 100)
    print(f"   ✓ 创建测试视频: {test_video}")
    
    # 创建测试文件
    test_file = Path('m3u8/scanfile/test_unified.txt')
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write('这是统一测试文件\n' * 100)
    print(f"   ✓ 创建测试文件: {test_file}")
    
    return test_image, test_video, test_file

def start_unified_services():
    """启动统一服务"""
    print("2. 启动统一服务...")
    
    try:
        # 启动统一服务
        process = subprocess.Popen(
            [sys.executable, 'unified_launcher.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print("   ✓ 统一服务已启动")
        print("   等待服务初始化...")
        time.sleep(10)  # 等待服务启动
        
        return process
        
    except Exception as e:
        print(f"   ❌ 启动统一服务失败: {e}")
        return None

def monitor_processing():
    """监控文件处理过程"""
    print("3. 监控文件处理过程...")
    
    initial_img, initial_vid, initial_file = check_db_status()
    print(f"   初始记录数 - 图片: {initial_img}, 视频: {initial_vid}, 文件: {initial_file}")
    
    # 监控30秒
    for i in range(30):
        time.sleep(1)
        current_img, current_vid, current_file = check_db_status()
        
        if current_img > initial_img or current_vid > initial_vid or current_file > initial_file:
            print(f"   [{i+1}s] 检测到变化 - 图片: {current_img}, 视频: {current_vid}, 文件: {current_file}")
        
        # 如果所有文件都处理完了，提前结束
        if current_img > initial_img and current_vid > initial_vid and current_file > initial_file:
            print("   ✓ 所有文件都已处理")
            break
    
    final_img, final_vid, final_file = check_db_status()
    print(f"   最终记录数 - 图片: {final_img}, 视频: {final_vid}, 文件: {final_file}")
    
    return final_img > initial_img, final_vid > initial_vid, final_file > initial_file

def show_database_records():
    """显示数据库记录"""
    print("4. 显示数据库记录...")
    
    db_file = Path('aes/image_encryption.db')
    if not db_file.exists():
        print("   数据库文件不存在")
        return
    
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    
    # 显示图片记录
    cursor.execute("SELECT file_path, created_at FROM encrypted_images ORDER BY id DESC LIMIT 3")
    images = cursor.fetchall()
    if images:
        print("   图片记录:")
        for img in images:
            print(f"     - {img[0]} ({img[1]})")
    
    # 显示视频记录
    cursor.execute("SELECT original_filename, m3u8_path, encryption_date FROM encrypted_videos ORDER BY id DESC LIMIT 3")
    videos = cursor.fetchall()
    if videos:
        print("   视频记录:")
        for vid in videos:
            print(f"     - {vid[0]} -> {vid[1]} ({vid[2]})")
    
    # 显示文件记录
    cursor.execute("SELECT original_filename, file_path, encryption_date FROM encrypted_files ORDER BY id DESC LIMIT 3")
    files = cursor.fetchall()
    if files:
        print("   文件记录:")
        for file in files:
            print(f"     - {file[0]} -> {file[1]} ({file[2]})")
    
    conn.close()

def cleanup_test_files():
    """清理测试文件"""
    print("5. 清理测试文件...")
    
    test_files = [
        Path('aes/temp/test_unified.jpg'),
        Path('m3u8/temp/test_unified.mp4'),
        Path('m3u8/scanfile/test_unified.txt')
    ]
    
    for file_path in test_files:
        if file_path.exists():
            file_path.unlink()
            print(f"   ✓ 删除: {file_path}")

def main():
    """主函数"""
    print("=== 从统一启动开始的完整测试 ===")
    print()
    
    # 检查初始状态
    initial_img, initial_vid, initial_file = check_db_status()
    print(f"初始数据库状态 - 图片: {initial_img}, 视频: {initial_vid}, 文件: {initial_file}")
    print()
    
    # 创建测试文件
    test_image, test_video, test_file = create_test_files()
    print()
    
    # 启动统一服务
    process = start_unified_services()
    if not process:
        print("❌ 无法启动统一服务，测试终止")
        return
    
    print()
    
    try:
        # 监控处理过程
        img_success, vid_success, file_success = monitor_processing()
        print()
        
        # 显示数据库记录
        show_database_records()
        print()
        
        # 结果总结
        print("="*50)
        print("测试结果:")
        print(f"  图片服务: {'✅ 正常' if img_success else '❌ 异常'}")
        print(f"  视频服务: {'✅ 正常' if vid_success else '❌ 异常'}")
        print(f"  文件服务: {'✅ 正常' if file_success else '❌ 异常'}")
        
        if img_success and vid_success and file_success:
            print("\n🎉 所有服务都正常工作并写入数据库！")
        else:
            print("\n⚠ 部分服务未正常工作")
            
            if not img_success:
                print("   - 图片服务可能有问题")
            if not vid_success:
                print("   - 视频服务可能有问题")
            if not file_success:
                print("   - 文件服务可能有问题")
    
    finally:
        # 停止服务
        print("\n6. 停止服务...")
        try:
            process.terminate()
            process.wait(timeout=5)
            print("   ✓ 服务已停止")
        except:
            process.kill()
            print("   ✓ 服务已强制停止")
        
        # 清理测试文件
        cleanup_test_files()

if __name__ == "__main__":
    main()
