/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.webdav-container[data-v-8b618efa] {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 配置区域样式 */
.config-section[data-v-8b618efa] {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.config-header[data-v-8b618efa] {
  text-align: center;
  margin-bottom: 30px;
}
.config-title[data-v-8b618efa] {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}
.config-subtitle[data-v-8b618efa] {
  font-size: 14px;
  color: #666;
  display: block;
}
.config-form[data-v-8b618efa] {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.form-item[data-v-8b618efa] {
  margin-bottom: 20px;
}
.form-label[data-v-8b618efa] {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}
.form-input[data-v-8b618efa] {
  width: 100%;
  height: 44px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 16px;
  background: #fff;
}
.form-input[data-v-8b618efa]:focus {
  border-color: #007AFF;
  outline: none;
}
.form-actions[data-v-8b618efa] {
  margin-top: 30px;
}
.connect-btn[data-v-8b618efa] {
  width: 100%;
  height: 48px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}
.connect-btn[data-v-8b618efa]:disabled {
  background: #ccc;
}
.form-help[data-v-8b618efa] {
  margin-top: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007AFF;
}
.help-text[data-v-8b618efa] {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}
.help-example[data-v-8b618efa] {
  display: block;
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
  font-family: monospace;
  word-break: break-all;
}
.help-btn[data-v-8b618efa] {
  margin-top: 8px;
  padding: 6px 12px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
}

/* 文件浏览区域样式 */
.file-browser[data-v-8b618efa] {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.toolbar[data-v-8b618efa] {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
.path-bar[data-v-8b618efa] {
  flex: 1;
  min-width: 0;
}
.current-path[data-v-8b618efa] {
  font-size: 14px;
  color: #666;
  word-break: break-all;
}
.toolbar-actions[data-v-8b618efa] {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.action-btn[data-v-8b618efa] {
  padding: 6px 12px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
}
.disconnect-btn[data-v-8b618efa] {
  background: #ff3b30;
}
.breadcrumb[data-v-8b618efa] {
  background: #f8f9fa;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.breadcrumb-item[data-v-8b618efa] {
  font-size: 14px;
  color: #007AFF;
  cursor: pointer;
  margin-right: 4px;
}
.breadcrumb-separator[data-v-8b618efa] {
  color: #999;
  margin: 0 4px;
}
.loading-section[data-v-8b618efa] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}
.spinner[data-v-8b618efa] {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007AFF;
  border-radius: 50%;
  animation: spin-8b618efa 1s linear infinite;
  margin-bottom: 16px;
}
@keyframes spin-8b618efa {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text[data-v-8b618efa] {
  color: #666;
  font-size: 14px;
}
.file-list[data-v-8b618efa] {
  flex: 1;
  background: white;
}
.file-item[data-v-8b618efa] {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}
.file-item[data-v-8b618efa]:active {
  background: #f8f9fa;
}
.directory-item[data-v-8b618efa] {
  background: #f8f9ff;
}
.file-icon[data-v-8b618efa] {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
}
.file-info[data-v-8b618efa] {
  flex: 1;
  min-width: 0;
}
.file-name[data-v-8b618efa] {
  display: block;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  word-break: break-all;
  margin-bottom: 4px;
}
.file-details[data-v-8b618efa] {
  display: block;
  font-size: 12px;
  color: #999;
}
.file-date[data-v-8b618efa] {
  margin-left: 8px;
}
.file-actions[data-v-8b618efa] {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.action-icon[data-v-8b618efa] {
  font-size: 16px;
  color: #999;
}
.empty-state[data-v-8b618efa] {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}
.empty-icon[data-v-8b618efa] {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}
.empty-text[data-v-8b618efa] {
  font-size: 16px;
}

/* 进度对话框样式 */
.progress-overlay[data-v-8b618efa] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.progress-dialog[data-v-8b618efa] {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin: 20px;
  min-width: 280px;
  max-width: 90%;
}
.progress-title[data-v-8b618efa] {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 16px;
  display: block;
}
.progress-bar[data-v-8b618efa] {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}
.progress-fill[data-v-8b618efa] {
  height: 100%;
  background: #007AFF;
  transition: width 0.3s ease;
}
.progress-text[data-v-8b618efa] {
  font-size: 14px;
  color: #666;
  text-align: center;
  display: block;
  margin-bottom: 16px;
}
.cancel-btn[data-v-8b618efa] {
  width: 100%;
  height: 40px;
  background: #ff3b30;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
}