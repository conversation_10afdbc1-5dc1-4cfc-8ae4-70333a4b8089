#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的数据库监控测试
"""

from pathlib import Path
import time

def test_30_second_polling():
    """测试30秒轮询功能"""
    print("=" * 60)
    print("30秒轮询测试")
    print("=" * 60)
    
    # 清理环境
    db_file = Path('image_encryption.db')
    if db_file.exists():
        db_file.unlink()
        print("✓ 清理现有数据库文件")
    
    # 启动数据库队列
    print("\n启动数据库队列...")
    from database_queue import start_database_queue, stop_database_queue
    
    db_queue = start_database_queue()
    
    # 等待初始创建
    time.sleep(3)
    
    if db_file.exists():
        print("✓ 数据库文件已自动创建")
    else:
        print("✗ 数据库文件未创建")
        stop_database_queue()
        return False
    
    # 删除数据库文件
    print("\n删除数据库文件，测试30秒轮询...")
    db_file.unlink()
    print("✓ 数据库文件已删除")
    
    # 监控35秒，看是否在30秒左右重新创建
    print("监控35秒，期待在30秒左右重新创建数据库...")
    
    start_time = time.time()
    recreated = False
    
    for i in range(35):
        time.sleep(1)
        current_time = time.time() - start_time
        
        if db_file.exists() and not recreated:
            print(f"✓ 数据库在 {current_time:.1f} 秒后被重新创建")
            recreated = True
            break
        
        if (i + 1) % 5 == 0:
            exists = "存在" if db_file.exists() else "不存在"
            print(f"  {current_time:.1f}秒: 数据库文件{exists}")
    
    if not recreated:
        print("✗ 数据库未在35秒内重新创建")
        stop_database_queue()
        return False
    
    # 验证数据库结构
    print("\n验证重新创建的数据库结构...")
    try:
        import sqlite3
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(encrypted_images)")
        columns = [row[1] for row in cursor.fetchall()]
        expected_columns = ['id', 'file_path', 'password', 'iv', 'sha1_hash', 'created_at', 'txt']
        
        if set(columns) == set(expected_columns):
            print("✓ 数据库表结构正确")
        else:
            print(f"✗ 数据库表结构不正确: {columns}")
            conn.close()
            stop_database_queue()
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"✗ 验证数据库失败: {e}")
        stop_database_queue()
        return False
    
    # 测试数据库操作
    print("\n测试数据库操作...")
    db_queue.save_encrypted_image(
        file_path="test/polling_test.jpg",
        password="polling_password",
        iv="polling_iv",
        sha1_hash="polling_sha1",
        txt="30秒轮询测试"
    )
    
    time.sleep(2)
    
    # 验证数据
    try:
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM encrypted_images WHERE txt = ?", ("30秒轮询测试",))
        count = cursor.fetchone()[0]
        
        if count > 0:
            print("✓ 数据库操作正常")
        else:
            print("✗ 数据库操作失败")
            conn.close()
            stop_database_queue()
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"✗ 验证数据库操作失败: {e}")
        stop_database_queue()
        return False
    
    print("\n停止数据库队列...")
    stop_database_queue()
    
    return True

def main():
    """主函数"""
    print("数据库30秒轮询测试")
    
    try:
        success = test_30_second_polling()
        
        print("\n" + "=" * 60)
        if success:
            print("✓ 30秒轮询测试成功！")
            print("数据库监控功能正常工作：")
            print("- 每30秒检查数据库是否存在")
            print("- 不存在时自动创建标准表结构")
            print("- 支持正常的数据库操作")
        else:
            print("✗ 30秒轮询测试失败")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
