<template>
  <view class="filebrowser-container">
    <!-- 顶部标签栏 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'images' }"
        @click="switchTab('images')"
      >
        <text class="tab-text">图片</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'videos' }"
        @click="switchTab('videos')"
      >
        <text class="tab-text">视频</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'files' }"
        @click="switchTab('files')"
      >
        <text class="tab-text">文件</text>
      </view>
    </view>

    <!-- 设置按钮 -->
    <view class="settings-bar">
      <button class="settings-btn" @click="showSettings">
        <text class="settings-text">⚙️ 设置</text>
      </button>
      <button class="refresh-btn" @click="refreshData" :disabled="loading">
        <text class="refresh-text">🔄 刷新</text>
      </button>
      <button class="debug-btn" @click="debugDatabase">
        <text class="debug-text">🔍 调试</text>
      </button>
      <button class="import-btn" @click="importDatabase">
        <text class="import-text">📁 导入数据库</text>
      </button>
      <button class="test-btn" @click="testDatabase">
        <text class="test-text">🧪 测试数据库</text>
      </button>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text class="loading-text">{{ loadingProgress.text || '正在加载...' }}</text>
        <view v-if="loadingProgress.total > 0" class="progress-info">
          <text class="progress-text">{{ loadingProgress.current }}/{{ loadingProgress.total }}</text>
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: (loadingProgress.current / loadingProgress.total * 100) + '%' }"
            ></view>
          </view>
        </view>
      </view>

      <!-- 图片列表 -->
      <view v-else-if="activeTab === 'images'" class="images-grid">
        <view
          v-for="image in imageList"
          :key="image.id"
          class="image-item"
          :class="{ 'decrypt-error': !image.decryptionSuccess }"
          @click="previewImage(image)"
        >
          <view class="image-container">
            <image
              :src="image.thumbnailUrl"
              class="image-thumbnail"
              mode="aspectFill"
              @error="onImageError"
              @load="onImageLoad"
            />
            <view v-if="!image.decryptionSuccess" class="error-overlay">
              <text class="error-text">解密失败</text>
            </view>
          </view>
          <view class="image-info">
            <text class="image-name">{{ image.name }}</text>
            <text class="image-size">{{ image.size }}</text>
            <text v-if="image.captureDate" class="image-date">{{ image.captureDate }}</text>
            <text v-if="!image.decryptionSuccess && image.decryptionError" class="error-msg">
              {{ image.decryptionError }}
            </text>
          </view>
        </view>
      </view>

      <!-- 视频列表 -->
      <view v-else-if="activeTab === 'videos'" class="videos-list">
        <text class="placeholder-text">视频功能开发中...</text>
      </view>

      <!-- 文件列表 -->
      <view v-else-if="activeTab === 'files'" class="files-list">
        <text class="placeholder-text">文件功能开发中...</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && imageList.length === 0 && activeTab === 'images'" class="empty-state">
        <text class="empty-text">暂无图片数据</text>
        <text class="empty-hint">请检查数据库文件和设置配置</text>
      </view>
    </view>

    <!-- 设置弹窗 -->
    <view v-if="showSettingsModal" class="settings-modal" @click="hideSettings">
      <view class="settings-content" @click.stop>
        <view class="settings-header">
          <text class="settings-title">文件浏览设置</text>
          <button class="close-btn" @click="hideSettings">×</button>
        </view>
        
        <view class="settings-form">
          <view class="form-item">
            <text class="form-label">服务器域名</text>
            <textarea
              class="form-textarea"
              v-model="settings.domain"
              placeholder="https://example.com"
              auto-height
              maxlength="500"
            />
          </view>

          <view class="form-item">
            <text class="form-label">访问密钥</text>
            <textarea
              class="form-textarea"
              v-model="settings.accessKey"
              placeholder="请输入访问密钥"
              auto-height
              maxlength="500"
            />
          </view>
          
          <view class="form-actions">
            <button class="save-btn" @click="saveSettings">保存设置</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import databaseManager from '@/utils/database.js'
import { buildImageUrls } from '@/utils/urlBuilder.js'
import { batchDecryptImages, revokeImageObjectURL } from '@/utils/crypto.js'
import { selectAndCopyDatabase, checkDatabaseExists, getDatabaseInfo } from '@/utils/dbHelper.js'

export default {
  name: 'FileBrowser',
  data() {
    return {
      activeTab: 'images',
      loading: false,
      imageList: [],
      videoList: [],
      fileList: [],
      showSettingsModal: false,
      settings: {
        domain: '',
        accessKey: ''
      },
      decryptedUrls: [], // 存储已解密的Object URLs，用于清理
      loadingProgress: {
        current: 0,
        total: 0,
        text: ''
      }
    }
  },

  onLoad() {
    console.log('文件浏览页面加载')
    this.loadSettings()
    this.loadData()
  },

  onUnload() {
    // 页面卸载时清理Object URLs
    this.cleanupDecryptedUrls()
  },
  
  methods: {
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab
      this.loadData()
    },
    
    // 加载数据
    async loadData() {
      if (this.activeTab === 'images') {
        await this.loadImages()
      } else if (this.activeTab === 'videos') {
        await this.loadVideos()
      } else if (this.activeTab === 'files') {
        await this.loadFiles()
      }
    },
    
    // 加载图片数据
    async loadImages() {
      this.loading = true
      this.loadingProgress = { current: 0, total: 0, text: '正在读取数据库...' }

      try {
        // 检查设置
        if (!this.settings.domain || !this.settings.accessKey) {
          uni.showModal({
            title: '设置未完成',
            content: '请先配置服务器域名和访问密钥',
            showCancel: false,
            confirmText: '去设置',
            success: () => {
              this.showSettings()
            }
          })
          return
        }

        console.log('开始加载图片数据...')

        // 从数据库读取图片数据
        const rawImageList = await databaseManager.getEncryptedImages(50, 0)
        console.log('从数据库获取到图片数据:', rawImageList.length, '条')

        if (rawImageList.length === 0) {
          this.imageList = []
          return
        }

        this.loadingProgress.text = '正在构建图片URL...'

        // 构建图片URL
        const imageListWithUrls = await buildImageUrls(
          rawImageList,
          this.settings.domain,
          this.settings.accessKey
        )

        this.loadingProgress = {
          current: 0,
          total: imageListWithUrls.length,
          text: '正在解密图片...'
        }

        // 批量解密图片
        const decryptedImageList = await batchDecryptImages(
          imageListWithUrls,
          (current, total) => {
            this.loadingProgress.current = current
            this.loadingProgress.total = total
          }
        )

        // 处理图片数据，添加显示所需的字段
        this.imageList = decryptedImageList.map(image => ({
          id: image.id,
          name: this.extractFileName(image.file_path),
          size: image.file_size_mb ? `${image.file_size_mb.toFixed(2)}MB` : '未知',
          thumbnailUrl: image.decryptedThumbnailUrl || '/static/images/placeholder.jpg',
          originalUrl: image.originalUrl,
          password: image.password,
          iv: image.iv,
          decryptionSuccess: image.decryptionSuccess,
          decryptionError: image.decryptionError,
          captureDate: image.capture_date,
          imageWidth: image.image_width,
          imageHeight: image.image_height
        }))

        // 记录解密成功的URLs用于后续清理
        this.decryptedUrls = decryptedImageList
          .filter(img => img.decryptedThumbnailUrl)
          .map(img => img.decryptedThumbnailUrl)

        console.log('图片加载完成:', this.imageList.length, '张，解密成功:',
          this.imageList.filter(img => img.decryptionSuccess).length, '张')

      } catch (error) {
        console.error('加载图片失败:', error)
        uni.showModal({
          title: '加载失败',
          content: `加载图片失败: ${error.message}`,
          showCancel: false
        })
      } finally {
        this.loading = false
        this.loadingProgress = { current: 0, total: 0, text: '' }
      }
    },
    
    // 加载视频数据
    async loadVideos() {
      this.loading = true
      try {
        // TODO: 实现视频数据加载
        this.videoList = []
      } catch (error) {
        console.error('加载视频失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 加载文件数据
    async loadFiles() {
      this.loading = true
      try {
        // TODO: 实现文件数据加载
        this.fileList = []
      } catch (error) {
        console.error('加载文件失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 预览图片
    previewImage(image) {
      console.log('预览图片:', image)
      // TODO: 实现图片预览功能
    },
    
    // 显示设置
    showSettings() {
      this.showSettingsModal = true
    },
    
    // 隐藏设置
    hideSettings() {
      this.showSettingsModal = false
    },
    
    // 加载设置
    loadSettings() {
      try {
        const savedSettings = uni.getStorageSync('filebrowser_settings')
        if (savedSettings) {
          this.settings = { ...this.settings, ...savedSettings }
        }
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },
    
    // 保存设置
    saveSettings() {
      try {
        uni.setStorageSync('filebrowser_settings', this.settings)
        uni.showToast({
          title: '设置保存成功',
          icon: 'success'
        })
        this.hideSettings()
        this.loadData() // 重新加载数据
      } catch (error) {
        console.error('保存设置失败:', error)
        uni.showToast({
          title: '保存设置失败',
          icon: 'none'
        })
      }
    },
    
    // 图片加载错误
    onImageError(e) {
      console.error('图片加载失败:', e)
    },
    
    // 图片加载成功
    onImageLoad(e) {
      console.log('图片加载成功:', e)
    },

    // 提取文件名
    extractFileName(filePath) {
      if (!filePath) return '未知文件'
      const parts = filePath.split('/')
      return parts[parts.length - 1] || '未知文件'
    },

    // 清理解密后的Object URLs
    cleanupDecryptedUrls() {
      this.decryptedUrls.forEach(url => {
        if (url) {
          revokeImageObjectURL(url)
        }
      })
      this.decryptedUrls = []
      console.log('已清理所有解密的Object URLs')
    },

    // 刷新数据
    async refreshData() {
      this.cleanupDecryptedUrls()
      await this.loadData()
    },

    // 调试数据库
    async debugDatabase() {
      try {
        console.log('开始调试数据库...')

        // 检查运行环境
        if (typeof plus === 'undefined') {
          uni.showModal({
            title: '调试信息',
            content: '当前运行在非App环境，使用模拟数据',
            showCancel: false
          })
          return
        }

        // 获取应用私有目录信息
        const docPath = plus.io.convertLocalFileSystemURL('_doc/')
        console.log('应用私有目录路径:', docPath)

        // 检查doc文件夹
        const docFolderPath = docPath + 'doc/'
        const docExists = await databaseManager.checkFileExists(docFolderPath)
        console.log('doc文件夹存在:', docExists)

        // 检查数据库文件
        const dbPath = docPath + 'doc/image_encryption.db'
        const dbExists = await databaseManager.checkFileExists(dbPath)
        console.log('数据库文件存在:', dbExists)

        // 列出doc文件夹内容
        if (docExists) {
          await databaseManager.listDirectoryContents(docFolderPath)
        }

        // 列出_doc根目录内容
        await databaseManager.listDirectoryContents(docPath)

        let debugInfo = `调试信息:\n`
        debugInfo += `应用私有目录: ${docPath}\n`
        debugInfo += `doc文件夹存在: ${docExists}\n`
        debugInfo += `数据库文件存在: ${dbExists}\n`
        debugInfo += `数据库路径: ${dbPath}\n\n`
        debugInfo += `请检查控制台日志获取详细信息`

        uni.showModal({
          title: '数据库调试',
          content: debugInfo,
          showCancel: false
        })

      } catch (error) {
        console.error('调试失败:', error)
        uni.showModal({
          title: '调试失败',
          content: `调试过程中出错: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 导入数据库
    async importDatabase() {
      try {
        if (typeof plus === 'undefined') {
          uni.showModal({
            title: '功能限制',
            content: '数据库导入功能仅在App环境中可用',
            showCancel: false
          })
          return
        }

        // 检查当前数据库状态
        const exists = await checkDatabaseExists()
        if (exists) {
          const confirm = await new Promise((resolve) => {
            uni.showModal({
              title: '数据库已存在',
              content: '检测到已有数据库文件，是否要替换？',
              success: (res) => resolve(res.confirm)
            })
          })

          if (!confirm) {
            return
          }
        }

        // 选择并复制数据库文件
        uni.showLoading({ title: '正在导入...' })
        const success = await selectAndCopyDatabase()
        uni.hideLoading()

        if (success) {
          // 重新初始化数据库管理器
          databaseManager.isInitialized = false
          await this.refreshData()
        }

      } catch (error) {
        uni.hideLoading()
        console.error('导入数据库失败:', error)
        uni.showModal({
          title: '导入失败',
          content: `导入数据库失败: ${error.message}`,
          showCancel: false
        })
      }
    },

    // 导入数据库
    async importDatabase() {
      try {
        if (typeof plus === 'undefined') {
          uni.showModal({
            title: '功能限制',
            content: '数据库导入功能仅在App环境中可用',
            showCancel: false
          })
          return
        }

        // 检查当前数据库状态
        const exists = await checkDatabaseExists()
        if (exists) {
          const confirm = await new Promise((resolve) => {
            uni.showModal({
              title: '数据库已存在',
              content: '检测到已有数据库文件，是否要替换？',
              success: (res) => resolve(res.confirm)
            })
          })

          if (!confirm) {
            return
          }
        }

        // 选择并复制数据库文件
        uni.showLoading({ title: '正在导入...' })
        const success = await selectAndCopyDatabase()
        uni.hideLoading()

        if (success) {
          // 重新初始化数据库管理器
          databaseManager.isInitialized = false
          await this.refreshData()
        }

      } catch (error) {
        uni.hideLoading()
        console.error('导入数据库失败:', error)
        uni.showModal({
          title: '导入失败',
          content: `导入数据库失败: ${error.message}`,
          showCancel: false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filebrowser-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.tab-bar {
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  
  .tab-item {
    flex: 1;
    padding: 15px;
    text-align: center;
    border-bottom: 3px solid transparent;
    
    &.active {
      border-bottom-color: #007AFF;
      
      .tab-text {
        color: #007AFF;
        font-weight: bold;
      }
    }
    
    .tab-text {
      font-size: 16px;
      color: #666;
    }
  }
}

.settings-bar {
  padding: 10px 15px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 10px;

  .settings-btn, .refresh-btn, .debug-btn, .import-btn {
    background: #007AFF;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;

    &:disabled {
      background: #ccc;
      opacity: 0.6;
    }

    .settings-text, .refresh-text, .debug-text, .import-text {
      color: white;
    }
  }

  .refresh-btn {
    background: #28a745;
  }

  .debug-btn {
    background: #ffc107;
  }

  .import-btn {
    background: #6f42c1;
  }
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  padding: 20px;

  .loading-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 15px;
  }

  .progress-info {
    width: 100%;
    max-width: 300px;

    .progress-text {
      font-size: 14px;
      color: #999;
      text-align: center;
      display: block;
      margin-bottom: 8px;
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #e9ecef;
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: #007AFF;
        transition: width 0.3s ease;
      }
    }
  }
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  
  .image-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    &.decrypt-error {
      border: 2px solid #dc3545;
      opacity: 0.8;
    }

    .image-container {
      position: relative;

      .image-thumbnail {
        width: 100%;
        height: 150px;
        background: #f5f5f5;
      }

      .error-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(220, 53, 69, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;

        .error-text {
          color: white;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }

    .image-info {
      padding: 10px;

      .image-name {
        display: block;
        font-size: 12px;
        color: #333;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .image-size {
        font-size: 11px;
        color: #999;
        margin-bottom: 2px;
      }

      .image-date {
        font-size: 10px;
        color: #aaa;
        margin-bottom: 2px;
      }

      .error-msg {
        font-size: 10px;
        color: #dc3545;
        word-break: break-word;
      }
    }
  }
}

.placeholder-text {
  text-align: center;
  color: #999;
  font-size: 16px;
  margin-top: 50px;
}

.empty-state {
  text-align: center;
  margin-top: 50px;
  
  .empty-text {
    display: block;
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
  }
  
  .empty-hint {
    font-size: 14px;
    color: #999;
  }
}

.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  .settings-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    
    .settings-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #e9ecef;
      
      .settings-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
      
      .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        color: #999;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    
    .settings-form {
      padding: 20px;
      
      .form-item {
        margin-bottom: 20px;
        
        .form-label {
          display: block;
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
        }
        
        .form-textarea {
          width: 100%;
          min-height: 80px;
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 14px;
          box-sizing: border-box;
          resize: vertical;
          line-height: 1.4;
        }
      }
      
      .form-actions {
        text-align: center;
        
        .save-btn {
          background: #007AFF;
          color: white;
          border: none;
          border-radius: 6px;
          padding: 12px 30px;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
