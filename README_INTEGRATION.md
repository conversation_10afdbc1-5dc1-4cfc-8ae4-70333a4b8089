# 统一加密服务集成系统

## 概述

本系统将原有的图片加密功能与新的视频加密、其他文件加密功能完美集成，通过统一的数据库队列管理所有加密任务，实现一键启动所有服务。

## 功能特性

### 1. 图片加密服务 (aes/)
- **功能**: 监控 `aes/temp/` 目录，自动加密图片文件
- **算法**: AES-GCM 加密
- **数据库表**: `encrypted_images`
- **特性**: 支持EXIF信息提取、GPS定位、缩略图生成

### 2. 视频加密服务 (m3u8/)
- **功能**: 监控 `m3u8/temp/` 目录，自动加密MP4视频文件
- **算法**: 
  - 小文件(≤120MB): AES-GCM 直接加密
  - 大文件(>120MB): HLS M3U8 分片加密
- **数据库表**: `encrypted_videos`
- **特性**: 自动生成缩略图、支持播放器兼容

### 3. 其他文件加密服务 (m3u8/)
- **功能**: 监控 `m3u8/scanfile/` 目录，加密除MP4视频和非动图图片外的所有文件
- **算法**: AES-GCM 加密
- **数据库表**: `encrypted_files`
- **特性**: 智能文件类型过滤、按日期分类存储

### 4. 统一数据库队列
- **数据库**: `aes/image_encryption.db` (SQLite)
- **表结构**:
  - `encrypted_images`: 图片加密信息
  - `encrypted_videos`: 视频加密信息  
  - `encrypted_files`: 其他文件加密信息
- **特性**: 高性能队列处理、事务安全、索引优化

## 数据库表结构

### encrypted_videos 表
```sql
CREATE TABLE encrypted_videos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sha1_hash TEXT UNIQUE NOT NULL,           -- 文件SHA1哈希值
    original_filename TEXT NOT NULL,          -- 原始文件名
    m3u8_path TEXT NOT NULL,                 -- M3U8文件路径(兼容播放器)
    encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 加密日期
    password TEXT NOT NULL,                   -- 加密密码(十六进制)
    iv TEXT NOT NULL,                        -- IV值(十六进制)
    file_created_date TEXT DEFAULT NULL,      -- 文件创建日期
    file_size_mb REAL NOT NULL,              -- 文件大小(MB,精确到0.00)
    tag TEXT DEFAULT '',                     -- 预留标签字段
    txt TEXT DEFAULT ''                      -- 预留文本字段
);
```

### encrypted_files 表
```sql
CREATE TABLE encrypted_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sha1_hash TEXT UNIQUE NOT NULL,           -- 文件SHA1哈希值
    original_filename TEXT NOT NULL,          -- 原始文件名
    file_path TEXT NOT NULL,                 -- 加密文件路径
    encryption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 加密日期
    password TEXT NOT NULL,                   -- 加密密码(十六进制)
    iv TEXT NOT NULL,                        -- IV值(十六进制)
    file_created_date TEXT DEFAULT NULL,      -- 文件创建日期
    file_size_mb REAL NOT NULL,              -- 文件大小(MB,精确到0.00)
    tag TEXT DEFAULT '',                     -- 预留标签字段
    txt TEXT DEFAULT ''                      -- 预留文本字段
);
```

## 使用方法

### 一键启动所有服务
```bash
python unified_launcher.py
```

### 单独启动服务
```bash
# 图片加密服务
cd aes && python main.py

# 视频加密服务  
cd m3u8 && python video_processor.py

# 其他文件加密服务
cd m3u8 && python start_file_encryptor.py
```

### 集成测试
```bash
python test_integration.py
```

## 监控目录

- **图片文件**: `aes/temp/` → 加密后存储到按SHA1命名的目录
- **视频文件**: `m3u8/temp/` → 加密后存储到 `m3u8/video/SHA1/`
- **其他文件**: `m3u8/scanfile/` → 加密后存储到 `m3u8/other/日期/`

## 重要特性

### 1. 安全性增强
- **M3U8密钥隐藏**: 移除M3U8文件中的密钥信息，仅保存在数据库中
- **SHA1去重**: 基于文件内容的SHA1哈希避免重复加密
- **AES-GCM**: 使用认证加密算法确保数据完整性

### 2. 播放器兼容性
- **路径兼容**: M3U8路径格式兼容现有的 `fixed_player.html` 播放器
- **密钥管理**: 播放器需要从数据库获取密钥信息进行解密

### 3. 性能优化
- **数据库索引**: 为常用查询字段创建索引
- **队列处理**: 异步数据库操作避免阻塞文件处理
- **内存管理**: 大文件分块处理避免内存溢出

### 4. 跨平台支持
- **Windows**: 完全支持，使用适当的路径分隔符
- **Linux**: 完全支持，自动适配系统差异

## 注意事项

1. **数据库备份**: 定期备份 `aes/image_encryption.db` 文件
2. **密钥安全**: 数据库中的密钥为十六进制格式，需要妥善保护
3. **磁盘空间**: 确保有足够空间存储加密文件和数据库
4. **依赖检查**: 确保安装了 `cryptography` 库和 `ffmpeg` 工具

## 故障排除

### 常见问题
1. **数据库锁定**: 重启服务或检查是否有其他进程占用数据库
2. **文件权限**: 确保程序对监控目录有读写权限
3. **编码问题**: Windows下可能出现Unicode编码问题，已修复为使用ASCII字符
4. **Unicode错误**: 如果遇到 `UnicodeEncodeError`，运行 `python fix_unicode.py` 修复

### 日志查看
所有服务都会输出详细的日志信息，便于问题诊断和性能监控。

## 开发说明

本系统采用模块化设计，各服务相互独立但共享数据库队列。修改时请注意：

1. **不要修改** `aes/` 目录下的原有图片处理功能
2. **数据库操作** 必须通过队列进行，避免直接操作数据库
3. **路径处理** 使用 `pathlib.Path` 确保跨平台兼容性
4. **错误处理** 添加适当的异常处理和日志记录
