import sqlite3

# 连接到数据库
conn = sqlite3.connect('aes/image_encryption.db')
cursor = conn.cursor()

# 获取所有表的结构
cursor.execute("SELECT sql FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()

print("=== 数据库表结构 ===")
for table in tables:
    if table[0]:
        print(table[0])
        print("-" * 50)

# 查看encrypted_images表的数据
print("\n=== encrypted_images 表数据示例 ===")
cursor.execute("SELECT * FROM encrypted_images LIMIT 3")
rows = cursor.fetchall()

# 获取列名
cursor.execute("PRAGMA table_info(encrypted_images)")
columns = cursor.fetchall()
print("列名:", [col[1] for col in columns])

for row in rows:
    print(row)

conn.close()
