#!/bin/bash

#=============================================================================
# Ente IP 更换脚本 (Ente IP Change Script)
# 
# 功能: 一键更换 Ente 自托管服务的 IP 地址配置
# 作者: AI Assistant
# 版本: 1.0
# 
# 使用方法:
#   ./change_ente_ip.sh [新IP地址]
#   
# 示例:
#   ./change_ente_ip.sh *************
#   ./change_ente_ip.sh auto  # 自动检测本机IP
#
# 注意: 请在 my-ente 目录中运行此脚本
#=============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Ente IP 更换脚本

使用方法:
    $0 <新IP地址>
    $0 auto                    # 自动检测本机IP
    $0 --help                  # 显示帮助信息
    $0 --current               # 显示当前配置的IP
    $0 --check                 # 检查配置文件状态

示例:
    $0 *************          # 更换为指定IP
    $0 auto                    # 自动检测并使用本机IP
    
注意:
    - 请在 my-ente 目录中运行此脚本
    - 脚本会自动备份配置文件
    - 更换IP后会自动重启相关服务
EOF
}

# 检查是否在正确的目录
check_directory() {
    if [[ ! -f "compose.yaml" ]] || [[ ! -f "museum.yaml" ]]; then
        log_error "未找到 compose.yaml 或 museum.yaml 文件"
        log_error "请确保在 my-ente 目录中运行此脚本"
        exit 1
    fi
    log_info "检测到 Ente 配置文件"
}

# 自动检测本机IP
detect_local_ip() {
    local ip=""
    
    # 方法1: 使用 ip route 命令
    if command -v ip >/dev/null 2>&1; then
        ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    fi
    
    # 方法2: 使用 hostname 命令
    if [[ -z "$ip" ]] && command -v hostname >/dev/null 2>&1; then
        ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi
    
    # 方法3: 使用 ifconfig 命令
    if [[ -z "$ip" ]] && command -v ifconfig >/dev/null 2>&1; then
        ip=$(ifconfig 2>/dev/null | grep -E 'inet.*192\.168\.|inet.*10\.|inet.*172\.' | grep -v '127.0.0.1' | awk '{print $2}' | head -1)
    fi
    
    # 方法4: 解析 /proc/net/route
    if [[ -z "$ip" ]] && [[ -f /proc/net/route ]]; then
        local interface=$(awk '$2 == 00000000 { print $1 }' /proc/net/route | head -1)
        if [[ -n "$interface" ]]; then
            ip=$(ip addr show "$interface" 2>/dev/null | grep -oP 'inet \K[\d.]+' | head -1)
        fi
    fi
    
    echo "$ip"
}

# 验证IP地址格式
validate_ip() {
    local ip=$1
    local regex='^([0-9]{1,3}\.){3}[0-9]{1,3}$'
    
    if [[ ! $ip =~ $regex ]]; then
        return 1
    fi
    
    # 检查每个数字是否在0-255范围内
    IFS='.' read -ra ADDR <<< "$ip"
    for i in "${ADDR[@]}"; do
        if [[ $i -gt 255 ]] || [[ $i -lt 0 ]]; then
            return 1
        fi
    done
    
    return 0
}

# 获取当前配置的IP
get_current_ip() {
    local current_ip=""
    
    # 从 compose.yaml 中提取IP
    if [[ -f "compose.yaml" ]]; then
        current_ip=$(grep -oP 'ENTE_API_ORIGIN: http://\K[^:]+' compose.yaml 2>/dev/null | head -1)
    fi
    
    # 如果没找到，从 museum.yaml 中提取
    if [[ -z "$current_ip" ]] && [[ -f "museum.yaml" ]]; then
        current_ip=$(grep -oP 'endpoint: http://\K[^:]+' museum.yaml 2>/dev/null | head -1)
    fi
    
    echo "$current_ip"
}

# 创建备份
create_backup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_dir="backup_${timestamp}"
    
    log_info "创建配置文件备份..."
    mkdir -p "$backup_dir"
    
    if [[ -f "compose.yaml" ]]; then
        cp "compose.yaml" "$backup_dir/"
        log_info "已备份 compose.yaml"
    fi
    
    if [[ -f "museum.yaml" ]]; then
        cp "museum.yaml" "$backup_dir/"
        log_info "已备份 museum.yaml"
    fi
    
    log_success "备份已创建: $backup_dir"
    echo "$backup_dir"
}

# 更新 compose.yaml 文件
update_compose_yaml() {
    local new_ip=$1
    local old_ip=$2
    
    log_info "更新 compose.yaml 文件..."
    
    # 备份原文件
    cp compose.yaml compose.yaml.tmp
    
    # 更新 ENTE_API_ORIGIN
    sed -i "s|ENTE_API_ORIGIN: http://[^:]*:8080|ENTE_API_ORIGIN: http://${new_ip}:8080|g" compose.yaml
    
    # 更新 ENTE_ALBUMS_ORIGIN  
    sed -i "s|ENTE_ALBUMS_ORIGIN: http[s]*://[^:]*:3002|ENTE_ALBUMS_ORIGIN: http://${new_ip}:3002|g" compose.yaml
    
    # 更新 NEXT_PUBLIC_ENTE_ENDPOINT (如果存在)
    sed -i "s|NEXT_PUBLIC_ENTE_ENDPOINT: http://[^:]*:8080|NEXT_PUBLIC_ENTE_ENDPOINT: http://${new_ip}:8080|g" compose.yaml
    
    # 检查是否有变化
    if ! diff -q compose.yaml compose.yaml.tmp >/dev/null 2>&1; then
        log_success "compose.yaml 已更新"
        rm compose.yaml.tmp
    else
        log_warning "compose.yaml 无需更新或未找到相关配置"
        mv compose.yaml.tmp compose.yaml
    fi
}

# 更新 museum.yaml 文件  
update_museum_yaml() {
    local new_ip=$1
    local old_ip=$2
    
    log_info "更新 museum.yaml 文件..."
    
    # 备份原文件
    cp museum.yaml museum.yaml.tmp
    
    # 更新 S3 endpoint (MinIO)
    sed -i "s|endpoint: http://[^:]*:3200|endpoint: http://${new_ip}:3200|g" museum.yaml
    
    # 更新其他可能的 localhost 引用
    sed -i "s|localhost:3200|${new_ip}:3200|g" museum.yaml
    sed -i "s|minio:3200|${new_ip}:3200|g" museum.yaml
    
    # 更新 HTTP 配置中的绑定地址 (如果存在)
    sed -i "s|host: localhost|host: 0.0.0.0|g" museum.yaml
    sed -i "s|host: 127.0.0.1|host: 0.0.0.0|g" museum.yaml
    
    # 检查是否有变化
    if ! diff -q museum.yaml museum.yaml.tmp >/dev/null 2>&1; then
        log_success "museum.yaml 已更新"
        rm museum.yaml.tmp
    else
        log_warning "museum.yaml 无需更新或未找到相关配置"
        mv museum.yaml.tmp museum.yaml
    fi
}
