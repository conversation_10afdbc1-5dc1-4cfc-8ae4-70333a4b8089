#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库状态
"""

import sqlite3
from pathlib import Path

def check_database():
    """检查数据库状态"""
    db_file = Path('aes/image_encryption.db')
    
    print(f"数据库文件存在: {db_file.exists()}")
    
    if not db_file.exists():
        print("数据库文件不存在！")
        return
    
    print(f"数据库文件大小: {db_file.stat().st_size} 字节")
    
    try:
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"数据库中的表: {[t[0] for t in tables]}")
        
        # 检查每个表的记录数
        for table in ['encrypted_images', 'encrypted_videos', 'encrypted_files']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"{table}: {count} 条记录")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                    records = cursor.fetchall()
                    print(f"  最新记录: {records}")
                    
            except Exception as e:
                print(f"{table}: 表不存在或错误 - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库检查失败: {e}")

if __name__ == "__main__":
    check_database()
