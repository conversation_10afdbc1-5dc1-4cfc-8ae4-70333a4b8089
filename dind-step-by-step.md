# Docker-in-Docker 从零开始配置指南

## 第一步：拉取官方 Docker-in-Docker 镜像

```bash
# 拉取官方 docker:dind 镜像
docker pull docker:dind

# 验证镜像已下载
docker images | grep docker
```

## 第二步：启动 Docker-in-Docker 容器

```bash
# 启动 DinD 容器（后台运行）
docker run -d \
  --name my-dind \
  --privileged \
  -p 2376:2376 \
  -v dind-storage:/var/lib/docker \
  docker:dind

# 检查容器状态
docker ps
```

**参数说明：**
- `-d`: 后台运行
- `--name my-dind`: 给容器命名
- `--privileged`: 特权模式（必需）
- `-p 2376:2376`: 映射Docker API端口
- `-v dind-storage:/var/lib/docker`: 创建数据卷存储Docker数据

## 第三步：进入容器并验证Docker

```bash
# 进入容器
docker exec -it my-dind sh

# 在容器内验证Docker
docker --version
docker info
```

## 第四步：在容器内安装Ubuntu 22.04环境

```bash
# 在DinD容器内拉取Ubuntu镜像
docker pull ubuntu:22.04

# 运行Ubuntu容器
docker run -it --name ubuntu-env ubuntu:22.04 /bin/bash
```

## 第五步：配置Ubuntu环境

在Ubuntu容器内执行：

```bash
# 更新包管理器
apt update && apt upgrade -y

# 安装基本工具
apt install -y \
    curl \
    wget \
    vim \
    git \
    build-essential \
    python3 \
    python3-pip \
    nodejs \
    npm

# 验证安装
python3 --version
node --version
```

## 第六步：测试Docker功能

回到DinD容器（按Ctrl+D退出Ubuntu容器，然后）：

```bash
# 测试构建镜像
cat > Dockerfile << EOF
FROM ubuntu:22.04
RUN apt update && apt install -y curl
CMD ["echo", "Hello from Docker-in-Docker!"]
EOF

# 构建镜像
docker build -t test-image .

# 运行测试镜像
docker run test-image

# 查看所有容器
docker ps -a

# 查看镜像
docker images
```

## 第七步：设置持久化工作环境

```bash
# 退出DinD容器
exit

# 创建工作目录挂载
docker run -d \
  --name my-dind-workspace \
  --privileged \
  -p 2376:2376 \
  -v dind-storage:/var/lib/docker \
  -v $(pwd)/workspace:/workspace \
  docker:dind

# 进入新容器
docker exec -it my-dind-workspace sh

# 进入工作目录
cd /workspace
```

## 第八步：创建便捷脚本

在主机上创建管理脚本：

```bash
# 创建启动脚本
cat > start-dind.sh << 'EOF'
#!/bin/bash
echo "启动 Docker-in-Docker 容器..."
docker run -d \
  --name my-dind \
  --privileged \
  -p 2376:2376 \
  -v dind-storage:/var/lib/docker \
  -v $(pwd)/workspace:/workspace \
  docker:dind

echo "等待Docker服务启动..."
sleep 5

echo "进入容器..."
docker exec -it my-dind sh
EOF

# 创建停止脚本
cat > stop-dind.sh << 'EOF'
#!/bin/bash
echo "停止并删除 Docker-in-Docker 容器..."
docker stop my-dind
docker rm my-dind
echo "完成！"
EOF

# 给脚本执行权限
chmod +x start-dind.sh stop-dind.sh
```

## 第九步：验证完整功能

进入DinD容器后测试：

```bash
# 测试网络功能
docker run --rm alpine ping -c 3 google.com

# 测试卷挂载
docker run --rm -v /workspace:/data alpine ls -la /data

# 测试端口映射
docker run -d -p 8080:80 --name web nginx
curl localhost:8080

# 测试Docker Compose（如果需要）
# 首先安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
docker-compose --version
```

## 第十步：常用操作命令

```bash
# 查看DinD容器状态
docker ps | grep dind

# 查看DinD容器日志
docker logs my-dind

# 重启DinD容器
docker restart my-dind

# 清理未使用的镜像和容器
docker exec my-dind docker system prune -f

# 备份DinD数据
docker run --rm -v dind-storage:/data -v $(pwd):/backup alpine tar czf /backup/dind-backup.tar.gz -C /data .

# 恢复DinD数据
docker run --rm -v dind-storage:/data -v $(pwd):/backup alpine tar xzf /backup/dind-backup.tar.gz -C /data
```

## 故障排除

### 问题1：容器无法启动
```bash
# 检查Docker服务状态
systemctl status docker

# 检查端口占用
netstat -tlnp | grep 2376

# 清理冲突容器
docker rm -f $(docker ps -aq --filter name=dind)
```

### 问题2：权限问题
```bash
# 确保使用特权模式
docker inspect my-dind | grep Privileged

# 重新创建容器
docker stop my-dind && docker rm my-dind
# 然后重新运行启动命令
```

### 问题3：存储空间不足
```bash
# 清理Docker数据
docker exec my-dind docker system prune -a -f

# 查看磁盘使用
docker exec my-dind df -h
```

现在你有了一个完全功能的Docker-in-Docker环境，可以在其中运行任何Docker命令，就像在真实主机上一样！
