#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复
"""

import sys
import os
import sqlite3
from pathlib import Path

def test_video_processor():
    """测试视频处理器的数据库写入"""
    print("1. 测试视频处理器...")
    
    try:
        # 切换到m3u8目录
        os.chdir(str(Path(__file__).parent / 'm3u8'))
        sys.path.append(str(Path(__file__).parent / 'm3u8'))
        
        from video_processor import VideoProcessor
        
        # 创建处理器实例
        processor = VideoProcessor()
        
        # 测试直接保存方法
        processor._save_video_to_database_direct(
            sha1_hash='test_fix_video_123',
            original_filename='test_fix.mp4',
            m3u8_path='video/test_fix/index.m3u8',
            password='testfix123456',
            iv='testfix654321',
            file_size_mb=18.5,
            tag='test_fix',
            txt='test_fix_video'
        )
        
        print("   ✓ 视频处理器数据库写入测试成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 视频处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 切换回原目录
        os.chdir(str(Path(__file__).parent))

def test_file_encryptor():
    """测试文件加密器的数据库写入"""
    print("2. 测试文件加密器...")
    
    try:
        # 切换到m3u8目录
        os.chdir(str(Path(__file__).parent / 'm3u8'))
        sys.path.append(str(Path(__file__).parent / 'm3u8'))
        
        from file_encryptor import FileEncryptor
        
        # 创建加密器实例
        encryptor = FileEncryptor()
        
        # 测试直接保存方法
        encryptor._save_file_to_database_direct(
            sha1_hash='test_fix_file_456',
            original_filename='test_fix.txt',
            file_path='other/2025-07-30/test_fix.txt',
            password='testfixfile123',
            iv='testfixfile456',
            file_size_mb=3.8,
            tag='test_fix',
            txt='test_fix_file'
        )
        
        print("   ✓ 文件加密器数据库写入测试成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 文件加密器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 切换回原目录
        os.chdir(str(Path(__file__).parent))

def verify_database():
    """验证数据库中的记录"""
    print("3. 验证数据库记录...")
    
    try:
        db_file = Path('aes/image_encryption.db')
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 检查视频记录
        cursor.execute("SELECT COUNT(*) FROM encrypted_videos")
        video_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT original_filename, m3u8_path FROM encrypted_videos WHERE sha1_hash = ?", 
                      ('test_fix_video_123',))
        video_record = cursor.fetchone()
        
        # 检查文件记录
        cursor.execute("SELECT COUNT(*) FROM encrypted_files")
        file_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT original_filename, file_path FROM encrypted_files WHERE sha1_hash = ?", 
                      ('test_fix_file_456',))
        file_record = cursor.fetchone()
        
        print(f"   数据库中总记录数 - 视频: {video_count}, 文件: {file_count}")
        
        if video_record:
            print(f"   ✓ 找到测试视频记录: {video_record[0]} -> {video_record[1]}")
        else:
            print("   ❌ 未找到测试视频记录")
        
        if file_record:
            print(f"   ✓ 找到测试文件记录: {file_record[0]} -> {file_record[1]}")
        else:
            print("   ❌ 未找到测试文件记录")
        
        conn.close()
        
        return video_record is not None and file_record is not None
        
    except Exception as e:
        print(f"   ❌ 数据库验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 测试最终修复 ===")
    
    # 测试视频处理器
    video_test = test_video_processor()
    
    # 测试文件加密器
    file_test = test_file_encryptor()
    
    # 验证数据库
    db_verify = verify_database()
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"  视频处理器: {'✅ 通过' if video_test else '❌ 失败'}")
    print(f"  文件加密器: {'✅ 通过' if file_test else '❌ 失败'}")
    print(f"  数据库验证: {'✅ 通过' if db_verify else '❌ 失败'}")
    
    if video_test and file_test and db_verify:
        print("\n🎉 所有测试通过！数据库写入问题已修复！")
        print("现在视频和文件处理器都能正常写入数据库了。")
    else:
        print("\n⚠ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
