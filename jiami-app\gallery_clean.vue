<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="initialLoading" class="loading">
			<view class="spinner"></view>
			<text>正在扫描本机相册...</text>
			<text class="loading-tip">首次加载可能需要较长时间</text>
			<!-- 调试按钮 -->
			<view class="debug-actions">
				<button @click="skipToMockData" class="debug-btn">跳过使用模拟数据</button>
				<button @click="loadQuickMode" class="debug-btn quick-btn">快速加载模式</button>
			</view>
		</view>

		<!-- 简化的高性能相册列表 -->
		<scroll-view v-else class="gallery-scroll" scroll-y="true" @scroll="handleScroll" @scrolltolower="loadMoreImages" :style="{ height: containerHeight + 'px' }">
			<!-- 按日期分组显示 -->
			<view v-for="(group, date) in groupedMedia" :key="date" class="date-group">
				<!-- 图片网格 -->
				<view class="media-grid">
					<view
						v-for="item in group"
						:key="item.id"
						class="media-item"
						@click="handleItemClick(item)"
					>
						<image
							:src="getOptimizedImageUrl(item)"
							class="media-image"
							mode="aspectFill"
							:lazy-load="true"
							:webp="true"
							:fade-show="false"
							@load="handleImageLoad(item)"
							@error="handleImageError(item)"
						/>
						
						<!-- 视频标识 -->
						<view v-if="item.type === 'video'" class="video-overlay">
							<view class="video-duration">{{ item.duration }}</view>
							<view class="video-play-icon">▶️</view>
						</view>
						
						<!-- 加载状态 -->
						<view v-if="item.loading" class="loading-overlay">
							<view class="mini-spinner"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多状态 -->
			<view v-if="hasMoreData && allMediaItems.length > 0" class="load-more">
				<view v-if="loadingMore" class="loading-more">
					<view class="mini-spinner"></view>
					<text class="loading-text">加载更多图片...</text>
				</view>
				<text v-else class="load-more-text">下拉加载更多</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMoreData && allMediaItems.length > 0" class="no-more">
				<text class="no-more-text">已加载全部图片</text>
			</view>

			<!-- 空状态 -->
			<view v-if="allMediaItems.length === 0" class="empty-state">
				<text class="empty-icon">🖼️</text>
				<text class="empty-text">暂无图片文件</text>
			</view>
		</scroll-view>
	</view>
</template>
