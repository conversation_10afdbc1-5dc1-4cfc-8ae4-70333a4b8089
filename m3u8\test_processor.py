#!/usr/bin/env python3
"""
测试脚本 - 用于测试视频处理程序的各个功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import subprocess

def test_dependencies():
    """测试依赖项"""
    print("测试依赖项...")

    # 测试FFmpeg
    try:
        # 在Windows上，尝试不同的方式调用ffmpeg
        import platform
        if platform.system() == "Windows":
            # 尝试直接调用
            result = subprocess.run(['ffmpeg', '-version'],
                                  capture_output=True, text=True, timeout=10, shell=True)
        else:
            result = subprocess.run(['ffmpeg', '-version'],
                                  capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            print("✓ FFmpeg 可用")
            # 显示版本信息的第一行
            version_line = result.stdout.split('\n')[0] if result.stdout else "版本信息不可用"
            print(f"  {version_line}")
        else:
            print("✗ FFmpeg 不可用")
            print(f"  错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ FFmpeg 测试失败: {e}")
        # 尝试使用shell=True
        try:
            result = subprocess.run('ffmpeg -version',
                                  capture_output=True, text=True, timeout=10, shell=True)
            if result.returncode == 0:
                print("✓ FFmpeg 可用 (通过shell调用)")
                return True
            else:
                print(f"✗ FFmpeg shell调用也失败: {result.stderr}")
                return False
        except Exception as e2:
            print(f"✗ FFmpeg shell调用异常: {e2}")
            return False
    
    # 测试cryptography
    try:
        from cryptography.hazmat.primitives.ciphers.aead import AESGCM
        print("✓ cryptography 库可用")
    except ImportError as e:
        print(f"✗ cryptography 库不可用: {e}")
        return False
    
    return True

def create_test_video():
    """创建测试视频文件"""
    print("创建测试视频...")
    
    temp_dir = Path("temp")
    temp_dir.mkdir(exist_ok=True)
    
    test_video = temp_dir / "test_video.mp4"
    
    try:
        # 使用FFmpeg创建一个简单的测试视频
        cmd = [
            'ffmpeg', '-f', 'lavfi', '-i', 'testsrc=duration=5:size=320x240:rate=1',
            '-c:v', 'libx264', '-t', '5', '-y', str(test_video)
        ]

        # 在Windows上使用shell=True
        import platform
        use_shell = platform.system() == "Windows"
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, shell=use_shell)
        
        if result.returncode == 0 and test_video.exists():
            print(f"✓ 测试视频创建成功: {test_video}")
            print(f"  文件大小: {test_video.stat().st_size / 1024:.2f} KB")
            return True
        else:
            print(f"✗ 测试视频创建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 创建测试视频异常: {e}")
        return False

def test_processor_import():
    """测试处理器导入"""
    print("测试处理器导入...")
    
    try:
        from video_processor import VideoProcessor
        processor = VideoProcessor()
        print("✓ 视频处理器导入成功")
        return True
    except Exception as e:
        print(f"✗ 视频处理器导入失败: {e}")
        return False

def test_key_generation():
    """测试密钥生成"""
    print("测试密钥生成...")
    
    try:
        from video_processor import VideoProcessor
        processor = VideoProcessor()
        
        key, iv = processor.generate_secure_key()
        
        if len(key) == 16 and len(iv) == 12:
            print("✓ 密钥生成成功")
            print(f"  密钥长度: {len(key)} 字节")
            print(f"  IV长度: {len(iv)} 字节")
            return True
        else:
            print(f"✗ 密钥长度错误: key={len(key)}, iv={len(iv)}")
            return False
            
    except Exception as e:
        print(f"✗ 密钥生成失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("视频处理程序测试")
    print("=" * 50)
    
    tests = [
        ("依赖项检查", test_dependencies),
        ("处理器导入", test_processor_import),
        ("密钥生成", test_key_generation),
        ("测试视频创建", create_test_video),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！程序可以正常运行。")
        print("\n使用方法:")
        print("1. 运行: python start.py")
        print("2. 将MP4文件放入temp目录")
        print("3. 程序会自动处理文件")
    else:
        print("✗ 部分测试失败，请检查环境配置。")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
