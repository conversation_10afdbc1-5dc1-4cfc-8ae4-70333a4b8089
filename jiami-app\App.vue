<template>
	<view id="app">
		<!-- 应用主体内容 -->
	</view>
</template>

<script>
export default {
	name: 'App',
	onLaunch: function() {
		console.log('App Launch')
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	}
}
</script>

<style lang="scss">
/* 全局样式 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
	background-color: #f8f8f8;
}

#app {
	height: 100vh;
	width: 100vw;
}

/* 通用工具类 */
.flex {
	display: flex;
}

.flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.flex-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.text-center {
	text-align: center;
}

.full-width {
	width: 100%;
}

.full-height {
	height: 100%;
}
</style>
