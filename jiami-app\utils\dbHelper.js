/**
 * 数据库文件管理辅助工具
 * 用于帮助用户将数据库文件复制到正确位置
 */

/**
 * 复制数据库文件到应用私有目录
 * @param {string} sourcePath - 源文件路径
 * @returns {Promise<boolean>} 复制是否成功
 */
export async function copyDatabaseToPrivateDir(sourcePath) {
  return new Promise((resolve, reject) => {
    if (typeof plus === 'undefined') {
      reject(new Error('此功能仅在App环境中可用'))
      return
    }

    try {
      // 获取源文件
      plus.io.resolveLocalFileSystemURL(sourcePath, (sourceEntry) => {
        console.log('找到源文件:', sourceEntry.fullPath)
        
        // 获取目标目录
        plus.io.resolveLocalFileSystemURL('_doc/', (docEntry) => {
          // 创建或获取doc文件夹
          docEntry.getDirectory('doc', { create: true }, (targetDir) => {
            console.log('目标目录准备完成:', targetDir.fullPath)
            
            // 复制文件
            sourceEntry.copyTo(targetDir, 'image_encryption.db', (copiedEntry) => {
              console.log('数据库文件复制成功:', copiedEntry.fullPath)
              resolve(true)
            }, (error) => {
              console.error('复制文件失败:', error)
              reject(error)
            })
          }, (error) => {
            console.error('创建doc目录失败:', error)
            reject(error)
          })
        }, (error) => {
          console.error('获取_doc目录失败:', error)
          reject(error)
        })
      }, (error) => {
        console.error('找不到源文件:', error)
        reject(error)
      })
    } catch (error) {
      console.error('复制数据库文件异常:', error)
      reject(error)
    }
  })
}

/**
 * 选择并复制数据库文件
 * @returns {Promise<boolean>} 操作是否成功
 */
export async function selectAndCopyDatabase() {
  return new Promise((resolve, reject) => {
    if (typeof plus === 'undefined') {
      reject(new Error('此功能仅在App环境中可用'))
      return
    }

    try {
      // 提示用户手动复制文件
      uni.showModal({
        title: '数据库文件导入',
        content: '请将image_encryption.db文件复制到以下路径：\n/Android/data/应用包名/files/doc/\n\n或者使用文件管理器将数据库文件放入应用的doc文件夹中。',
        confirmText: '我已复制',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户确认已复制文件，检查文件是否存在
            checkDatabaseExists().then((exists) => {
              if (exists) {
                uni.showToast({
                  title: '导入成功',
                  icon: 'success'
                })
                resolve(true)
              } else {
                uni.showModal({
                  title: '文件未找到',
                  content: '未在指定位置找到数据库文件，请确认文件已正确复制',
                  showCancel: false
                })
                resolve(false)
              }
            }).catch((error) => {
              console.error('检查数据库文件失败:', error)
              resolve(false)
            })
          } else {
            resolve(false)
          }
        }
      })
    } catch (error) {
      console.error('选择数据库文件异常:', error)
      reject(error)
    }
  })
}

/**
 * 检查数据库文件是否存在于正确位置
 * @returns {Promise<boolean>} 文件是否存在
 */
export async function checkDatabaseExists() {
  return new Promise((resolve) => {
    if (typeof plus === 'undefined') {
      resolve(false)
      return
    }

    const dbPath = plus.io.convertLocalFileSystemURL('_doc/') + 'doc/image_encryption.db'
    plus.io.resolveLocalFileSystemURL(dbPath, 
      () => resolve(true),
      () => resolve(false)
    )
  })
}

/**
 * 获取数据库文件信息
 * @returns {Promise<Object>} 文件信息
 */
export async function getDatabaseInfo() {
  return new Promise((resolve, reject) => {
    if (typeof plus === 'undefined') {
      reject(new Error('此功能仅在App环境中可用'))
      return
    }

    const dbPath = plus.io.convertLocalFileSystemURL('_doc/') + 'doc/image_encryption.db'
    plus.io.resolveLocalFileSystemURL(dbPath, (entry) => {
      entry.getMetadata((metadata) => {
        resolve({
          name: entry.name,
          fullPath: entry.fullPath,
          size: metadata.size,
          modificationTime: metadata.modificationTime
        })
      }, (error) => {
        reject(error)
      })
    }, (error) => {
      reject(error)
    })
  })
}
