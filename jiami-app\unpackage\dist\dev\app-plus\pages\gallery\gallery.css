/**
 * 这里是uni-app内置的常用样式变量
 * 
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 * 
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container[data-v-ff88f784] {
  flex: 1;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* ==================== 加载状态样式 ==================== */
.loading[data-v-ff88f784] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}
.spinner[data-v-ff88f784] {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin-ff88f784 1s linear infinite;
  margin-bottom: 20px;
}
@keyframes spin-ff88f784 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-tip[data-v-ff88f784] {
  color: #6b7280;
  font-size: 14px;
  margin-top: 10px;
}
.debug-actions[data-v-ff88f784] {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}
.debug-btn[data-v-ff88f784] {
  background: #f59e0b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
}
.quick-btn[data-v-ff88f784] {
  background: #10b981;
}

/* ==================== 🚀 媒体类型切换按钮样式 ==================== */
.media-tabs[data-v-ff88f784] {
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 16px;
  gap: 8px;
}
.media-tab[data-v-ff88f784] {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  cursor: pointer;
  -webkit-user-select: none;
          user-select: none;
  transform: translateZ(0);
  contain: layout style;
}
.media-tab[data-v-ff88f784]:active {
  transform: scale(0.98) translateZ(0);
}
.media-tab.active[data-v-ff88f784] {
  color: #ffffff;
  background: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}
.media-tab[data-v-ff88f784]:not(.active):hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  color: #374151;
}

/* ==================== 🚀 高性能相册滚动样式 ==================== */
.gallery-scroll[data-v-ff88f784] {
  width: 100%;
  background: #f8f9fa;
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  will-change: scroll-position;
  /* 优化滚动性能 */
  overflow-anchor: none;
  scroll-behavior: auto;
  contain: layout style paint;
}

/* 日期分组 - 优化渲染性能 */
.date-group[data-v-ff88f784] {
  position: relative;
  width: 100%;
  contain: layout style paint;
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

/* 媒体网格 - 优化布局性能，确保整齐排列 */
.media-grid[data-v-ff88f784] {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
  padding: 4px;
  background: #ffffff;
  contain: layout style;
  transform: translateZ(0);
}

/* ==================== 🚀 视频列表样式 ==================== */
.video-list[data-v-ff88f784] {
  background: #ffffff;
  padding: 8px 0;
  contain: layout style;
  transform: translateZ(0);
}
.video-item[data-v-ff88f784] {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #ffffff;
  transition: background-color 0.2s ease;
  cursor: pointer;
  -webkit-user-select: none;
          user-select: none;
  contain: layout style;
  transform: translateZ(0);
}
.video-item[data-v-ff88f784]:active {
  background: #f8f9fa;
  transform: scale(0.99) translateZ(0);
}
.video-item[data-v-ff88f784]:last-child {
  border-bottom: none;
}
.video-icon[data-v-ff88f784] {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  font-size: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}
.video-info[data-v-ff88f784] {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.video-name[data-v-ff88f784] {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.video-details[data-v-ff88f784] {
  display: flex;
  gap: 12px;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.3;
}
.video-size[data-v-ff88f784] {
  font-weight: 500;
  color: #3b82f6;
}
.video-date[data-v-ff88f784] {
  color: #9ca3af;
}
.video-arrow[data-v-ff88f784] {
  font-size: 20px;
  color: #d1d5db;
  margin-left: 8px;
  flex-shrink: 0;
  transition: color 0.2s ease;
}
.video-item:active .video-arrow[data-v-ff88f784] {
  color: #9ca3af;
}

/* 媒体项 - 高性能优化，使用grid布局确保整齐 */
.media-item[data-v-ff88f784] {
  width: 100%;
  aspect-ratio: 1;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #f3f4f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  will-change: transform;
  contain: layout style paint;
  /* 进一步优化性能 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  isolation: isolate;
}
.media-item[data-v-ff88f784]:active {
  transform: scale(0.95) translateZ(0);
}
.media-image[data-v-ff88f784] {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  background: #e5e7eb;
  transition: opacity 0.2s ease;
  /* 优化图片渲染性能 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

/* 视频覆盖层 */
.video-overlay[data-v-ff88f784] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
.video-duration[data-v-ff88f784] {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}
.video-play-icon[data-v-ff88f784] {
  font-size: 24px;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 加载覆盖层 */
.loading-overlay[data-v-ff88f784] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
.mini-spinner[data-v-ff88f784] {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin-ff88f784 1s linear infinite;
}

/* ==================== 🚀 高性能下拉加载样式 ==================== */
.load-more[data-v-ff88f784] {
  padding: 20px;
  text-align: center;
  background: #ffffff;
  border-top: 1px solid #f0f0f0;
}
.loading-more[data-v-ff88f784] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.loading-text[data-v-ff88f784] {
  color: #666;
  font-size: 14px;
}
.load-more-text[data-v-ff88f784] {
  color: #999;
  font-size: 14px;
}
.no-more[data-v-ff88f784] {
  padding: 20px;
  text-align: center;
  background: #ffffff;
  border-top: 1px solid #f0f0f0;
}
.no-more-text[data-v-ff88f784] {
  color: #999;
  font-size: 14px;
}

/* 空状态 */
.empty-state[data-v-ff88f784] {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}
.empty-icon[data-v-ff88f784] {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}
.empty-text[data-v-ff88f784] {
  font-size: 16px;
  color: #9ca3af;
}