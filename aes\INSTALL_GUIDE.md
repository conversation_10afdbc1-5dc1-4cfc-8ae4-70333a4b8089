# 安装和使用指南

## 系统要求

- **Python版本**: Python 3.8 或更高版本
- **操作系统**: Windows 10+ 或 Linux (Ubuntu 18.04+, CentOS 7+, 等)
- **内存**: 建议至少 512MB 可用内存
- **磁盘空间**: 至少 100MB 可用空间

## 快速安装

### Windows用户

1. **下载并解压项目文件**到任意目录
2. **双击运行** `start.bat` 文件
3. 脚本会自动检查Python环境并安装依赖包
4. 按照菜单提示操作

### Linux用户

1. **下载并解压项目文件**到任意目录
2. **打开终端**，进入项目目录
3. **运行启动脚本**:
   ```bash
   chmod +x start.sh
   ./start.sh
   ```
4. 按照菜单提示操作

## 手动安装

如果自动安装脚本无法正常工作，可以按照以下步骤手动安装：

### 1. 检查Python环境

```bash
# Windows
python --version

# Linux
python3 --version
```

确保版本为 3.8 或更高。

### 2. 安装依赖包

```bash
# Windows
pip install -r requirements.txt

# Linux
pip3 install -r requirements.txt
```

### 3. 验证安装

```bash
# Windows
python test_system.py

# Linux
python3 test_system.py
```

## 使用方法

### 方法一：使用交互式菜单（推荐）

```bash
# Windows
python run.py

# Linux
python3 run.py
```

选择相应的菜单选项：
- `1` - 安装依赖包
- `2` - 创建测试图片
- `3` - 启动图片监控和处理
- `4` - 列出加密文件
- `5` - 解密指定文件
- `6` - 解密所有文件
- `7` - 查看帮助

### 方法二：直接运行命令

#### 启动监控系统
```bash
# Windows
python main.py

# Linux
python3 main.py
```

#### 解密文件
```bash
# 列出所有加密文件
python decrypt_tool.py --list

# 解密指定文件
python decrypt_tool.py --file temp/image.jpg

# 解密所有文件到指定目录
python decrypt_tool.py --all decrypted
```

## 使用流程

### 1. 准备图片文件
- 将需要处理的图片放入 `temp` 目录
- 支持格式：JPG, PNG, WebP, AVIF, HEIC

### 2. 启动监控系统
- 运行主程序开始监控
- 系统每2秒检查一次新文件

### 3. 自动处理过程
- 检测到图片文件后自动开始处理
- 生成缩略图（文件名_tagimg.jpg）
- 转换为AVIF格式（80%质量）
- 使用AES256-GCM加密
- 覆盖原文件
- 保存加密信息到数据库

### 4. 查看和解密
- 使用解密工具查看加密文件列表
- 根据需要解密特定文件或全部文件

## 注意事项

### ⚠️ 重要警告
- **数据备份**: 程序会覆盖原始图片文件，请务必提前备份重要文件
- **数据库安全**: `image_encryption.db` 包含解密密钥，请妥善保管
- **单实例运行**: 避免同时运行多个程序实例，可能导致数据库冲突

### 💡 使用建议
- **测试环境**: 建议先使用测试图片验证功能
- **监控日志**: 注意观察控制台输出的处理日志
- **定期备份**: 定期备份数据库文件和重要图片
- **性能考虑**: 大量文件处理时注意系统资源使用

## 故障排除

### 依赖包安装问题

**问题**: pip安装失败
```bash
# 解决方案1：升级pip
python -m pip install --upgrade pip

# 解决方案2：使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 解决方案3：手动安装
pip install Pillow>=10.0.0
pip install pillow-avif-plugin>=1.4.0
pip install cryptography>=41.0.0
```

### AVIF格式支持问题

**问题**: 不支持AVIF格式
```bash
# 确保安装了AVIF插件
pip install pillow-avif-plugin

# 如果仍有问题，尝试重新安装Pillow
pip uninstall Pillow
pip install Pillow
pip install pillow-avif-plugin
```

### 权限问题

**Linux权限错误**:
```bash
# 给脚本添加执行权限
chmod +x start.sh

# 确保对目录有写权限
chmod 755 .
```

**Windows权限错误**:
- 以管理员身份运行命令提示符
- 或者将项目放在用户目录下

### 数据库问题

**数据库锁定**:
- 确保没有多个程序实例运行
- 重启程序通常可以解决

**数据库损坏**:
- 删除 `image_encryption.db` 文件
- 重新运行程序（会丢失之前的加密信息）

## 技术支持

### 日志信息
程序运行时会在控制台显示详细的处理信息：
- 文件检测日志
- 处理进度信息
- 错误和警告信息

### 调试模式
如需更详细的调试信息，可以修改代码中的日志级别或添加调试输出。

### 性能监控
- 监控CPU和内存使用情况
- 大文件处理时注意磁盘空间
- 网络存储可能影响处理速度

## 更新和维护

### 依赖包更新
```bash
pip install --upgrade -r requirements.txt
```

### 功能扩展
项目采用模块化设计，可以方便地添加新功能：
- 支持更多图片格式
- 添加不同的加密算法
- 实现网络监控功能
- 添加图形用户界面

### 配置修改
主要配置参数位于 `main.py` 文件顶部，可以根据需要调整：
- 监控间隔时间
- 缩略图尺寸
- 图片质量设置
- 支持的文件格式
