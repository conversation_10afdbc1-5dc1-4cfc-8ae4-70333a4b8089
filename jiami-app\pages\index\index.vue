<template>
	<view class="container">
		<!-- WebView 组件加载本地HTML页面 -->
		<web-view 
			class="webview"
			:src="webviewSrc"
			@message="handleMessage"
			@load="handleLoad"
			@error="handleError"
		></web-view>
	</view>
</template>

<script>
export default {
	name: 'Index',
	data() {
		return {
			webviewSrc: '/static/html/gallery.html'
		}
	},
	onLoad() {
		console.log('Index页面加载')
		this.initGallery()
	},
	onShow() {
		console.log('Index页面显示')
	},
	methods: {
		// 初始化相册数据
		initGallery() {
			// 这里可以获取本地图片视频数据
			// 然后通过postMessage发送给webview
			const mockData = {
				images: [
					{
						id: 1,
						url: '/static/images/sample1.jpg',
						name: '示例图片1.jpg',
						size: '2.5MB',
						date: '2024-01-15'
					},
					{
						id: 2,
						url: '/static/images/sample2.jpg',
						name: '示例图片2.jpg',
						size: '1.8MB',
						date: '2024-01-14'
					}
				],
				videos: [
					{
						id: 1,
						url: '/static/videos/sample1.mp4',
						thumbnail: '/static/images/video-thumb1.jpg',
						name: '示例视频1.mp4',
						size: '15.2MB',
						duration: '00:02:30',
						date: '2024-01-13'
					}
				]
			}
			
			// 延迟发送数据，确保webview已加载完成
			setTimeout(() => {
				this.sendDataToWebview(mockData)
			}, 1000)
		},
		
		// 向webview发送数据
		sendDataToWebview(data) {
			// #ifdef APP-PLUS
			// App端可以使用evalJS方法
			const currentWebview = this.$scope.$getAppWebview()
			if (currentWebview) {
				setTimeout(() => {
					const webview = currentWebview.children()[0]
					if (webview) {
						webview.evalJS(`
							if (window.updateGalleryData) {
								window.updateGalleryData(${JSON.stringify(data)});
							}
						`)
					}
				}, 500)
			}
			// #endif
		},
		
		// 处理webview发送的消息
		handleMessage(event) {
			console.log('收到webview消息:', event.detail.data)
			const data = event.detail.data[0] // uni-app中消息以数组形式接收
			
			if (data.type === 'ready') {
				// webview已准备就绪
				console.log('WebView已准备就绪')
			} else if (data.type === 'imageClick') {
				// 处理图片点击
				this.handleImageClick(data.payload)
			} else if (data.type === 'videoClick') {
				// 处理视频点击
				this.handleVideoClick(data.payload)
			}
		},
		
		// 处理图片点击
		handleImageClick(imageData) {
			console.log('图片被点击:', imageData)
			// 这里可以实现图片预览、编辑等功能
			uni.showToast({
				title: `点击了图片: ${imageData.name}`,
				icon: 'none'
			})
		},
		
		// 处理视频点击
		handleVideoClick(videoData) {
			console.log('视频被点击:', videoData)
			// 这里可以实现视频播放等功能
			uni.showToast({
				title: `点击了视频: ${videoData.name}`,
				icon: 'none'
			})
		},
		
		// webview加载完成
		handleLoad(event) {
			console.log('WebView加载完成')
		},
		
		// webview加载错误
		handleError(event) {
			console.error('WebView加载错误:', event)
			uni.showToast({
				title: 'WebView加载失败',
				icon: 'error'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100vh;
	background-color: #f8f8f8;
}

.webview {
	width: 100%;
	height: 100%;
}
</style>
