<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版HLS播放器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 10px;
            background-color: #f5f5f5;
            box-sizing: border-box;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            body {
                padding: 5px;
                font-size: 14px;
            }
            .container {
                padding: 10px;
                border-radius: 4px;
            }
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        #video {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 4px;
            background-color: #000;
        }
        .url-input, .encryption-input {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .url-input label, .encryption-input label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .url-input input[type="text"] {
            width: calc(100% - 120px);
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-right: 10px;
            box-sizing: border-box;
        }
        .url-input button {
            width: 110px;
            vertical-align: top;
        }

        .url-input-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .url-input-row input {
            flex: 1;
        }

        /* 移动端URL输入框适配 */
        @media (max-width: 768px) {
            .url-input {
                padding: 10px;
            }

            .url-input-row {
                flex-direction: column;
                gap: 8px;
                align-items: stretch;
            }

            .url-input-row input {
                width: 100%;
                font-size: 16px; /* 防止iOS缩放 */
                margin-bottom: 0;
            }

            .url-input-row button {
                width: 100%;
                margin-bottom: 0;
            }
        }
        .encryption-input {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .encryption-row {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            align-items: center;
        }
        .encryption-field {
            flex: 1;
        }
        .encryption-field label {
            margin-bottom: 5px;
            font-size: 14px;
        }
        .encryption-field input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            box-sizing: border-box;
        }

        /* 移动端加密输入框适配 */
        @media (max-width: 768px) {
            .encryption-input {
                padding: 10px;
            }
            .encryption-row {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }
            .encryption-field input {
                font-size: 16px; /* 防止iOS缩放 */
                padding: 12px;
            }
        }
        .encryption-toggle {
            margin-bottom: 10px;
        }
        .encryption-toggle input[type="checkbox"] {
            margin-right: 8px;
        }
        .encryption-note {
            font-size: 12px;
            color: #856404;
            margin-top: 10px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            transition: background-color 0.3s;
            min-height: 44px; /* 移动端触摸友好 */
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        /* 移动端按钮适配 */
        @media (max-width: 768px) {
            .controls {
                margin: 15px 0;
                gap: 8px;
            }
            button {
                padding: 12px 16px;
                font-size: 16px;
                min-height: 48px; /* 更大的触摸区域 */
                flex: 1;
                min-width: 120px;
            }
        }
        .status {
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            font-size: 14px;
        }
        .status.info {
            background-color: #e3f2fd;
            color: #1565c0;
            border-left: 4px solid #2196f3;
        }
        .status.success {
            background-color: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }
        .status.error {
            background-color: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }
        .debug {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            border: 1px solid #dee2e6;
        }

        /* 移动端整体优化 */
        @media (max-width: 768px) {
            h1 {
                font-size: 20px;
                margin-bottom: 20px;
            }

            .debug {
                font-size: 11px;
                max-height: 200px;
                padding: 10px;
            }

            .status {
                padding: 10px;
                font-size: 13px;
            }

            /* 确保所有输入框在移动端可见 */
            input, select, textarea {
                font-size: 16px !important; /* 防止iOS缩放 */
                -webkit-appearance: none;
                appearance: none;
                border-radius: 4px;
            }

            /* 视频播放器移动端优化 */
            #video {
                width: 100%;
                height: auto;
                max-height: 50vh;
            }
        }

        /* 小屏幕设备进一步优化 */
        @media (max-width: 480px) {
            body {
                padding: 2px;
            }

            .container {
                padding: 8px;
                margin: 0;
            }

            h1 {
                font-size: 18px;
                margin-bottom: 15px;
            }

            .url-input, .encryption-input {
                margin: 10px 0;
                padding: 8px;
            }

            button {
                font-size: 14px;
                padding: 10px 12px;
                min-width: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>修复版HLS播放器</h1>
        
        <div class="url-input">
            <label for="m3u8Url">M3U8地址:</label>
            <div class="url-input-row">
                <input type="text" id="m3u8Url" placeholder="输入M3U8文件地址，例如: m3u8/video/xxx/playlist.m3u8" />
                <button onclick="loadFromInput()">加载并播放</button>
                <button onclick="loadFromInput(false)" style="background-color: #28a745;">仅加载</button>
            </div>
            <div style="margin-top: 8px; font-size: 12px; color: #666;">
                💡 提示：输入地址后按回车键可快速加载并播放
            </div>
        </div>

        <div class="encryption-input">
            <div class="encryption-toggle">
                <label>
                    <input type="checkbox" id="useCustomEncryption" onchange="toggleEncryptionInputs()">
                    使用自定义加密参数（密钥和IV）
                </label>
            </div>
            <div id="encryptionFields" style="display: none;">
                <div class="encryption-row">
                    <div class="encryption-field">
                        <label for="customKey">密钥 (Key) - 32位十六进制:</label>
                        <input type="text" id="customKey" placeholder="输入32位十六进制密钥" maxlength="32" />
                    </div>
                    <div class="encryption-field">
                        <label for="customIV">初始化向量 (IV) - 32位十六进制:</label>
                        <input type="text" id="customIV" placeholder="输入32位十六进制IV" maxlength="32" />
                    </div>
                </div>
                <div class="encryption-note">
                    ⚠️ 注意：自定义加密参数将覆盖M3U8文件中的加密设置。请确保密钥和IV格式正确（32位十六进制字符）。
                </div>
            </div>
        </div>

        <div class="controls">
            <button onclick="loadDefaultVideo()">加载默认视频</button>
            <button id="playBtn" onclick="playVideo()" disabled>播放</button>
            <button id="pauseBtn" onclick="pauseVideo()" disabled>暂停</button>
            <button onclick="reloadVideo()">重新加载</button>
            <button onclick="clearDebug()">清除日志</button>
        </div>

        <div id="status" class="status info">输入M3U8地址并点击"加载并播放"，或点击"加载默认视频"</div>

        <div id="videoInfo" class="status" style="display: none;">
            <strong>当前视频:</strong> <span id="currentUrl"></span>
        </div>

        <video id="video" controls preload="none"></video>
        
        <div id="debug" class="debug"></div>
    </div>

    <script src="hls.js@latest"></script>
    <script>
        let hls = null;
        let currentM3u8Path = '';
        const video = document.getElementById('video');
        const playBtn = document.getElementById('playBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const status = document.getElementById('status');
        const debug = document.getElementById('debug');
        const m3u8Input = document.getElementById('m3u8Url');
        const useCustomEncryption = document.getElementById('useCustomEncryption');
        const customKey = document.getElementById('customKey');
        const customIV = document.getElementById('customIV');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            debug.textContent += logMessage + '\n';
            debug.scrollTop = debug.scrollHeight;
            
            if (type !== 'debug') {
                updateStatus(message, type);
            }
        }

        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function clearDebug() {
            debug.textContent = '';
        }

        function toggleEncryptionInputs() {
            const encryptionFields = document.getElementById('encryptionFields');
            if (useCustomEncryption.checked) {
                encryptionFields.style.display = 'block';
                log('启用自定义加密参数输入', 'info');
            } else {
                encryptionFields.style.display = 'none';
                log('禁用自定义加密参数，将使用M3U8文件中的设置', 'info');
            }
        }

        function validateEncryptionInputs() {
            if (!useCustomEncryption.checked) {
                return true; // 不使用自定义加密，直接通过
            }

            const key = customKey.value.trim();
            const iv = customIV.value.trim();

            if (!key || !iv) {
                log('请输入完整的密钥和IV', 'error');
                return false;
            }

            // 验证十六进制格式
            const hexPattern = /^[0-9a-fA-F]{32}$/;
            if (!hexPattern.test(key)) {
                log('密钥格式错误：必须是32位十六进制字符', 'error');
                return false;
            }

            if (!hexPattern.test(iv)) {
                log('IV格式错误：必须是32位十六进制字符', 'error');
                return false;
            }

            log('自定义加密参数验证通过', 'success');
            return true;
        }

        function loadFromInput(autoPlay = true) {
            const url = m3u8Input.value.trim();
            if (!url) {
                log('请输入M3U8地址', 'error');
                m3u8Input.focus();
                return;
            }

            // 验证加密参数
            if (!validateEncryptionInputs()) {
                return;
            }

            // 简单的URL验证
            if (!url.endsWith('.m3u8') && !url.includes('m3u8')) {
                log('警告：输入的地址可能不是有效的M3U8文件', 'error');
            }

            log(`从输入框加载: ${url}${autoPlay ? ' (自动播放)' : ''}`, 'info');
            if (useCustomEncryption.checked) {
                log(`使用自定义加密 - Key: ${customKey.value}, IV: ${customIV.value}`, 'debug');
            }
            loadVideo(url, autoPlay);
        }

        function loadDefaultVideo() {
            const defaultUrl = 'data/video/2025-07-30/17a603598b0bb7733e783e60ddc677320c65b81f/playlist.m3u8';
            m3u8Input.value = defaultUrl;
            log('加载默认视频', 'info');
            loadVideo(defaultUrl, false);
        }

        async function createCustomEncryptedM3U8(originalM3u8Path) {
            try {
                const response = await fetch(originalM3u8Path);
                const content = await response.text();

                log('正在应用自定义加密参数...', 'info');

                // 获取自定义参数
                const key = customKey.value.trim();
                const iv = customIV.value.trim();

                // 创建自定义密钥文件的Blob URL
                const keyBytes = new Uint8Array(16);
                for (let i = 0; i < 16; i++) {
                    keyBytes[i] = parseInt(key.substr(i * 2, 2), 16);
                }
                const keyBlob = new Blob([keyBytes], { type: 'application/octet-stream' });
                const keyBlobUrl = URL.createObjectURL(keyBlob);

                // 获取M3U8文件的基础路径
                const basePath = originalM3u8Path.substring(0, originalM3u8Path.lastIndexOf('/') + 1);
                log(`基础路径: ${basePath}`, 'debug');

                // 修改M3U8内容
                const lines = content.split('\n');
                const modifiedLines = [];
                let hasEncryption = false;
                let headerComplete = false;

                for (const line of lines) {
                    if (line.startsWith('#EXT-X-BYTERANGE:')) {
                        // 跳过BYTERANGE行
                        continue;
                    } else if (line.startsWith('#EXT-X-KEY:')) {
                        // 替换为自定义加密参数
                        const customKeyLine = `#EXT-X-KEY:METHOD=AES-128,URI="${keyBlobUrl}",IV=0x${iv}`;
                        modifiedLines.push(customKeyLine);
                        log(`替换现有密钥行: ${customKeyLine}`, 'debug');
                        hasEncryption = true;
                    } else if (line.startsWith('#EXTINF:') && !headerComplete) {
                        // 如果到了第一个片段信息行，且还没有加密信息，则添加自定义加密
                        if (!hasEncryption) {
                            const customKeyLine = `#EXT-X-KEY:METHOD=AES-128,URI="${keyBlobUrl}",IV=0x${iv}`;
                            modifiedLines.push(customKeyLine);
                            log(`添加自定义密钥行: ${customKeyLine}`, 'debug');
                            hasEncryption = true;
                        }
                        modifiedLines.push(line);
                        headerComplete = true;
                    } else if (line.endsWith('.ts')) {
                        // 将相对路径的片段转换为绝对路径
                        if (!line.startsWith('http')) {
                            const absolutePath = basePath + line;
                            modifiedLines.push(absolutePath);
                            log(`片段路径转换: ${line} -> ${absolutePath}`, 'debug');
                        } else {
                            modifiedLines.push(line);
                        }
                    } else {
                        modifiedLines.push(line);
                    }
                }

                // 如果整个文件都没有加密信息且没有片段信息，在文件末尾前添加
                if (!hasEncryption && !headerComplete) {
                    // 在#EXT-X-ENDLIST前插入加密信息
                    const endListIndex = modifiedLines.findIndex(line => line.startsWith('#EXT-X-ENDLIST'));
                    if (endListIndex > -1) {
                        const customKeyLine = `#EXT-X-KEY:METHOD=AES-128,URI="${keyBlobUrl}",IV=0x${iv}`;
                        modifiedLines.splice(endListIndex, 0, customKeyLine);
                        log(`在文件末尾添加自定义密钥行: ${customKeyLine}`, 'debug');
                    }
                }

                const modifiedContent = modifiedLines.join('\n');
                log('修改后的M3U8内容:', 'debug');
                log(modifiedContent, 'debug');

                // 创建修改后的M3U8 Blob URL
                const m3u8Blob = new Blob([modifiedContent], { type: 'application/vnd.apple.mpegurl' });
                const m3u8BlobUrl = URL.createObjectURL(m3u8Blob);

                log('自定义加密M3U8创建成功', 'success');
                return m3u8BlobUrl;

            } catch (error) {
                log('创建自定义加密M3U8失败: ' + error.message, 'error');
                throw error;
            }
        }

        async function checkM3U8Encryption(m3u8Path) {
            // 检查M3U8文件是否包含加密信息
            try {
                const response = await fetch(m3u8Path);
                const content = await response.text();
                return content.includes('#EXT-X-KEY:');
            } catch (error) {
                log('无法检查M3U8文件加密状态: ' + error.message, 'debug');
                return false;
            }
        }

        async function loadVideo(m3u8Path = null, autoPlay = false) {
            if (!Hls.isSupported()) {
                log('您的浏览器不支持 HLS.js', 'error');
                return;
            }

            // 如果没有提供路径，使用默认路径
            if (!m3u8Path) {
                m3u8Path = 'data/video/2025-07-30/17a603598b0bb7733e783e60ddc677320c65b81f/playlist.m3u8';
            }

            currentM3u8Path = m3u8Path;
            log(`正在加载视频: ${m3u8Path}`, 'info');

            // 预检查M3U8文件是否包含加密信息
            try {
                const hasEncryption = await checkM3U8Encryption(m3u8Path);
                if (hasEncryption && !useCustomEncryption.checked) {
                    log('检测到加密视频，但未启用自定义加密', 'warning');
                    log('提示：请勾选"使用自定义加密"并输入正确的密钥和IV', 'info');
                    log('如果您不知道密钥和IV，请联系视频提供者', 'info');
                }
            } catch (error) {
                log('预检查加密状态失败，继续加载: ' + error.message, 'debug');
            }

            // 更新视频信息显示
            const videoInfo = document.getElementById('videoInfo');
            const currentUrl = document.getElementById('currentUrl');
            currentUrl.textContent = m3u8Path;
            videoInfo.style.display = 'block';

            // 清理之前的实例
            if (hls) {
                hls.destroy();
                log('清理之前的HLS实例', 'debug');
            }

            try {
                // 如果使用自定义加密，先处理M3U8文件
                let finalM3u8Path = m3u8Path;
                if (useCustomEncryption.checked) {
                    log('正在应用自定义加密参数...', 'info');
                    finalM3u8Path = await createCustomEncryptedM3U8(m3u8Path);
                }

                hls = new Hls({
                    debug: false,
                    enableWorker: false,
                    lowLatencyMode: false,
                    // 优化缓冲区设置以避免bufferFullError
                    backBufferLength: 30,           // 减少后缓冲区长度
                    maxBufferLength: 20,            // 减少最大缓冲区长度
                    maxMaxBufferLength: 60,         // 减少最大最大缓冲区长度
                    maxBufferSize: 30 * 1000 * 1000, // 30MB缓冲区大小
                    enableSoftwareAES: true,
                    // 优化seek相关参数
                    maxBufferHole: 2,               // 增加缓冲区洞的容忍度
                    maxSeekHole: 5,                 // 增加seek洞的容忍度
                    seekHoleNudgeDuration: 0.1,     // seek洞推进持续时间
                    maxStarvationDelay: 4,
                    maxLoadingDelay: 4,
                    // 添加更多缓冲区控制参数
                    liveSyncDurationCount: 3,
                    liveMaxLatencyDurationCount: 10,
                    liveDurationInfinity: false,
                    // 片段重试配置
                    fragLoadingTimeOut: 20000,      // 片段加载超时
                    fragLoadingMaxRetry: 6,         // 片段加载最大重试次数
                    fragLoadingRetryDelay: 1000,    // 片段加载重试延迟
                    // 错误恢复配置
                    errorRecoveryDelay: 100         // 错误恢复延迟
                });

                setupHlsEvents(autoPlay);

                hls.loadSource(finalM3u8Path);
                hls.attachMedia(video);
                log('HLS源已加载并附加到媒体元素', 'debug');

            } catch (error) {
                log('加载失败: ' + error.message, 'error');
            }
        }

        function setupHlsEvents(autoPlay = false) {
            // 错误处理
            hls.on(Hls.Events.ERROR, function(event, data) {
                // 特殊处理bufferFullError - 这是常见的非致命错误
                if (data.details === 'bufferFullError') {
                    log('缓冲区已满，自动清理中...', 'debug');
                    // 自动清理缓冲区
                    try {
                        const buffered = video.buffered;
                        if (buffered.length > 0) {
                            const currentTime = video.currentTime;
                            // 清理当前播放位置之前的缓冲区
                            for (let i = 0; i < buffered.length; i++) {
                                if (buffered.end(i) < currentTime - 10) {
                                    // HLS.js会自动管理缓冲区，我们只记录日志
                                    log('清理旧缓冲区数据', 'debug');
                                }
                            }
                        }
                    } catch (e) {
                        log('缓冲区清理失败，但不影响播放', 'debug');
                    }
                    return; // 不显示bufferFullError给用户
                }

                // 处理片段解析错误
                if (data.details === 'fragParsingError') {
                    if (data.error && data.error.message.includes('decrypt')) {
                        log('解密错误：可能是密钥或IV不正确', 'error');
                        return;
                    } else {
                        // 其他fragParsingError通常是拖动进度条导致的，自动恢复
                        log('片段解析错误，自动重试中...', 'debug');
                        setTimeout(() => {
                            if (hls && !hls.media.paused) {
                                hls.startLoad();
                            }
                        }, 100);
                        return; // 不显示给用户
                    }
                }

                if (data.fatal) {
                    switch(data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                            log('网络错误：' + data.details, 'error');
                            setTimeout(() => {
                                log('尝试恢复网络错误...', 'info');
                                hls.startLoad();
                            }, 1000);
                            break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                            // 对于媒体错误，先尝试自动恢复
                            if (data.details === 'bufferFullError') {
                                log('缓冲区满，尝试自动恢复...', 'info');
                                hls.recoverMediaError();
                            } else {
                                log('媒体错误：' + data.details, 'error');
                                setTimeout(() => {
                                    log('尝试恢复媒体错误...', 'info');
                                    hls.recoverMediaError();
                                }, 1000);
                            }
                            break;
                        default:
                            log('致命错误：' + data.details, 'error');
                            break;
                    }
                } else {
                    // 特殊处理密钥加载错误
                    if (data.details === 'keyLoadError') {
                        if (!useCustomEncryption.checked) {
                            log('视频需要解密密钥，请启用自定义加密并输入正确的密钥和IV', 'error');
                            log('提示：如果您不知道密钥和IV，请联系视频提供者', 'info');
                        } else {
                            log('密钥加载失败，请检查自定义密钥和IV是否正确', 'error');
                        }
                        return; // 不继续处理其他逻辑
                    }

                    // 过滤掉一些常见的非致命错误，避免日志噪音
                    const ignoredErrors = [
                        'bufferFullError',
                        'bufferSeekOverHole',
                        'bufferNudgeOnStall',
                        'fragParsingError',     // 拖动进度条时常见
                        'bufferAppendError',    // 缓冲区追加错误
                        'bufferAppendingError', // 缓冲区追加中错误
                        'fragLoadError'         // 片段加载错误（非致命）
                    ];

                    if (!ignoredErrors.includes(data.details)) {
                        log('非致命错误：' + data.details, 'debug');
                    }
                }
            });

            // 清单解析完成
            hls.on(Hls.Events.MANIFEST_PARSED, function(event, data) {
                log('视频清单解析成功', 'success');
                playBtn.disabled = false;
                pauseBtn.disabled = false;

                // 如果需要自动播放
                if (autoPlay) {
                    log('自动播放视频...', 'info');
                    setTimeout(() => {
                        playVideo();
                    }, 500); // 稍微延迟以确保视频准备就绪
                }
            });

            // 密钥加载事件
            hls.on(Hls.Events.KEY_LOADING, function(event, data) {
                log('正在加载解密密钥: ' + data.frag.decryptdata.uri, 'debug');
            });

            hls.on(Hls.Events.KEY_LOADED, function(event, data) {
                log('解密密钥加载成功', 'debug');
            });

            // 片段加载事件
            hls.on(Hls.Events.FRAG_LOADING, function(event, data) {
                log('正在加载片段: ' + data.frag.url, 'debug');
            });

            hls.on(Hls.Events.FRAG_LOADED, function(event, data) {
                log('片段加载完成: ' + data.frag.url, 'debug');
            });

            // 媒体附加事件
            hls.on(Hls.Events.MEDIA_ATTACHED, function() {
                log('媒体已连接到HLS实例', 'debug');
            });

            // 缓冲区相关事件监听
            hls.on(Hls.Events.BUFFER_CREATED, function(event, data) {
                log('缓冲区已创建', 'debug');
            });

            hls.on(Hls.Events.BUFFER_APPENDED, function(event, data) {
                // 静默处理，避免日志过多
            });

            hls.on(Hls.Events.BUFFER_FLUSHED, function(event, data) {
                log('缓冲区已清理', 'debug');
            });
        }

        function playVideo() {
            log('尝试播放视频', 'debug');
            video.play().then(() => {
                log('视频播放开始', 'success');
            }).catch((error) => {
                log('播放失败：' + error.message, 'error');
            });
        }

        function pauseVideo() {
            video.pause();
            log('视频已暂停', 'info');
        }

        function reloadVideo() {
            if (currentM3u8Path) {
                log('重新加载视频: ' + currentM3u8Path, 'info');
                loadVideo(currentM3u8Path, false);
            } else {
                log('没有可重新加载的视频', 'error');
            }
        }

        // 视频事件监听
        video.addEventListener('loadstart', () => log('开始加载视频', 'debug'));
        video.addEventListener('canplay', () => log('视频可以播放', 'success'));
        video.addEventListener('playing', () => log('视频正在播放', 'success'));
        video.addEventListener('pause', () => log('视频已暂停', 'info'));
        video.addEventListener('ended', () => log('视频播放完成', 'info'));
        video.addEventListener('error', (e) => log('视频错误：' + e.message, 'error'));

        // 添加seek相关事件监听
        video.addEventListener('seeking', () => {
            log('正在跳转到新位置...', 'debug');
        });

        video.addEventListener('seeked', () => {
            log('跳转完成', 'debug');
            // seek完成后，确保HLS继续加载
            if (hls && hls.media) {
                setTimeout(() => {
                    if (hls.media.readyState < 3) { // 如果数据不足
                        hls.startLoad();
                    }
                }, 100);
            }
        });

        // 监听时间更新，用于缓冲区管理
        video.addEventListener('timeupdate', () => {
            // 静默处理，避免日志过多
            // 可以在这里添加缓冲区清理逻辑
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，准备就绪', 'info');

            // 设置默认地址
            m3u8Input.value = 'm3u8/video/c62bd2ba0196dc22ad386a463d049d215ad87985/playlist.m3u8';

            // 加密参数输入框保持空白，需要用户手动输入
            // customKey.value = '';
            // customIV.value = '';

            // 添加回车键监听
            m3u8Input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loadFromInput();
                }
            });

            // 添加输入框焦点时的提示
            m3u8Input.addEventListener('focus', function() {
                if (this.value === '') {
                    this.placeholder = '例如: m3u8/video/xxx/playlist.m3u8 或 http://example.com/video.m3u8';
                }
            });

            // 添加加密参数输入框的格式化
            [customKey, customIV].forEach(input => {
                input.addEventListener('input', function() {
                    // 只允许十六进制字符
                    this.value = this.value.replace(/[^0-9a-fA-F]/g, '').toLowerCase();
                    // 限制长度为32字符
                    if (this.value.length > 32) {
                        this.value = this.value.substring(0, 32);
                    }
                });

                input.addEventListener('paste', function(e) {
                    // 延迟处理粘贴内容
                    setTimeout(() => {
                        this.value = this.value.replace(/[^0-9a-fA-F]/g, '').toLowerCase();
                        if (this.value.length > 32) {
                            this.value = this.value.substring(0, 32);
                        }
                    }, 10);
                });
            });
        });
    </script>
</body>
</html>
