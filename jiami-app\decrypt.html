<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AES-256-GCM 图片解密器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .upload-area {
            border: 3px dashed #f093fb;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #f5576c;
            background: rgba(240, 147, 251, 0.05);
        }
        
        .upload-area.dragover {
            border-color: #f5576c;
            background: rgba(240, 147, 251, 0.1);
        }
        
        #fileInput {
            display: none;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .key-section {
            margin-bottom: 30px;
        }
        
        .key-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1.1em;
            margin-bottom: 15px;
            transition: border-color 0.3s ease;
        }
        
        .key-input:focus {
            outline: none;
            border-color: #f093fb;
        }
        
        .btn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #f093fb, #f5576c);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .results {
            margin-top: 30px;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .image-item:hover {
            transform: translateY(-5px);
        }
        
        .image-preview {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .image-info {
            margin-bottom: 10px;
        }
        
        .image-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .image-size {
            color: #666;
            font-size: 0.9em;
        }
        
        .download-btn {
            background: #28a745;
            padding: 8px 16px;
            font-size: 0.9em;
            width: 100%;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }
        
        .modal img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #bbb;
        }

        .performance-stats {
            background: #f3e5f5;
            border: 1px solid #e1bee7;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }

        .performance-stats h4 {
            color: #7b1fa2;
            margin-bottom: 10px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #7b1fa2;
        }

        .file-type-selector {
            text-align: center;
            margin-bottom: 20px;
        }

        .mode-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background: #ddd;
            color: #666;
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .encryption-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-size: 0.9em;
            color: #856404;
        }

        .file-size-warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-size: 0.9em;
            color: #721c24;
            display: none;
        }

        .memory-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-size: 0.9em;
            color: #0c5460;
        }

        .video-preview {
            width: 100%;
            height: 200px;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }

        .video-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
        }

        .video-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .video-modal video {
            width: 100%;
            height: 100%;
            max-width: 90vw;
            max-height: 90vh;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔓 AES-256-GCM 图片解密器</h1>
        
        <div class="key-section">
            <input type="password" id="decryptionKey" class="key-input" placeholder="请输入解密密钥" />
        </div>
        
        <div class="file-type-selector">
            <button id="imageMode" class="btn mode-btn active">📷 图片模式</button>
            <button id="videoMode" class="btn mode-btn">🎬 视频模式</button>
        </div>

        <div class="upload-area" id="uploadArea">
            <div class="upload-text" id="uploadText">
                <strong>点击选择加密文件或拖拽到此处</strong><br>
                支持批量上传 .encrypted 格式文件
            </div>
            <input type="file" id="fileInput" multiple accept=".encrypted">
        </div>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">准备中...</div>
        </div>
        
        <div id="status"></div>

        <div class="encryption-info" id="encryptionInfo">
            📷 图片模式：解密 AES-256-GCM 加密的图片文件
        </div>

        <div class="file-size-warning" id="fileSizeWarning">
            ⚠️ 注意：支持最大10GB文件，大文件将自动使用高性能分块解密
        </div>

        <div class="memory-info" id="memoryInfo" style="display: none;">
            <div id="memoryStatus"></div>
        </div>

        <div class="performance-stats" id="performanceStats">
            <h4>📊 性能统计</h4>
            <div class="stats-grid" id="statsGrid"></div>
        </div>

        <div class="results" id="results"></div>
    </div>
    
    <!-- 图片预览模态框 -->
    <div id="imageModal" class="modal">
        <span class="close">&times;</span>
        <div class="modal-content">
            <img id="modalImage" src="" alt="预览图片">
        </div>
    </div>

    <!-- 视频预览模态框 -->
    <div id="videoModal" class="video-modal">
        <span class="close">&times;</span>
        <div class="video-modal-content">
            <video id="modalVideo" controls>
                您的浏览器不支持视频播放。
            </video>
        </div>
    </div>

    <script>
        class AESImageDecryptor {
            constructor() {
                this.initializeElements();
                this.setupEventListeners();
                this.decryptedFiles = [];
                this.currentMode = 'image'; // 'image' or 'video'
            }
            
            initializeElements() {
                this.uploadArea = document.getElementById('uploadArea');
                this.fileInput = document.getElementById('fileInput');
                this.decryptionKeyInput = document.getElementById('decryptionKey');
                this.progressContainer = document.getElementById('progressContainer');
                this.progressFill = document.getElementById('progressFill');
                this.progressText = document.getElementById('progressText');
                this.status = document.getElementById('status');
                this.results = document.getElementById('results');
                this.modal = document.getElementById('imageModal');
                this.modalImage = document.getElementById('modalImage');
                this.videoModal = document.getElementById('videoModal');
                this.modalVideo = document.getElementById('modalVideo');
                this.closeBtn = document.querySelector('.close');
                this.performanceStats = document.getElementById('performanceStats');
                this.statsGrid = document.getElementById('statsGrid');
                this.imageModeBtn = document.getElementById('imageMode');
                this.videoModeBtn = document.getElementById('videoMode');
                this.uploadText = document.getElementById('uploadText');
                this.encryptionInfo = document.getElementById('encryptionInfo');
                this.fileSizeWarning = document.getElementById('fileSizeWarning');
                this.memoryInfo = document.getElementById('memoryInfo');
                this.memoryStatus = document.getElementById('memoryStatus');

                // 初始化内存监控
                this.initMemoryMonitoring();
            }
            
            setupEventListeners() {
                this.uploadArea.addEventListener('click', () => this.fileInput.click());
                this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
                this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                this.imageModeBtn.addEventListener('click', () => this.switchMode('image'));
                this.videoModeBtn.addEventListener('click', () => this.switchMode('video'));

                // 图片模态框事件
                this.closeBtn.addEventListener('click', () => this.modal.style.display = 'none');
                this.modal.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.modal.style.display = 'none';
                    }
                });

                // 视频模态框事件
                const videoCloseBtn = this.videoModal.querySelector('.close');
                videoCloseBtn.addEventListener('click', () => this.videoModal.style.display = 'none');
                this.videoModal.addEventListener('click', (e) => {
                    if (e.target === this.videoModal) {
                        this.videoModal.style.display = 'none';
                    }
                });
            }
            
            handleDragOver(e) {
                e.preventDefault();
                this.uploadArea.classList.add('dragover');
            }
            
            handleDragLeave(e) {
                e.preventDefault();
                this.uploadArea.classList.remove('dragover');
            }
            
            handleDrop(e) {
                e.preventDefault();
                this.uploadArea.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    this.processFiles(files);
                }
            }
            
            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    this.processFiles(files);
                }
            }

            switchMode(mode) {
                this.currentMode = mode;

                if (mode === 'image') {
                    this.imageModeBtn.classList.add('active');
                    this.videoModeBtn.classList.remove('active');
                    this.uploadText.innerHTML = `
                        <strong>点击选择加密文件或拖拽到此处</strong><br>
                        支持批量上传图片加密文件 (.encrypted)
                    `;
                    this.encryptionInfo.textContent = '📷 图片模式：解密 AES-256-GCM 加密的图片文件';
                    this.fileSizeWarning.style.display = 'none';
                } else {
                    this.videoModeBtn.classList.add('active');
                    this.imageModeBtn.classList.remove('active');
                    this.uploadText.innerHTML = `
                        <strong>点击选择加密文件或拖拽到此处</strong><br>
                        支持批量上传视频加密文件 (.encrypted)
                    `;
                    this.encryptionInfo.textContent = '🎬 视频模式：解密 AES-128-GCM 加密的视频文件';
                    this.fileSizeWarning.style.display = 'block';
                }

                // 清空之前的结果
                this.results.innerHTML = '';
                this.performanceStats.style.display = 'none';
                this.decryptedFiles = [];
            }
            
            async deriveKey(password, salt, keyLength = 256) {
                const encoder = new TextEncoder();
                const keyMaterial = await crypto.subtle.importKey(
                    'raw',
                    encoder.encode(password),
                    { name: 'PBKDF2' },
                    false,
                    ['deriveKey']
                );

                const key = await crypto.subtle.deriveKey(
                    {
                        name: 'PBKDF2',
                        salt: salt,
                        iterations: 100000,
                        hash: 'SHA-256'
                    },
                    keyMaterial,
                    { name: 'AES-GCM', length: keyLength },
                    false,
                    ['decrypt']
                );

                return key;
            }
            
            async decryptFile(encryptedBuffer, password, progressCallback) {
                // 解析文件格式: salt(16) + iv(12) + metadataLength(4) + metadata + encryptedData
                const salt = encryptedBuffer.slice(0, 16);
                const iv = encryptedBuffer.slice(16, 28);
                const metadataLengthBuffer = encryptedBuffer.slice(28, 32);
                const metadataLength = new Uint32Array(metadataLengthBuffer)[0];
                const metadataBuffer = encryptedBuffer.slice(32, 32 + metadataLength);
                const encryptedData = encryptedBuffer.slice(32 + metadataLength);

                // 解析元数据
                const metadata = JSON.parse(new TextDecoder().decode(metadataBuffer));

                // 派生密钥并测量时间
                const keyStartTime = performance.now();
                // 根据文件类型确定密钥长度：图片256位，视频128位
                const keyLength = metadata.fileType === 'image' ? 256 : 128;
                const key = await this.deriveKey(password, salt, keyLength);
                const keyDerivationTime = performance.now() - keyStartTime;

                let decryptedData;

                // 检查是否为分块加密的文件
                if (metadata.isChunked) {
                    decryptedData = await this.decryptChunkedData(encryptedData, key, progressCallback);
                } else {
                    // 传统单块解密
                    decryptedData = await crypto.subtle.decrypt(
                        {
                            name: 'AES-GCM',
                            iv: iv,
                            tagLength: 128
                        },
                        key,
                        encryptedData
                    );
                }

                return {
                    data: decryptedData,
                    metadata: metadata,
                    keyDerivationTime: keyDerivationTime
                };
            }

            async decryptChunkedData(encryptedData, key, progressCallback) {
                const dataView = new DataView(encryptedData);
                let offset = 0;

                // 读取分块数量
                const chunkCount = dataView.getUint32(offset, true);
                offset += 4;

                // 计算总的解密数据大小
                let totalDecryptedSize = 0;
                const chunkInfos = [];
                let tempOffset = offset;

                // 预扫描获取所有分块信息
                for (let i = 0; i < chunkCount; i++) {
                    const ivLength = dataView.getUint32(tempOffset, true);
                    tempOffset += 4 + ivLength;

                    const dataLength = dataView.getUint32(tempOffset, true);
                    tempOffset += 4;

                    // 估算解密后的大小（通常比加密数据小16字节的认证标签）
                    const decryptedSize = Math.max(0, dataLength - 16);
                    totalDecryptedSize += decryptedSize;

                    chunkInfos.push({
                        ivLength,
                        dataLength,
                        estimatedDecryptedSize: decryptedSize
                    });

                    tempOffset += dataLength;
                }

                // 创建最终结果数组
                const finalResult = new Uint8Array(totalDecryptedSize);
                let resultOffset = 0;

                // 分批处理分块，避免内存积累
                const batchSize = 5; // 每批处理5个分块

                for (let batchStart = 0; batchStart < chunkCount; batchStart += batchSize) {
                    const batchEnd = Math.min(batchStart + batchSize, chunkCount);
                    const batchChunks = [];

                    // 处理当前批次
                    for (let i = batchStart; i < batchEnd; i++) {
                        try {
                            // 读取IV长度
                            const ivLength = dataView.getUint32(offset, true);
                            offset += 4;

                            // 读取IV
                            const iv = new Uint8Array(encryptedData, offset, ivLength);
                            offset += ivLength;

                            // 读取数据长度
                            const dataLength = dataView.getUint32(offset, true);
                            offset += 4;

                            // 读取加密数据
                            const chunkData = new Uint8Array(encryptedData, offset, dataLength);
                            offset += dataLength;

                            // 解密分块
                            const decryptedChunk = await crypto.subtle.decrypt(
                                {
                                    name: 'AES-GCM',
                                    iv: iv,
                                    tagLength: 128
                                },
                                key,
                                chunkData
                            );

                            batchChunks.push(new Uint8Array(decryptedChunk));

                            // 更新进度
                            if (progressCallback) {
                                progressCallback((i + 1) / chunkCount * 100);
                            }

                            // 让出控制权，避免阻塞UI
                            await new Promise(resolve => setTimeout(resolve, 1));

                        } catch (error) {
                            console.error(`解密分块 ${i} 失败:`, error);
                            throw new Error(`分块 ${i} 解密失败: ${error.message}`);
                        }
                    }

                    // 将当前批次写入最终结果
                    for (const chunk of batchChunks) {
                        finalResult.set(chunk, resultOffset);
                        resultOffset += chunk.length;
                    }

                    // 清理批次数据
                    batchChunks.length = 0;

                    // 强制垃圾回收提示
                    if (window.gc) {
                        window.gc();
                    }
                }

                return finalResult.buffer;
            }
            
            async processFiles(files) {
                const password = this.decryptionKeyInput.value.trim();
                if (password.length === 0) {
                    this.showStatus('请输入解密密钥', 'error');
                    return;
                }

                this.showProgress(true);
                this.decryptedFiles = [];

                // 性能统计变量
                const startTime = performance.now();
                let totalEncryptedSize = 0;
                let totalDecryptedSize = 0;
                let keyDerivationTime = 0;
                let decryptionTime = 0;
                let parseTime = 0;

                try {
                    const decryptStartTime = performance.now();

                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        totalEncryptedSize += file.size;

                        // 显示文件大小信息
                        const fileSizeMB = (file.size / 1024 / 1024).toFixed(2);
                        this.updateProgress((i / files.length) * 100, `正在解密: ${file.name} (${fileSizeMB} MB)`);

                        // 检查文件大小限制和内存可用性
                        if (file.size > 10 * 1024 * 1024 * 1024) { // 10GB限制
                            throw new Error(`文件 ${file.name} 过大 (${fileSizeMB} MB)，请选择小于10GB的文件`);
                        }

                        // 检查文件是否为空
                        if (file.size === 0) {
                            throw new Error(`文件 ${file.name} 为空文件`);
                        }

                        // 检查文件扩展名
                        if (!file.name.endsWith('.encrypted')) {
                            console.warn(`文件 ${file.name} 不是标准的加密文件扩展名`);
                        }

                        // 检查可用内存（粗略估算）
                        if (performance.memory) {
                            const availableMemory = performance.memory.jsHeapSizeLimit - performance.memory.usedJSHeapSize;
                            const estimatedMemoryNeeded = file.size * 0.4; // 解密需要更多内存

                            console.log(`文件 ${file.name}: 大小 ${fileSizeMB}MB, 可用内存: ${(availableMemory/1024/1024).toFixed(2)}MB, 估算需要: ${(estimatedMemoryNeeded/1024/1024).toFixed(2)}MB`);

                            if (estimatedMemoryNeeded > availableMemory) {
                                console.warn(`文件 ${file.name} 可能导致内存不足`);
                            }
                        }

                        const parseStartTime = performance.now();
                        let encryptedBuffer;
                        // 分块解密进度回调 - 先定义回调函数
                        let currentFileProgress = 0;
                        const fileProgressCallback = (chunkProgress) => {
                            currentFileProgress = chunkProgress;
                            const overallProgress = ((i + currentFileProgress / 100) / files.length) * 100;
                            this.updateProgress(overallProgress, `正在解密: ${file.name} (${fileSizeMB} MB) - ${chunkProgress.toFixed(1)}%`);
                        };

                        try {
                            console.log(`开始处理文件 ${file.name}, 大小: ${fileSizeMB}MB`);

                            // 先尝试读取文件头部进行快速验证
                            await this.validateFileHeader(file);

                            // 根据文件大小选择处理方式
                            if (file.size > 100 * 1024 * 1024) { // 大于100MB使用流式处理
                                console.log(`使用流式处理大文件: ${file.name}`);
                                const fileDecryptStartTime = performance.now();
                                const streamResult = await this.processLargeFileStream(file, password, fileProgressCallback);

                                // 流式处理已经完成解密，直接使用结果
                                const { data, metadata } = streamResult;

                                totalDecryptedSize += data.size || data.byteLength;

                                // 创建文件URL
                                const fileUrl = data.size ? URL.createObjectURL(data) : URL.createObjectURL(new Blob([data]));

                                this.decryptedFiles.push({
                                    name: metadata.originalName,
                                    size: metadata.originalSize,
                                    mimeType: metadata.mimeType,
                                    timestamp: metadata.timestamp,
                                    fileType: metadata.fileType || 'image',
                                    encryptionAlgorithm: metadata.encryptionAlgorithm || 'AES-256-GCM',
                                    isChunked: metadata.isChunked || false,
                                    fileUrl: fileUrl,
                                    blob: data.size ? data : new Blob([data]),
                                    decryptTime: performance.now() - fileDecryptStartTime
                                });

                                continue; // 跳过后续的传统处理流程
                            } else {
                                // 小文件使用传统方式
                                console.log(`使用传统方式处理小文件: ${file.name}`);
                                const buffer = await this.readFileAsArrayBuffer(file);
                                encryptedBuffer = buffer;
                            }

                            // 流式处理的情况已经在上面处理完毕，这里只处理小文件

                            console.log(`成功读取文件 ${file.name}, 数据长度: ${encryptedBuffer.length} 字节`);

                            // 验证文件格式
                            if (encryptedBuffer.length < 32) { // 至少需要 salt(16) + iv(12) + metadataLength(4)
                                throw new Error(`文件 ${file.name} 格式不正确，文件太小`);
                            }

                            // 验证文件完整性
                            if (encryptedBuffer.length !== file.size) {
                                throw new Error(`文件 ${file.name} 读取不完整，期望 ${file.size} 字节，实际 ${encryptedBuffer.length} 字节`);
                            }

                        } catch (error) {
                            console.error(`处理文件 ${file.name} 失败:`, error);
                            throw new Error(`处理文件 ${file.name} 失败: ${error.message}`);
                        }
                        parseTime += performance.now() - parseStartTime;

                        const fileDecryptStartTime = performance.now();
                        const { data, metadata, keyDerivationTime: fileKeyTime } = await this.decryptFile(encryptedBuffer, password, fileProgressCallback);
                        keyDerivationTime += fileKeyTime;

                        totalDecryptedSize += data.byteLength;

                        // 创建文件URL
                        const blob = new Blob([data], { type: metadata.mimeType });
                        const fileUrl = URL.createObjectURL(blob);

                        this.decryptedFiles.push({
                            name: metadata.originalName,
                            size: metadata.originalSize,
                            mimeType: metadata.mimeType,
                            timestamp: metadata.timestamp,
                            fileType: metadata.fileType || 'image',
                            encryptionAlgorithm: metadata.encryptionAlgorithm || 'AES-256-GCM',
                            isChunked: metadata.isChunked || false,
                            fileUrl: fileUrl,
                            blob: blob,
                            decryptTime: performance.now() - fileDecryptStartTime
                        });
                    }

                    decryptionTime = performance.now() - decryptStartTime;
                    const totalTime = performance.now() - startTime;

                    this.updateProgress(100, '解密完成！');
                    this.showStatus(`成功解密 ${files.length} 个文件`, 'success');

                    // 计算分块处理统计
                    const chunkedFiles = this.decryptedFiles.filter(f => f.isChunked).length;
                    const avgFileSize = totalDecryptedSize / files.length;
                    const firstFileType = this.decryptedFiles.length > 0 ? this.decryptedFiles[0].fileType : 'image';
                    const firstAlgorithm = this.decryptedFiles.length > 0 ? this.decryptedFiles[0].encryptionAlgorithm : 'AES-256-GCM';

                    // 显示性能统计
                    this.displayPerformanceStats({
                        totalTime,
                        keyDerivationTime,
                        decryptionTime,
                        parseTime,
                        totalEncryptedSize,
                        totalDecryptedSize,
                        fileCount: files.length,
                        chunkedFiles,
                        avgFileSize,
                        throughput: totalEncryptedSize / (totalTime / 1000), // bytes per second
                        avgDecryptTime: this.decryptedFiles.reduce((sum, file) => sum + file.decryptTime, 0) / files.length,
                        mode: firstFileType,
                        algorithm: firstAlgorithm
                    });

                    this.displayResults();

                } catch (error) {
                    this.showStatus(`解密失败: ${error.message}`, 'error');
                } finally {
                    setTimeout(() => this.showProgress(false), 1000);
                }
            }
            
            displayResults() {
                this.results.innerHTML = '<h3>解密结果</h3><div class="image-grid" id="fileGrid"></div>';
                const fileGrid = document.getElementById('fileGrid');

                this.decryptedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'image-item';

                    let previewElement = '';
                    let modalAction = '';
                    let downloadText = '';

                    if (file.fileType === 'video') {
                        previewElement = `<video src="${file.fileUrl}" class="video-preview" onclick="decryptor.showVideoModal('${file.fileUrl}')" muted></video>`;
                        modalAction = `onclick="decryptor.showVideoModal('${file.fileUrl}')"`;
                        downloadText = '下载视频';
                    } else {
                        previewElement = `<img src="${file.fileUrl}" alt="${file.name}" class="image-preview" onclick="decryptor.showImageModal('${file.fileUrl}')">`;
                        modalAction = `onclick="decryptor.showImageModal('${file.fileUrl}')"`;
                        downloadText = '下载图片';
                    }

                    fileItem.innerHTML = `
                        ${previewElement}
                        <div class="image-info">
                            <div class="image-name">${file.name}</div>
                            <div class="image-size">${this.formatFileSize(file.size)}</div>
                            <div class="image-size">算法: ${file.encryptionAlgorithm}</div>
                            <div class="image-size">解密时间: ${new Date(file.timestamp).toLocaleString()}</div>
                        </div>
                        <button class="btn download-btn" onclick="decryptor.downloadDecryptedFile(${index})">
                            ${downloadText}
                        </button>
                    `;
                    fileGrid.appendChild(fileItem);
                });
            }
            
            showImageModal(imageUrl) {
                this.modalImage.src = imageUrl;
                this.modal.style.display = 'block';
            }

            showVideoModal(videoUrl) {
                this.modalVideo.src = videoUrl;
                this.videoModal.style.display = 'block';
            }

            downloadDecryptedFile(index) {
                const file = this.decryptedFiles[index];
                const url = file.fileUrl;
                const a = document.createElement('a');
                a.href = url;
                a.download = file.name;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }
            
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            showProgress(show) {
                this.progressContainer.style.display = show ? 'block' : 'none';
                if (!show) {
                    this.progressFill.style.width = '0%';
                }
            }
            
            updateProgress(percent, text) {
                this.progressFill.style.width = percent + '%';
                this.progressText.textContent = text;
            }
            
            displayPerformanceStats(stats) {
                this.performanceStats.style.display = 'block';

                const sizeReduction = ((stats.totalEncryptedSize - stats.totalDecryptedSize) / stats.totalEncryptedSize * 100).toFixed(2);
                const avgFileSize = (stats.totalDecryptedSize / stats.fileCount / 1024 / 1024).toFixed(2);
                const throughputMBps = (stats.throughput / 1024 / 1024).toFixed(2);
                const avgKeyDerivationTime = (stats.keyDerivationTime / stats.fileCount).toFixed(2);
                const modeIcon = stats.mode === 'image' ? '📷' : '🎬';
                const modeText = stats.mode === 'image' ? '图片' : '视频';

                this.statsGrid.innerHTML = `
                    <div class="stat-item">
                        <div class="stat-label">处理模式</div>
                        <div class="stat-value">${modeIcon} ${modeText}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">解密算法</div>
                        <div class="stat-value">${stats.algorithm}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">总用时</div>
                        <div class="stat-value">${stats.totalTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">密钥派生总时间</div>
                        <div class="stat-value">${stats.keyDerivationTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">解密时间</div>
                        <div class="stat-value">${stats.decryptionTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">文件解析时间</div>
                        <div class="stat-value">${stats.parseTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">处理文件数</div>
                        <div class="stat-value">${stats.fileCount} 个</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">加密文件总大小</div>
                        <div class="stat-value">${this.formatFileSize(stats.totalEncryptedSize)}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">解密后总大小</div>
                        <div class="stat-value">${this.formatFileSize(stats.totalDecryptedSize)}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">大小减少</div>
                        <div class="stat-value">${sizeReduction}%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">平均文件大小</div>
                        <div class="stat-value">${avgFileSize} MB</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">处理速度</div>
                        <div class="stat-value">${throughputMBps} MB/s</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">平均解密用时</div>
                        <div class="stat-value">${stats.avgDecryptTime.toFixed(2)} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">平均密钥派生用时</div>
                        <div class="stat-value">${avgKeyDerivationTime} ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">分块处理文件</div>
                        <div class="stat-value">${stats.chunkedFiles || 0} 个</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">处理策略</div>
                        <div class="stat-value">${stats.avgFileSize > 50 * 1024 * 1024 ? '🚀 高性能分块' : '⚡ 直接处理'}</div>
                    </div>
                `;
            }

            async readFileAsArrayBuffer(file) {
                // 对于大文件，使用分块读取
                if (file.size > 500 * 1024 * 1024) { // 大于500MB使用分块读取
                    return this.readLargeFileInChunks(file);
                }

                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        try {
                            const result = e.target.result;
                            if (!result) {
                                reject(new Error('文件读取结果为空'));
                                return;
                            }
                            console.log(`文件读取成功，大小: ${result.byteLength} 字节`);
                            resolve(new Uint8Array(result));
                        } catch (error) {
                            console.error('处理文件数据失败:', error);
                            reject(new Error(`处理文件数据失败: ${error.message}`));
                        }
                    };

                    reader.onerror = function(e) {
                        console.error('FileReader错误详情:', {
                            error: e,
                            readyState: reader.readyState,
                            result: reader.result,
                            fileName: file.name,
                            fileSize: file.size,
                            fileType: file.type,
                            lastModified: file.lastModified
                        });

                        let errorMsg = '文件读取失败';
                        if (e.target && e.target.error) {
                            switch (e.target.error.name) {
                                case 'NotFoundError':
                                    errorMsg = '文件未找到';
                                    break;
                                case 'SecurityError':
                                    errorMsg = '文件访问权限不足';
                                    break;
                                case 'NotReadableError':
                                    errorMsg = '文件无法读取，可能已损坏';
                                    break;
                                case 'EncodingError':
                                    errorMsg = '文件编码错误';
                                    break;
                                default:
                                    errorMsg = `文件读取错误: ${e.target.error.name}`;
                            }
                        }
                        reject(new Error(errorMsg));
                    };

                    reader.onabort = function(e) {
                        console.log('文件读取被中断');
                        reject(new Error('文件读取被中断'));
                    };

                    reader.onprogress = function(e) {
                        if (e.lengthComputable) {
                            const progress = (e.loaded / e.total * 100).toFixed(1);
                            console.log(`文件读取进度: ${progress}% (${e.loaded}/${e.total})`);
                        }
                    };

                    // 添加超时处理
                    const timeout = setTimeout(() => {
                        console.log('文件读取超时，正在中断...');
                        reader.abort();
                        reject(new Error(`文件读取超时 (${file.size > 1024*1024*1024 ? '大文件' : '小文件'})`));
                    }, file.size > 1024*1024*1024 ? 300000 : 120000); // 大文件5分钟，小文件2分钟

                    reader.onloadend = function() {
                        clearTimeout(timeout);
                    };

                    try {
                        console.log(`开始读取文件: ${file.name}, 大小: ${(file.size/1024/1024).toFixed(2)}MB`);
                        // 使用readAsArrayBuffer读取文件
                        reader.readAsArrayBuffer(file);
                    } catch (error) {
                        clearTimeout(timeout);
                        console.error('启动文件读取失败:', error);
                        reject(new Error(`启动文件读取失败: ${error.message}`));
                    }
                });
            }

            async readLargeFileInChunks(file) {
                console.log(`使用分块读取大文件: ${file.name}, 大小: ${(file.size/1024/1024).toFixed(2)}MB`);

                const chunkSize = 50 * 1024 * 1024; // 50MB分块
                const totalChunks = Math.ceil(file.size / chunkSize);
                const chunks = [];

                for (let i = 0; i < totalChunks; i++) {
                    const start = i * chunkSize;
                    const end = Math.min(start + chunkSize, file.size);
                    const chunk = file.slice(start, end);

                    console.log(`读取分块 ${i + 1}/${totalChunks}: ${start}-${end}`);

                    try {
                        const chunkData = await new Promise((resolve, reject) => {
                            const reader = new FileReader();

                            reader.onload = (e) => resolve(new Uint8Array(e.target.result));
                            reader.onerror = (e) => reject(new Error(`分块 ${i + 1} 读取失败`));
                            reader.onabort = (e) => reject(new Error(`分块 ${i + 1} 读取被中断`));

                            const timeout = setTimeout(() => {
                                reader.abort();
                                reject(new Error(`分块 ${i + 1} 读取超时`));
                            }, 60000);

                            reader.onloadend = () => clearTimeout(timeout);
                            reader.readAsArrayBuffer(chunk);
                        });

                        chunks.push(chunkData);

                        // 让出控制权
                        await new Promise(resolve => setTimeout(resolve, 10));

                    } catch (error) {
                        console.error(`分块读取失败:`, error);
                        throw error;
                    }
                }

                // 合并所有分块
                const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
                const combined = new Uint8Array(totalLength);
                let offset = 0;

                for (const chunk of chunks) {
                    combined.set(chunk, offset);
                    offset += chunk.length;
                }

                console.log(`分块读取完成，总大小: ${combined.length} 字节`);
                return combined;
            }

            async validateFileHeader(file) {
                // 读取文件前32字节进行快速验证
                const headerChunk = file.slice(0, 32);

                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        try {
                            const headerData = new Uint8Array(e.target.result);
                            console.log(`文件头部验证: ${headerData.length} 字节`);

                            if (headerData.length < 32) {
                                reject(new Error('文件头部不完整'));
                                return;
                            }

                            // 检查是否全为0（可能的损坏文件）
                            const allZero = headerData.every(byte => byte === 0);
                            if (allZero) {
                                reject(new Error('文件头部全为0，可能是损坏的文件'));
                                return;
                            }

                            console.log('文件头部验证通过');
                            resolve();

                        } catch (error) {
                            reject(new Error(`文件头部验证失败: ${error.message}`));
                        }
                    };

                    reader.onerror = function(e) {
                        console.error('文件头部读取失败:', e);
                        reject(new Error('无法读取文件头部，文件可能已损坏'));
                    };

                    const timeout = setTimeout(() => {
                        reader.abort();
                        reject(new Error('文件头部读取超时'));
                    }, 10000); // 10秒超时

                    reader.onloadend = () => clearTimeout(timeout);

                    try {
                        reader.readAsArrayBuffer(headerChunk);
                    } catch (error) {
                        clearTimeout(timeout);
                        reject(new Error(`启动文件头部读取失败: ${error.message}`));
                    }
                });
            }

            async processLargeFileStream(file, password, progressCallback) {
                console.log(`开始流式解密大文件: ${file.name}`);

                try {
                    // 读取文件头部信息 (salt + iv + metadata)
                    const headerSize = 32; // 初始估算
                    const headerChunk = file.slice(0, headerSize);
                    const headerBuffer = await this.readChunkAsArrayBuffer(headerChunk);

                    // 解析头部
                    const salt = headerBuffer.slice(0, 16);
                    const iv = headerBuffer.slice(16, 28);
                    const metadataLengthBuffer = headerBuffer.slice(28, 32);
                    const metadataLength = new Uint32Array(metadataLengthBuffer)[0];

                    // 读取完整的元数据
                    const fullHeaderSize = 32 + metadataLength;
                    const fullHeaderChunk = file.slice(0, fullHeaderSize);
                    const fullHeaderBuffer = await this.readChunkAsArrayBuffer(fullHeaderChunk);

                    const metadataBuffer = fullHeaderBuffer.slice(32, 32 + metadataLength);
                    const metadata = JSON.parse(new TextDecoder().decode(metadataBuffer));

                    console.log('解析文件元数据:', metadata);

                    // 派生密钥
                    const keyLength = metadata.fileType === 'image' ? 256 : 128;
                    const key = await this.deriveKey(password, salt, keyLength);

                    // 获取加密数据部分
                    const encryptedDataBlob = file.slice(fullHeaderSize);

                    let decryptedData;

                    if (metadata.isChunked) {
                        // 分块解密
                        decryptedData = await this.decryptChunkedDataStream(encryptedDataBlob, key, progressCallback);
                    } else {
                        // 单块解密
                        const encryptedBuffer = await this.readChunkAsArrayBuffer(encryptedDataBlob);
                        const decrypted = await crypto.subtle.decrypt(
                            {
                                name: 'AES-GCM',
                                iv: iv,
                                tagLength: 128
                            },
                            key,
                            encryptedBuffer
                        );
                        decryptedData = new Blob([decrypted], { type: metadata.mimeType });
                    }

                    return {
                        data: decryptedData,
                        metadata: metadata
                    };

                } catch (error) {
                    console.error('流式解密失败:', error);
                    throw new Error(`流式解密失败: ${error.message}`);
                }
            }

            async readChunkAsArrayBuffer(blob) {
                return new Promise((resolve, reject) => {
                    if (!blob || blob.size === 0) {
                        reject(new Error('无效的blob数据'));
                        return;
                    }

                    const reader = new FileReader();

                    reader.onload = (e) => {
                        try {
                            const result = e.target.result;
                            if (!result) {
                                reject(new Error('分块读取结果为空'));
                                return;
                            }
                            console.log(`成功读取分块: ${result.byteLength} 字节`);
                            resolve(new Uint8Array(result));
                        } catch (error) {
                            reject(new Error(`处理分块数据失败: ${error.message}`));
                        }
                    };

                    reader.onerror = (e) => {
                        console.error('分块读取错误:', e);
                        reject(new Error(`分块读取失败: ${e.target?.error?.message || '未知错误'}`));
                    };

                    reader.onabort = (e) => {
                        reject(new Error('分块读取被中断'));
                    };

                    const timeout = setTimeout(() => {
                        reader.abort();
                        reject(new Error(`分块读取超时 (大小: ${blob.size} 字节)`));
                    }, 60000);

                    reader.onloadend = () => clearTimeout(timeout);

                    try {
                        console.log(`开始读取分块: ${blob.size} 字节`);
                        reader.readAsArrayBuffer(blob);
                    } catch (error) {
                        clearTimeout(timeout);
                        reject(new Error(`启动分块读取失败: ${error.message}`));
                    }
                });
            }

            async decryptChunkedDataStream(encryptedBlob, key, progressCallback) {
                console.log(`开始流式解密分块数据，大小: ${(encryptedBlob.size/1024/1024).toFixed(2)}MB`);

                try {
                    // 读取分块头部信息
                    console.log('读取分块数量...');
                    const chunkCountBuffer = await this.readChunkAsArrayBuffer(encryptedBlob.slice(0, 4));
                    const chunkCount = new Uint32Array(chunkCountBuffer.buffer)[0];

                    console.log(`分块数量: ${chunkCount}`);

                    if (chunkCount <= 0 || chunkCount > 10000) {
                        throw new Error(`无效的分块数量: ${chunkCount}`);
                    }

                    const decryptedBlobs = [];
                    let offset = 4;

                    for (let i = 0; i < chunkCount; i++) {
                        try {
                            console.log(`处理分块 ${i + 1}/${chunkCount}, 当前偏移: ${offset}`);

                            // 检查偏移量是否超出文件大小
                            if (offset >= encryptedBlob.size) {
                                throw new Error(`偏移量超出文件大小: ${offset} >= ${encryptedBlob.size}`);
                            }

                            // 读取IV长度
                            if (offset + 4 > encryptedBlob.size) {
                                throw new Error(`无法读取IV长度，文件可能已损坏`);
                            }
                            const ivLengthBuffer = await this.readChunkAsArrayBuffer(encryptedBlob.slice(offset, offset + 4));
                            const ivLength = new Uint32Array(ivLengthBuffer.buffer)[0];
                            offset += 4;

                            console.log(`分块 ${i}: IV长度 = ${ivLength}`);

                            if (ivLength !== 12) {
                                throw new Error(`无效的IV长度: ${ivLength}, 期望12字节`);
                            }

                            // 读取IV
                            if (offset + ivLength > encryptedBlob.size) {
                                throw new Error(`无法读取IV数据，文件可能已损坏`);
                            }
                            const iv = await this.readChunkAsArrayBuffer(encryptedBlob.slice(offset, offset + ivLength));
                            offset += ivLength;

                            // 读取数据长度
                            if (offset + 4 > encryptedBlob.size) {
                                throw new Error(`无法读取数据长度，文件可能已损坏`);
                            }
                            const dataLengthBuffer = await this.readChunkAsArrayBuffer(encryptedBlob.slice(offset, offset + 4));
                            const dataLength = new Uint32Array(dataLengthBuffer.buffer)[0];
                            offset += 4;

                            console.log(`分块 ${i}: 数据长度 = ${dataLength}`);

                            if (dataLength <= 0 || dataLength > 50 * 1024 * 1024) {
                                throw new Error(`无效的数据长度: ${dataLength}`);
                            }

                            // 读取加密数据
                            if (offset + dataLength > encryptedBlob.size) {
                                throw new Error(`无法读取加密数据，文件可能已损坏: 需要${dataLength}字节，但只剩${encryptedBlob.size - offset}字节`);
                            }
                            const chunkData = await this.readChunkAsArrayBuffer(encryptedBlob.slice(offset, offset + dataLength));
                            offset += dataLength;

                            console.log(`分块 ${i}: 开始解密 ${dataLength} 字节数据`);

                            // 解密分块
                            const decryptedChunk = await crypto.subtle.decrypt(
                                {
                                    name: 'AES-GCM',
                                    iv: iv,
                                    tagLength: 128
                                },
                                key,
                                chunkData
                            );

                            decryptedBlobs.push(new Blob([decryptedChunk]));

                            // 更新进度
                            if (progressCallback) {
                                progressCallback((i + 1) / chunkCount * 100);
                            }

                            console.log(`解密分块 ${i + 1}/${chunkCount} 完成，解密后大小: ${decryptedChunk.byteLength} 字节`);

                            // 让出控制权
                            await new Promise(resolve => setTimeout(resolve, 1));

                        } catch (error) {
                            console.error(`解密分块 ${i} 详细错误:`, {
                                error: error.message,
                                offset: offset,
                                blobSize: encryptedBlob.size,
                                chunkIndex: i,
                                totalChunks: chunkCount
                            });
                            throw new Error(`解密分块 ${i} 失败: ${error.message}`);
                        }
                    }

                    // 合并所有解密的分块为一个Blob
                    const finalBlob = new Blob(decryptedBlobs);
                    console.log(`流式解密完成，最终大小: ${(finalBlob.size/1024/1024).toFixed(2)}MB`);

                    return finalBlob;

                } catch (error) {
                    console.error('流式解密分块数据失败:', error);
                    throw error;
                }
            }

            // 保留旧方法用于向后兼容
            async readLargeFile(file) {
                return this.readFileAsArrayBuffer(file);
            }

            initMemoryMonitoring() {
                if (performance.memory) {
                    this.memoryInfo.style.display = 'block';
                    this.updateMemoryStatus();

                    // 每5秒更新一次内存状态
                    setInterval(() => {
                        this.updateMemoryStatus();
                    }, 5000);
                }
            }

            updateMemoryStatus() {
                if (performance.memory) {
                    const used = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                    const total = (performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(1);
                    const limit = (performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(1);
                    const available = (limit - used).toFixed(1);

                    const usagePercent = ((used / limit) * 100).toFixed(1);

                    let statusColor = '#0c5460';
                    if (usagePercent > 80) {
                        statusColor = '#721c24';
                    } else if (usagePercent > 60) {
                        statusColor = '#856404';
                    }

                    this.memoryStatus.innerHTML = `
                        💾 内存使用: ${used}MB / ${limit}MB (${usagePercent}%) | 可用: ${available}MB
                    `;
                    this.memoryStatus.style.color = statusColor;
                }
            }

            showStatus(message, type) {
                this.status.innerHTML = `<div class="status ${type}">${message}</div>`;
                setTimeout(() => {
                    this.status.innerHTML = '';
                }, 5000);
            }
        }

        const decryptor = new AESImageDecryptor();
    </script>
</body>
</html>
