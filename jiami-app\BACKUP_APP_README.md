# 备份助手 - UniApp版本

一个基于UniApp开发的照片视频文件备份应用，使用WebView套装HTML5页面实现美观的媒体文件展示。

## 🚀 项目特点

- **简单精炼**：代码结构清晰，易于维护和扩展
- **性能优异**：使用WebView加载本地HTML5页面，响应速度快
- **美观界面**：基于HTML5/CSS3实现的现代化UI设计
- **跨平台**：支持iOS、Android等多个平台

## 📁 项目结构

```
├── manifest.json          # 应用配置文件
├── pages.json             # 页面路由配置
├── main.js               # 应用入口文件
├── App.vue               # 应用根组件
├── pages/
│   └── index/
│       └── index.vue     # 主页面（包含WebView）
└── static/
    ├── html/
    │   └── gallery.html  # HTML5展示页面
    └── images/           # 图片资源目录
```

## 🛠️ 核心功能

### 1. WebView集成
- 使用uni-app的web-view组件加载本地HTML页面
- 实现uni-app与HTML5页面的双向通信
- 支持数据传递和事件处理

### 2. 媒体文件展示
- 响应式网格布局展示照片和视频
- 支持照片/视频分类切换
- 显示文件基本信息（名称、大小、日期等）

### 3. 交互功能
- 点击文件触发相应事件
- 统计信息实时更新
- 加载状态和空状态处理

## 🔧 技术栈

- **前端框架**：UniApp (Vue 3)
- **UI实现**：HTML5 + CSS3 + JavaScript
- **通信方式**：WebView postMessage
- **样式方案**：原生CSS + Flexbox/Grid

## 📱 使用方法

1. **开发环境**：
   ```bash
   # 使用HBuilderX打开项目
   # 或使用uni-app CLI
   npm install -g @vue/cli @vue/cli-init
   vue init dcloudio/uni-preset-vue my-backup-app
   ```

2. **运行项目**：
   - 在HBuilderX中选择运行到相应平台
   - 或使用命令行：`npm run dev:app-plus`

3. **自定义数据**：
   - 修改 `pages/index/index.vue` 中的 `mockData`
   - 实现真实的文件读取逻辑

## 🎨 界面特色

- **渐变背景**：现代化的紫蓝色渐变设计
- **卡片布局**：圆角卡片展示文件信息
- **响应式设计**：适配不同屏幕尺寸
- **流畅动画**：悬停和点击效果
- **统计面板**：实时显示文件数量

## 🔄 通信机制

### uni-app → HTML5
```javascript
// 通过evalJS向HTML5页面发送数据
webview.evalJS(`
    if (window.updateGalleryData) {
        window.updateGalleryData(${JSON.stringify(data)});
    }
`)
```

### HTML5 → uni-app
```javascript
// 通过postMessage向uni-app发送消息
uni.postMessage({
    data: {
        type: 'imageClick',
        payload: imageData
    }
});
```

## 📝 扩展建议

1. **文件管理**：
   - 集成文件系统API读取真实文件
   - 实现文件上传/下载功能
   - 添加文件搜索和筛选

2. **媒体预览**：
   - 集成图片预览组件
   - 添加视频播放功能
   - 支持缩略图生成

3. **云端备份**：
   - 集成云存储服务
   - 实现自动备份功能
   - 添加同步状态显示

4. **用户体验**：
   - 添加下拉刷新
   - 实现无限滚动加载
   - 优化加载性能

## 📄 许可证

MIT License - 可自由使用和修改

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
