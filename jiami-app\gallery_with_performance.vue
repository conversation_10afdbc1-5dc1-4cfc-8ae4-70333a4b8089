<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="initialLoading" class="loading">
			<view class="spinner"></view>
			<text>正在扫描本机相册...</text>
			<text class="loading-tip">首次加载可能需要较长时间</text>
			<!-- 调试按钮 -->
			<view class="debug-actions">
				<button @click="skipToMockData" class="debug-btn">跳过使用模拟数据</button>
				<button @click="loadQuickMode" class="debug-btn quick-btn">快速加载模式</button>
			</view>
		</view>

		<!-- 高性能相册列表 - 支持下拉加载，优化滚动性能 -->
		<scroll-view v-else class="gallery-scroll" scroll-y="true" @scrolltolower="loadMoreImages" :style="{ height: containerHeight + 'px' }" :lower-threshold="150" :throttle="false" :scroll-with-animation="false">
			<!-- 按日期分组显示 -->
			<view v-for="(group, date) in groupedMedia" :key="date" class="date-group">
				<!-- 图片网格 -->
				<view class="media-grid">
					<view
						v-for="item in group"
						:key="item.id"
						class="media-item"
						@click="handleItemClick(item)"
					>
						<image
							:src="item.url"
							class="media-image"
							mode="aspectFill"
							:lazy-load="true"
							@load="handleImageLoad(item)"
							@error="handleImageError(item)"
						/>
						
						<!-- 视频标识 -->
						<view v-if="item.type === 'video'" class="video-overlay">
							<view class="video-duration">{{ item.duration }}</view>
							<view class="video-play-icon">▶️</view>
						</view>
						
						<!-- 加载状态 -->
						<view v-if="item.loading" class="loading-overlay">
							<view class="mini-spinner"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 高性能下拉加载更多状态 -->
			<view v-if="hasMoreData && allMediaItems.length > 0" class="load-more">
				<view v-if="loadingMore" class="loading-more">
					<view class="mini-spinner"></view>
					<text class="loading-text">加载更多图片...</text>
				</view>
				<text v-else class="load-more-text">下拉加载更多</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMoreData && allMediaItems.length > 0" class="no-more">
				<text class="no-more-text">已加载全部图片</text>
			</view>

			<!-- 空状态 -->
			<view v-if="allMediaItems.length === 0" class="empty-state">
				<text class="empty-icon">🖼️</text>
				<text class="empty-text">暂无图片文件</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: 'Gallery',
	data() {
		return {
			// 基础数据
			allMediaItems: [],
			totalImages: 0,
			initialLoading: true,
			containerHeight: 0,
			imageCache: new Map(),
			
			// 🚀 高性能下拉加载控制 - 优化批量大小
			loadBatchSize: 30, // 减少批量大小，提升响应速度
			loadOffset: 0,
			hasMoreData: true,
			loadingMore: false,
			loadMoreTimer: null,
			
			// 性能优化
			backgroundLoadTimer: null,
			isScrolling: false,
			scrollTimer: null
		}
	},
	computed: {
		groupedMedia() {
			if (!this.allMediaItems || this.allMediaItems.length === 0) {
				return {}
			}

			const groups = {}
			this.allMediaItems.forEach(item => {
				const dateKey = item.date || '未知日期'
				if (!groups[dateKey]) {
					groups[dateKey] = []
				}
				groups[dateKey].push(item)
			})

			const sortedGroups = {}
			Object.keys(groups)
				.sort((a, b) => {
					if (a === '未知日期') return 1
					if (b === '未知日期') return -1
					return new Date(b) - new Date(a)
				})
				.forEach(key => {
					sortedGroups[key] = groups[key]
				})

			return sortedGroups
		}
	},
	onLoad() {
		console.log('🚀 高性能相册页面加载')
		this.initializeGallery()
	},
	onUnload() {
		this.cleanup()
	},
	methods: {
		async initializeGallery() {
			console.log('开始初始化高性能相册...')

			try {
				this.initializeContainer()
				await this.loadAllMediaData()
				this.initialLoading = false



			} catch (error) {
				console.error('❌ 相册初始化失败:', error)
				this.initialLoading = false

				uni.showModal({
					title: '初始化失败',
					content: `相册初始化失败: ${error.message || '未知错误'}`,
					showCancel: false,
					confirmText: '重试',
					success: (res) => {
						if (res.confirm) {
							this.initialLoading = true
							setTimeout(() => {
								this.initializeGallery()
							}, 1000)
						}
					}
				})
			}
		},

		initializeContainer() {
			const systemInfo = uni.getSystemInfoSync()
			this.containerHeight = systemInfo.windowHeight
			console.log('容器高度设置为:', this.containerHeight + 'px')
		},

		// 🚀 支持分批加载的数据加载方法
		async loadAllMediaData() {
			console.log('开始加载媒体数据...')

			try {
				const hasPermission = await this.requestPermission()
				if (!hasPermission) {
					throw new Error('相册权限被拒绝')
				}

				// 加载第一批图片数据
				const firstBatch = await this.loadBatchImages(0, this.loadBatchSize)
				this.allMediaItems = firstBatch || []
				this.totalImages = this.allMediaItems.length
				this.loadOffset = this.loadBatchSize
				
				// 检查是否还有更多数据
				this.hasMoreData = firstBatch.length >= this.loadBatchSize

				console.log(`✅ 首批媒体数据加载完成: ${this.totalImages}张图片, 还有更多: ${this.hasMoreData}`)

			} catch (error) {
				console.error('❌ 加载媒体数据失败:', error)
				this.allMediaItems = []
				this.totalImages = 0
				this.hasMoreData = false
				throw error
			}
		},

		// 🚀 高性能下拉加载功能
		
		// 加载更多图片（防抖优化）
		loadMoreImages() {
			// 防抖处理，避免频繁触发
			if (this.loadMoreTimer) {
				clearTimeout(this.loadMoreTimer)
			}
			
			this.loadMoreTimer = setTimeout(() => {
				this.doLoadMoreImages()
			}, 300) // 300ms防抖
		},
		
		async doLoadMoreImages() {
			if (this.loadingMore || !this.hasMoreData) {
				console.log('⚠️ 正在加载或没有更多数据')
				return
			}
			
			this.loadingMore = true
			console.log(`📱 开始加载更多图片, offset: ${this.loadOffset}`)
			
			try {
				const moreBatch = await this.loadBatchImages(this.loadOffset, this.loadBatchSize)
				
				if (moreBatch && moreBatch.length > 0) {
					// 使用nextTick优化DOM更新
					this.$nextTick(() => {
						// 添加到现有数据
						this.allMediaItems = [...this.allMediaItems, ...moreBatch]
						this.totalImages = this.allMediaItems.length
						this.loadOffset += moreBatch.length
						
						// 检查是否还有更多数据
						this.hasMoreData = moreBatch.length >= this.loadBatchSize
						
						console.log(`✅ 加载更多完成: 新增${moreBatch.length}张, 总计${this.totalImages}张, 还有更多: ${this.hasMoreData}`)
					})
				} else {
					this.hasMoreData = false
					console.log('⚠️ 没有更多图片了')
					

				}
				
			} catch (error) {
				console.error('❌ 加载更多图片失败:', error)
				this.hasMoreData = false
				
				uni.showToast({
					title: '加载失败，请重试',
					icon: 'error'
				})
			} finally {
				this.loadingMore = false
			}
		},
		
		async loadBatchImages(offset = 0, limit = 50) {
			// #ifdef APP-PLUS
			console.log(`App端: 开始分批查询MediaStore, offset: ${offset}, limit: ${limit}`)
			return new Promise((resolve) => {
				try {
					const main = plus.android.runtimeMainActivity()
					const ContentResolver = plus.android.importClass('android.content.ContentResolver')
					const MediaStore = plus.android.importClass('android.provider.MediaStore')
					const ContentUris = plus.android.importClass('android.content.ContentUris')

					const resolver = main.getContentResolver()
					const uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI

					// 分批查询图片
					this.queryBatchMediaStore(resolver, uri, 'image', offset, limit).then((images) => {
						console.log(`✅ MediaStore分批查询成功: ${images.length}张图片`)
						resolve(images)
					}).catch((error) => {
						console.error('❌ MediaStore查询失败:', error)
						resolve([])
					})

				} catch (error) {
					console.error('❌ 加载图片异常:', error)
					resolve([])
				}
			})
			// #endif

			// #ifndef APP-PLUS
			// 非App端返回模拟数据
			console.log(`非App端: 生成分批模拟数据, offset: ${offset}, limit: ${limit}`)
			try {
				const allMockImages = this.generateAllMockImages(500) // 生成500张模拟图片
				const batchImages = allMockImages.slice(offset, offset + limit)
				console.log(`✅ 模拟图片分批生成成功: ${batchImages.length}张`)
				return Promise.resolve(batchImages)
			} catch (error) {
				console.error('❌ 生成模拟数据失败:', error)
				return Promise.resolve([])
			}
			// #endif
		},
