#!/usr/bin/env python3
"""
视频处理程序 - 监控temp目录中的MP4文件并进行处理
支持缩略图生成、AES128-GCM加密、M3U8分片等功能
兼容Windows和Linux系统
"""

import os
import sys
import time
import hashlib
import secrets
import subprocess
import logging
import shutil
import platform
from pathlib import Path
from typing import Optional, Tuple
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from datetime import datetime

# 添加aes目录到Python路径以导入数据库队列
current_file = Path(__file__).resolve()
aes_path = str(current_file.parent.parent / 'aes')
if aes_path not in sys.path:
    sys.path.insert(0, aes_path)

try:
    from database_queue import get_database_queue
    DATABASE_QUEUE_AVAILABLE = True
    print("[OK] 数据库队列模块导入成功")
except ImportError as e:
    print(f"警告: 无法导入数据库队列模块，视频信息将不会保存到数据库: {e}")
    DATABASE_QUEUE_AVAILABLE = False

# 导入配置
try:
    from config import *
except ImportError:
    # 如果没有配置文件，使用默认值
    TEMP_DIR = "temp"
    VIDEO_DIR = "video"
    MONITOR_INTERVAL = 2
    MAX_FILE_SIZE = 120 * 1024 * 1024
    CHUNK_SIZE = 10 * 1024 * 1024
    THUMBNAIL_WIDTH = 320
    THUMBNAIL_HEIGHT = 240
    THUMBNAIL_QUALITY = 5
    AES_KEY_SIZE = 16
    GCM_IV_SIZE = 12
    FFMPEG_TIMEOUT = 300
    THUMBNAIL_TIMEOUT = 30
    LOG_LEVEL = "INFO"
    LOG_FILE = "video_processor.log"

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VideoProcessor:
    """视频处理器类"""

    def __init__(self, temp_dir: str = None, video_dir: str = None):
        self.temp_dir = Path(temp_dir or TEMP_DIR)
        self.video_dir = Path(video_dir or VIDEO_DIR)
        self.max_file_size = MAX_FILE_SIZE
        self.chunk_size = CHUNK_SIZE

        # 初始化数据库队列
        self.db_queue = None
        if DATABASE_QUEUE_AVAILABLE:
            try:
                self.db_queue = get_database_queue()
                if not self.db_queue.running:
                    self.db_queue.start()
                logger.info("数据库队列初始化成功")
            except Exception as e:
                logger.warning(f"数据库队列初始化失败: {e}")
                self.db_queue = None

        # 确保目录存在
        self.temp_dir.mkdir(exist_ok=True)
        self.video_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"视频处理器初始化完成 - temp: {self.temp_dir}, video: {self.video_dir}")

    def _save_video_to_database_direct(self, sha1_hash, original_filename, m3u8_path, password, iv,
                                     file_created_date=None, file_size_mb=None, tag="", txt=""):
        """直接保存视频记录到数据库"""
        import sqlite3
        from pathlib import Path

        db_file = Path(__file__).resolve().parent.parent / 'aes' / 'image_encryption.db'

        try:
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()

            # 检查是否已存在
            cursor.execute('SELECT id FROM encrypted_videos WHERE sha1_hash = ?', (sha1_hash,))
            existing = cursor.fetchone()

            if existing:
                # 更新
                cursor.execute('''
                    UPDATE encrypted_videos
                    SET original_filename = ?, m3u8_path = ?, password = ?, iv = ?,
                        file_created_date = ?, file_size_mb = ?, tag = ?, txt = ?,
                        encryption_date = CURRENT_TIMESTAMP
                    WHERE sha1_hash = ?
                ''', (original_filename, m3u8_path, password, iv, file_created_date,
                      file_size_mb, tag, txt, sha1_hash))
                logger.info(f"更新视频数据库记录: {original_filename}")
            else:
                # 插入
                cursor.execute('''
                    INSERT INTO encrypted_videos
                    (sha1_hash, original_filename, m3u8_path, password, iv, file_created_date,
                     file_size_mb, tag, txt)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (sha1_hash, original_filename, m3u8_path, password, iv, file_created_date,
                      file_size_mb, tag, txt))
                logger.info(f"新增视频数据库记录: {original_filename}")

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"直接保存视频记录失败: {e}")
            raise

    def generate_secure_key(self) -> Tuple[bytes, bytes]:
        """生成安全的16字节密钥和IV"""
        key = secrets.token_bytes(AES_KEY_SIZE)
        iv = secrets.token_bytes(GCM_IV_SIZE)
        return key, iv

    def generate_hls_key_iv(self) -> Tuple[bytes, bytes]:
        """为HLS生成16字节密钥和16字节IV（CBC模式）"""
        key = secrets.token_bytes(16)  # AES128需要16字节密钥
        iv = secrets.token_bytes(16)   # CBC模式需要16字节IV
        return key, iv

    def calculate_segment_duration(self, video_path: Path) -> int:
        """计算分片时长以达到约10MB大小"""
        try:
            # 使用ffprobe获取视频信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]

            use_shell = platform.system() == "Windows"
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  timeout=30, shell=use_shell)

            if result.returncode == 0:
                import json
                info = json.loads(result.stdout)

                # 获取总比特率
                format_info = info.get('format', {})
                bitrate = float(format_info.get('bit_rate', 0))

                if bitrate > 0:
                    # 计算达到10MB所需的秒数
                    # 10MB = 10 * 1024 * 1024 * 8 bits
                    target_bits = self.chunk_size * 8
                    duration = int(target_bits / bitrate)

                    # 限制在合理范围内 (10-300秒)
                    duration = max(10, min(300, duration))
                    logger.info(f"计算分片时长: {duration}秒 (比特率: {bitrate/1000:.1f}kbps)")
                    return duration

            # 如果无法获取比特率，使用默认值
            logger.warning("无法获取视频比特率，使用默认分片时长60秒")
            return 60

        except Exception as e:
            logger.error(f"计算分片时长失败: {e}")
            return 60
    
    def get_file_sha1(self, file_path: Path) -> str:
        """计算文件的SHA1哈希值"""
        sha1_hash = hashlib.sha1()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha1_hash.update(chunk)
        return sha1_hash.hexdigest()
    
    def generate_thumbnail(self, video_path: Path, output_path: Path) -> bool:
        """使用ffmpeg生成视频缩略图"""
        try:
            cmd = [
                'ffmpeg', '-i', str(video_path),
                '-vf', f'scale={THUMBNAIL_WIDTH}:{THUMBNAIL_HEIGHT}',
                '-vframes', '1',
                '-q:v', str(THUMBNAIL_QUALITY),
                '-y',
                str(output_path)
            ]

            # 在Windows上使用shell=True
            use_shell = platform.system() == "Windows"
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=THUMBNAIL_TIMEOUT,
                shell=use_shell
            )
            
            if result.returncode == 0:
                logger.info(f"缩略图生成成功: {output_path}")
                return True
            else:
                logger.error(f"缩略图生成失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"缩略图生成超时: {video_path}")
            return False
        except Exception as e:
            logger.error(f"缩略图生成异常: {e}")
            return False
    
    def encrypt_file_aes_gcm(self, file_path: Path, key: bytes, iv: bytes) -> bool:
        """使用AES128-GCM加密文件"""
        try:
            aesgcm = AESGCM(key)
            encrypted_path = file_path.with_suffix(file_path.suffix + '.enc')
            
            with open(file_path, 'rb') as infile, open(encrypted_path, 'wb') as outfile:
                # 写入IV到文件开头
                outfile.write(iv)
                
                # 分块加密以处理大文件
                chunk_size = 64 * 1024  # 64KB chunks
                while True:
                    chunk = infile.read(chunk_size)
                    if not chunk:
                        break
                    
                    encrypted_chunk = aesgcm.encrypt(iv, chunk, None)
                    outfile.write(encrypted_chunk)
            
            # 删除原文件
            file_path.unlink()
            encrypted_path.rename(file_path)
            
            logger.info(f"文件加密成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件加密失败: {e}")
            return False
    
    def create_m3u8_with_encryption(self, video_path: Path, output_dir: Path,
                                  key: bytes, iv: bytes, custom_key: str = None, custom_iv: str = None) -> Tuple[bool, bytes, bytes]:
        """创建加密的M3U8文件，支持自定义密钥和IV，返回(成功状态, HLS密钥, HLS IV)"""
        try:
            # 使用自定义密钥和IV，或生成新的
            if custom_key and custom_iv:
                # 使用自定义密钥和IV
                hls_key = bytes.fromhex(custom_key)
                hls_iv = bytes.fromhex(custom_iv)
                logger.info("使用自定义密钥和IV进行HLS加密")
            else:
                # 为HLS生成专用的密钥和IV（16字节）
                hls_key, hls_iv = self.generate_hls_key_iv()
                logger.info("使用随机生成的密钥和IV进行HLS加密")

            # 创建临时密钥文件（用于ffmpeg，处理完后删除）
            key_file = output_dir / "temp_hls_key.key"
            with open(key_file, 'wb') as f:
                f.write(hls_key)

            # 计算每个分片的时长以达到约10MB大小
            # 先获取视频比特率来估算分片时长
            segment_duration = self.calculate_segment_duration(video_path)

            # ffmpeg命令生成加密的M3U8
            cmd = [
                'ffmpeg', '-i', str(video_path),
                '-c', 'copy',  # 复制流，不重新编码
                '-f', 'hls',
                '-hls_time', str(segment_duration),  # 动态计算的分片时长
                '-hls_segment_size', str(self.chunk_size),  # 设置分片大小约10MB
                '-hls_key_info_file', str(self.create_key_info_file(output_dir, key_file, hls_iv)),
                '-hls_segment_filename', str(output_dir / 'segment_%03d.ts'),
                '-hls_playlist_type', 'vod',  # 点播类型
                '-hls_flags', 'independent_segments',  # 独立分片
                '-y',
                str(output_dir / 'playlist.m3u8')
            ]

            # 在Windows上使用shell=True
            use_shell = platform.system() == "Windows"
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=FFMPEG_TIMEOUT,
                shell=use_shell
            )

            if result.returncode == 0:
                # 删除原视频文件
                video_path.unlink()

                # 删除临时密钥文件和key_info文件
                try:
                    key_file.unlink()
                    key_info_file = output_dir / "key_info.txt"
                    if key_info_file.exists():
                        key_info_file.unlink()
                except:
                    pass

                # 移除m3u8文件中的密钥信息，只保留分片信息
                self.remove_key_info_from_m3u8(output_dir / 'playlist.m3u8')

                logger.info(f"M3U8加密分片成功: {output_dir}")
                return True, hls_key, hls_iv
            else:
                logger.error(f"M3U8生成失败: {result.stderr}")
                return False, None, None

        except Exception as e:
            logger.error(f"M3U8生成异常: {e}")
            return False, None, None
    
    def create_key_info_file(self, output_dir: Path, key_file: Path, iv: bytes) -> Path:
        """创建ffmpeg所需的密钥信息文件"""
        key_info_file = output_dir / "key_info.txt"

        with open(key_info_file, 'w') as f:
            f.write(f"{key_file.name}\n")  # 密钥文件路径
            f.write(f"{key_file}\n")       # 密钥文件绝对路径
            f.write(f"{iv.hex()}\n")       # IV (十六进制)

        return key_info_file

    def remove_key_info_from_m3u8(self, m3u8_file: Path):
        """从m3u8文件中移除密钥信息，只保留分片信息"""
        try:
            with open(m3u8_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 过滤掉包含密钥信息的行
            filtered_lines = []
            for line in lines:
                # 跳过EXT-X-KEY行（包含密钥信息）
                if not line.strip().startswith('#EXT-X-KEY:'):
                    filtered_lines.append(line)

            # 写回文件
            with open(m3u8_file, 'w', encoding='utf-8') as f:
                f.writelines(filtered_lines)

            logger.info(f"已从M3U8文件中移除密钥信息: {m3u8_file}")

        except Exception as e:
            logger.error(f"移除M3U8密钥信息失败: {e}")

    def get_today_folder(self):
        """获取今天日期的文件夹路径"""
        today = datetime.now().strftime("%Y-%m-%d")
        today_folder = self.video_dir / today
        today_folder.mkdir(exist_ok=True)
        return today_folder

    def process_video_file(self, video_path: Path) -> bool:
        """处理单个视频文件"""
        try:
            logger.info(f"开始处理视频文件: {video_path}")

            # 获取原始文件信息
            original_filename = video_path.name
            file_size = video_path.stat().st_size
            file_size_mb = round(file_size / (1024 * 1024), 2)

            # 获取文件创建日期
            file_created_date = datetime.fromtimestamp(video_path.stat().st_ctime).strftime("%Y-%m-%d %H:%M:%S")

            # 获取今天的日期文件夹
            today_folder = self.get_today_folder()

            # 生成SHA1哈希作为文件夹名，放在日期文件夹下
            sha1_hash = self.get_file_sha1(video_path)
            target_dir = today_folder / sha1_hash
            target_dir.mkdir(exist_ok=True)

            # 生成密钥和IV
            key, iv = self.generate_secure_key()

            # 移动视频文件到目标目录
            video_name = video_path.stem
            video_ext = video_path.suffix
            target_video_path = target_dir / f"{video_name}{video_ext}"
            shutil.move(str(video_path), str(target_video_path))

            # 生成缩略图
            thumbnail_name = f"{video_name}_tagvideo.jpg"
            thumbnail_path = target_dir / thumbnail_name
            thumbnail_generated = self.generate_thumbnail(target_video_path, thumbnail_path)

            # 加密缩略图
            if thumbnail_generated:
                self.encrypt_file_aes_gcm(thumbnail_path, key, iv)

            # 根据文件大小选择处理方式
            hls_key = None
            hls_iv = None
            m3u8_path = None

            if file_size <= self.max_file_size:
                # 小文件直接加密
                logger.info(f"文件大小 {file_size_mb}MB <= 120MB，使用AES加密")
                success = self.encrypt_file_aes_gcm(target_video_path, key, iv)
                # 小文件使用原始密钥和IV
                password = key.hex()
                iv_hex = iv.hex()
            else:
                # 大文件生成M3U8
                logger.info(f"文件大小 {file_size_mb}MB > 120MB，生成M3U8分片")
                success, hls_key, hls_iv = self.create_m3u8_with_encryption(target_video_path, target_dir, key, iv)
                if success and hls_key and hls_iv:
                    # 大文件使用HLS密钥和IV
                    password = hls_key.hex()
                    iv_hex = hls_iv.hex()
                    # 生成m3u8路径（相对于项目根目录）
                    m3u8_path = str(target_dir / 'playlist.m3u8').replace('\\', '/')
                else:
                    success = False

            if success:

                # 保存到数据库队列
                if self.db_queue:
                    try:
                        # 对于小文件，m3u8_path使用加密文件路径
                        if file_size <= self.max_file_size:
                            m3u8_path = str(target_video_path).replace('\\', '/')

                        # 直接写入数据库，绕过队列机制
                        self._save_video_to_database_direct(
                            sha1_hash=sha1_hash,
                            original_filename=original_filename,
                            m3u8_path=m3u8_path,
                            password=password,
                            iv=iv_hex,
                            file_created_date=file_created_date,
                            file_size_mb=file_size_mb,
                            tag="",
                            txt=""
                        )
                        logger.info(f"视频信息已保存到数据库: {original_filename}")
                    except Exception as e:
                        logger.error(f"保存视频信息到数据库失败: {e}")

                logger.info(f"视频文件处理完成: {video_path} -> {target_dir}")
                return True
            else:
                logger.error(f"视频文件处理失败: {video_path}")
                return False

        except Exception as e:
            logger.error(f"处理视频文件异常: {e}")
            return False



    def scan_temp_directory(self) -> list:
        """扫描temp目录中的MP4文件"""
        mp4_files = []
        try:
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file() and file_path.suffix.lower() == '.mp4':
                    mp4_files.append(file_path)
        except Exception as e:
            logger.error(f"扫描目录异常: {e}")

        return mp4_files

    def run_monitor(self):
        """运行监控循环"""
        logger.info("开始监控temp目录...")

        while True:
            try:
                mp4_files = self.scan_temp_directory()

                if mp4_files:
                    logger.info(f"发现 {len(mp4_files)} 个MP4文件")

                    for video_file in mp4_files:
                        self.process_video_file(video_file)

                # 等待指定间隔
                time.sleep(MONITOR_INTERVAL)

            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监控")
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(MONITOR_INTERVAL)


def check_dependencies():
    """检查依赖项"""
    try:
        # 检查ffmpeg - 在Windows上使用shell=True
        use_shell = platform.system() == "Windows"
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True, shell=use_shell)
        logger.info("ffmpeg 检查通过")
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("ffmpeg 未安装或不在PATH中")
        sys.exit(1)

    try:
        # 检查cryptography库
        import cryptography
        logger.info("cryptography 库检查通过")
    except ImportError:
        logger.error("cryptography 库未安装，请运行: pip install cryptography")
        sys.exit(1)


def main():
    """主函数"""
    logger.info("视频处理程序启动")

    # 检查依赖
    check_dependencies()

    # 创建处理器实例
    processor = VideoProcessor()

    # 开始监控
    processor.run_monitor()


if __name__ == "__main__":
    main()
